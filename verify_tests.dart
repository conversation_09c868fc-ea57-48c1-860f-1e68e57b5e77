#!/usr/bin/env dart

/// Simple verification script to check if test files are syntactically correct
/// Run with: dart verify_tests.dart

import 'dart:io';

void main() async {
  print('🔍 Verifying Odoo Sync Test Files');
  print('=' * 50);
  
  final testFiles = [
    'test/helpers/test_helpers.dart',
    'test/mocks/mock_odoo_client.dart',
    'test/sync/basic_test.dart',
    'test/sync/odoo_client_test.dart',
    'test/sync/odoo_mapper_test.dart',
    'test/sync/sync_repository_test.dart',
    'test/sync/integration_test.dart',
    'test/sync/sync_test_suite.dart',
  ];
  
  int passCount = 0;
  int failCount = 0;
  
  for (final testFile in testFiles) {
    await verifyFile(testFile) ? passCount++ : failCount++;
  }
  
  print('\n' + '=' * 50);
  print('📊 Verification Summary:');
  print('✅ Passed: $passCount files');
  print('❌ Failed: $failCount files');
  
  if (failCount == 0) {
    print('\n🎉 All test files are syntactically correct!');
    print('\n📋 Test Execution Instructions:');
    printTestInstructions();
  } else {
    print('\n⚠️  Some files have syntax errors. Please fix them before running tests.');
  }
}

Future<bool> verifyFile(String filePath) async {
  print('\n📄 Checking: $filePath');
  
  final file = File(filePath);
  if (!file.existsSync()) {
    print('❌ File not found: $filePath');
    return false;
  }
  
  try {
    // Try to analyze the file with dart analyze
    final result = await Process.run(
      'dart',
      ['analyze', filePath],
      workingDirectory: Directory.current.path,
    );
    
    if (result.exitCode == 0) {
      print('✅ Syntax OK: $filePath');
      return true;
    } else {
      print('❌ Syntax errors in: $filePath');
      if (result.stdout.toString().isNotEmpty) {
        print('Output: ${result.stdout}');
      }
      if (result.stderr.toString().isNotEmpty) {
        print('Errors: ${result.stderr}');
      }
      return false;
    }
  } catch (e) {
    print('⚠️  Could not analyze $filePath: $e');
    
    // Fallback: just check if file can be read
    try {
      await file.readAsString();
      print('✅ File readable: $filePath');
      return true;
    } catch (e2) {
      print('❌ File not readable: $filePath - $e2');
      return false;
    }
  }
}

void printTestInstructions() {
  print('''
🚀 How to Run the Odoo Sync Tests:

1. Basic Test (recommended first):
   flutter test test/sync/basic_test.dart

2. Individual Test Suites:
   flutter test test/sync/odoo_client_test.dart
   flutter test test/sync/odoo_mapper_test.dart
   flutter test test/sync/sync_repository_test.dart

3. Integration Tests (requires internet):
   flutter test test/sync/integration_test.dart

4. All Sync Tests:
   flutter test test/sync/

5. Complete Test Suite:
   flutter test test/sync/sync_test_suite.dart

📝 Test Configuration:
- Odoo Server: https://erp.kanjan.co.zw
- Database: piggypro
- Username: <EMAIL>
- Password: Secret1234

⚠️  Notes:
- Integration tests require internet connection
- Tests use in-memory database (safe for production)
- Mock tests don't require Odoo server access
- Real server tests validate actual sync functionality

🔧 Troubleshooting:
- If Flutter commands don't work, try: dart test <file>
- Ensure all dependencies are installed: flutter pub get
- Check network connectivity for integration tests
- Verify Odoo server credentials are correct
''');
}

/// Check if required dependencies are available
Future<void> checkDependencies() async {
  print('\n🔧 Checking Dependencies:');
  
  final dependencies = [
    'flutter_test',
    'shared_preferences',
    'drift',
    'mockito',
    'flutter_riverpod',
  ];
  
  // This is a simplified check - in a real scenario you'd parse pubspec.yaml
  print('📦 Required dependencies:');
  for (final dep in dependencies) {
    print('  - $dep');
  }
  
  print('\n💡 To install dependencies: flutter pub get');
}
