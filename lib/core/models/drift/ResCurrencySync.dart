import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/interfaces/DriftSyncClass.dart';

/// Wrapper class that implements DriftSyncClass for ResCurrencyTableData
class ResCurrencySync implements DriftSyncClass {
  final ResCurrencyTableData _currency;

  ResCurrencySync(this._currency);

  // DriftSyncClass properties
  @override
  int get id => _currency.id;

  @override
  int? get universal_id => _currency.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _currency.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _currency.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _currency.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _currency.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _currency.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the currency's JSON data
    Map<String, dynamic> data = _currency.toJson();
    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = this.universal_id;
    data['is_synced'] = this.is_synced ?? false;
    data['synced'] = this.is_synced ?? false;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed;
    data['confirmed'] = this.is_confirmed;
    data['origin_id'] = this.id;

    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = this.id;
    data['is_synced'] = this.is_synced ?? true;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed ?? true;
    data['confirmed'] = this.is_confirmed ?? true;
    data['synced'] = this.is_synced ?? true;
    data['origin_id'] = this.id;

    return data;
  }

  @override
  Future<void> saveSynced(local) async {
    // Note: In a real implementation, we would need to get a ProviderContainer
    // to access the repository. For now, we'll just print a message.
    print('Would save currency with ID: ${_currency.id} as synced');

    // In a real implementation, we would create a companion and save it:
    // final companion = ResCurrencyTableCompanion(
    //   id: Value(_currency.id),
    //   name: Value(_currency.name),
    //   symbol: Value(_currency.symbol),
    //   full_name: Value(_currency.full_name),
    //   rate: Value(_currency.rate),
    //   active: Value(_currency.active),
    //   position: Value(_currency.position),
    //   currency_unit_label: Value(_currency.currency_unit_label),
    //   currency_subunit_label: Value(_currency.currency_subunit_label),
    //   decimal_places: Value(_currency.decimal_places),
    //   rounding: Value(_currency.rounding),
    //   company_id: Value(_currency.company_id),
    //   universal_id: Value(_currency.universal_id),
    //   is_synced: const Value(true),
    //   origin_id: Value(_currency.origin_id),
    //   version: Value(_currency.version),
    //   is_confirmed: Value(_currency.is_confirmed),
    //   is_deleted: Value(_currency.is_deleted),
    // );
    // final container = ProviderContainer();
    // await container.read(resCurrencyRepositoryProvider).save(companion);
    // container.dispose();
  }

  @override
  Future<ResCurrencySync?> getByUni() async {
    if (universal_id == null) return null;

    // Note: In a real implementation, we would need to get a ProviderContainer
    // to access the repository. For now, we'll just return null.
    print('Would get currency with universal ID: $universal_id');
    // In a real implementation, this would be:
    // final container = ProviderContainer();
    // final currency = await container.read(resCurrencyRepositoryProvider).getByuniversal_id(universal_id!);
    // container.dispose();
    // if (currency == null) return null;
    // return ResCurrencySync(currency);

    return null;
  }

  // Additional methods
  ResCurrencyTableData get currency => _currency;
}
