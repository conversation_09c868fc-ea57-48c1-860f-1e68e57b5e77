import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for ProductProductTableData
/// It replaces the old ProductProduct class with direct use of Drift's generated classes

/// Extension methods for ProductProductTableData
extension ProductProductTableDataExtensions on ProductProductTableData {
  /// Check if the product is active (not deleted)
  bool isActive() => !is_deleted && (active == 1 || active == null);

  /// Check if the product can be sold
  bool canBeSold() => sale_ok == 1;

  /// Check if the product can be purchased
  bool canBePurchased() => purchase_ok == 1;

  /// Check if the product is featured
  bool isFeatured() => featured == 1;

  /// Check if the product has stock
  bool hasStock() => stock == 1;

  /// Get the display name (uses name if available, otherwise default_code)
  String getDisplayName() {
    if (name != null && name!.isNotEmpty) {
      return name!;
    }
    if (default_code != null && default_code!.isNotEmpty) {
      return default_code!;
    }
    return 'Product #$id';
  }

  /// Get the formatted list price
  String getFormattedlist_price() {
    if (list_price == null) return '0.00';
    return list_price!.toStringAsFixed(2);
  }

  /// Get the formatted standard price
  String getFormattedStandardPrice() {
    if (standard_price == null) return '0.00';
    return standard_price!.toStringAsFixed(2);
  }

  /// Get the formatted quantity available
  String getFormattedQtyAvailable() {
    if (qty_available == null) return '0';
    return qty_available!.toStringAsFixed(0);
  }

  /// Convert to ProductProductTableCompanion for Drift operations
  ProductProductTableCompanion toCompanion() {
    return ProductProductTableCompanion(
      id: Value(id),
      name: Value(name),
      sequence: Value(sequence),
      description: Value(description),
      description_purchase: Value(description_purchase),
      description_sale: Value(description_sale),
      categ_id: Value(categ_id),
      list_price: Value(list_price),
      standard_price: Value(standard_price),
      volume: Value(volume),
      weight: Value(weight),
      sale_ok: Value(sale_ok),
      purchase_ok: Value(purchase_ok),
      active: Value(active),
      color: Value(color),
      default_code: Value(default_code),
      barcode: Value(barcode),
      company_id: Value(company_id),
      type: Value(type),
      uom_id: Value(uom_id),
      uom_po_id: Value(uom_po_id),
      image_1920: Value(image_1920),
      image_1024: Value(image_1024),
      image_512: Value(image_512),
      image_256: Value(image_256),
      image_128: Value(image_128),
      qty_available: Value(qty_available),
      virtual_available: Value(virtual_available),
      incoming_qty: Value(incoming_qty),
      outgoing_qty: Value(outgoing_qty),
      featured: Value(featured),
      stock: Value(stock),
      universal_id: Value(universal_id),
      is_synced: Value(is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  ProductProductTableData copyWith({
    int? id,
    String? name,
    String? sequence,
    String? description,
    String? description_purchase,
    String? description_sale,
    int? categ_id,
    double? list_price,
    double? standard_price,
    double? volume,
    double? weight,
    int? sale_ok,
    bool? purchase_ok,
    bool? active,
    String? color,
    String? default_code,
    String? barcode,
    int? company_id,
    String? type,
    int? uom_id,
    int? uom_po_id,
    String? image_1920,
    String? image_1024,
    String? image_512,
    String? image_256,
    String? image_128,
    double? qty_available,
    double? virtual_available,
    double? incoming_qty,
    double? outgoing_qty,
    int? featured,
    int? stock,
    String? taxes_id,
    String? supplier_taxes_id,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return ProductProductTableData(
      id: id ?? this.id,
      name: name ?? this.name,
      sequence: sequence ?? this.sequence,
      description: description ?? this.description,
      description_purchase: description_purchase ?? this.description_purchase,
      description_sale: description_sale ?? this.description_sale,
      categ_id: categ_id ?? this.categ_id,
      list_price: list_price ?? this.list_price,
      standard_price: standard_price ?? this.standard_price,
      volume: volume ?? this.volume,
      weight: weight ?? this.weight,
      sale_ok: sale_ok ?? this.sale_ok,
      purchase_ok: purchase_ok ?? this.purchase_ok,
      active: active ?? this.active,
      color: color ?? this.color,
      default_code: default_code ?? this.default_code,
      barcode: barcode ?? this.barcode,
      company_id: company_id ?? this.company_id,
      type: type ?? this.type,
      uom_id: uom_id ?? this.uom_id,
      uom_po_id: uom_po_id ?? this.uom_po_id,
      image_1920: image_1920 ?? this.image_1920,
      image_1024: image_1024 ?? this.image_1024,
      image_512: image_512 ?? this.image_512,
      image_256: image_256 ?? this.image_256,
      image_128: image_128 ?? this.image_128,
      qty_available: qty_available ?? this.qty_available,
      virtual_available: virtual_available ?? this.virtual_available,
      incoming_qty: incoming_qty ?? this.incoming_qty,
      outgoing_qty: outgoing_qty ?? this.outgoing_qty,
      featured: featured ?? this.featured,
      stock: stock ?? this.stock,
      taxes_id: taxes_id ?? this.taxes_id,
      supplier_taxes_id: supplier_taxes_id ?? this.supplier_taxes_id,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }

  /// Get customer tax IDs as a list
  List<int> getCustomerTaxIds() {
    if (taxes_id == null || taxes_id!.isEmpty) return [];
    try {
      final List<dynamic> taxIds = json.decode(taxes_id!);
      return taxIds.map((id) => id as int).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get supplier tax IDs as a list
  List<int> getSupplierTaxIds() {
    if (supplier_taxes_id == null || supplier_taxes_id!.isEmpty) return [];
    try {
      final List<dynamic> taxIds = json.decode(supplier_taxes_id!);
      return taxIds.map((id) => id as int).toList();
    } catch (e) {
      return [];
    }
  }
}

/// Extension methods for ProductProductTableCompanion
extension ProductProductTableCompanionExtensions on ProductProductTableCompanion {
  /// Create a companion for a new product
  static ProductProductTableCompanion createProduct({
    required String name,
    int? categ_id,
    double? list_price = 0.0,
    double? standard_price = 0.0,
    String? default_code,
    int? company_id,
    int? sale_ok = 1,
    bool? purchase_ok = true,
    bool? active = true,
  }) {
    return ProductProductTableCompanion.insert(
      name: Value(name),
      categ_id: Value(categ_id),
      list_price: Value(list_price),
      standard_price: Value(standard_price),
      default_code: Value(default_code),
      company_id: Value(company_id),
      sale_ok: Value(sale_ok),
      purchase_ok: Value(purchase_ok),
      active: Value(active),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Mark a product as deleted
  ProductProductTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark a product as active
  ProductProductTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
      active: const Value(true),
    );
  }

  /// Mark a product as synced
  ProductProductTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }

  /// Mark a product as featured
  ProductProductTableCompanion markAsFeatured() {
    return copyWith(
      featured: const Value(1),
    );
  }

  /// Mark a product as not featured
  ProductProductTableCompanion markAsNotFeatured() {
    return copyWith(
      featured: const Value(0),
    );
  }

  /// Set customer tax IDs from a list
  ProductProductTableCompanion setCustomerTaxIds(List<int> taxIds) {
    return copyWith(
      taxes_id: Value(json.encode(taxIds)),
    );
  }

  /// Set supplier tax IDs from a list
  ProductProductTableCompanion setSupplierTaxIds(List<int> taxIds) {
    return copyWith(
      supplier_taxes_id: Value(json.encode(taxIds)),
    );
  }
}

/// Model class for a product with its related data
class ProductProductWithRelations {
  final ProductProductTableData product;
  final ProductCategoryTableData? category;
  final ResCompanyTableData? company;

  ProductProductWithRelations({
    required this.product,
    this.category,
    this.company,
  });
}
