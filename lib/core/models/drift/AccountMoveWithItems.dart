import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveExtensions.dart';
import 'package:invoicer/core/models/drift/AccountMoveLineExtensions.dart';
import 'package:invoicer/core/models/drift/AccountPaymentExtensions.dart';

/// A class that extends AccountMoveTableData to include invoice items and payments
class AccountMoveWithItems {
  final AccountMoveTableData invoice;
  final List<AccountMoveLineTableData> items;
  final List<AccountPaymentTableData> payments;

  AccountMoveWithItems({
    required this.invoice,
    required this.items,
    this.payments = const [],
  });

  /// Calculate the total amount before tax and discounts
  double get totalBeforeTaxAndDiscounts {
    if (items.isEmpty) return 0.0;

    // Sum up the raw total (quantity * price_unit) for all items
    return items.fold(0.0, (sum, item) {
      if (item.quantity == null || item.price_unit == null) return sum;
      return sum + (item.quantity! * item.price_unit!);
    });
  }

  double get totalAfterTaxAndDiscounts {
    // Sum up the raw total (quantity * price_unit) for all items
    return totalBeforeTaxAndDiscounts + ((invoice.amount_tax??0.0));
  }

  /// Calculate the total payments
  double get totalPayments {
    if (payments.isEmpty) return 0.0;

    // Sum up all payments
    return payments.fold(0.0, (sum, payment) {
      return sum + (payment.amount ?? 0.0);
    });
  }

  /// Calculate the remaining balance
  double get remainingBalance {
    return totalAfterTaxAndDiscounts - totalPayments;
  }

  /// Check if the invoice is fully paid
  bool get isFullyPaid {
    return remainingBalance <= 0.0;
  }

  /// Get the total amount before tax and discounts as a formatted string
  String get formattedTotalBeforeTaxAndDiscounts {
    final total = totalBeforeTaxAndDiscounts;
    return total.toStringAsFixed(2);
  }

  /// Get the total amount before tax and discounts with currency symbol
  String get formattedCurrencyTotalBeforeTaxAndDiscounts {
    final total = totalBeforeTaxAndDiscounts;
    if (invoice.currency_symbol == null) return total.toStringAsFixed(2);
    return '${invoice.currency_symbol} ${total.toStringAsFixed(2)}';
  }

  /// Get the total payments as a formatted string
  String get formattedTotalPayments {
    final total = totalPayments;
    return total.toStringAsFixed(2);
  }

  /// Get the total payments with currency symbol
  String get formattedCurrencyTotalPayments {
    final total = totalPayments;
    if (invoice.currency_symbol == null) return total.toStringAsFixed(2);
    return '${invoice.currency_symbol} ${total.toStringAsFixed(2)}';
  }

  /// Get the remaining balance as a formatted string
  String get formattedRemainingBalance {
    final balance = remainingBalance;
    return balance.toStringAsFixed(2);
  }

  /// Get the remaining balance with currency symbol
  String get formattedCurrencyRemainingBalance {
    final balance = remainingBalance;
    if (invoice.currency_symbol == null) return balance.toStringAsFixed(2);
    return '${invoice.currency_symbol} ${balance.toStringAsFixed(2)}';
  }

  /// Forward properties from the invoice
  int get id => invoice.id;
  String? get name => invoice.name;
  String? get move_type => invoice.move_type;
  String? get state => invoice.state;
  int? get partner_id => invoice.partner_id;
  String? get invoice_date => invoice.invoice_date;
  String? get invoice_date_due => invoice.invoice_date_due;
  String? get date => invoice.date;
  String? get narration => invoice.narration;
  int? get currency_id => invoice.currency_id;
  String? get currency_symbol => invoice.currency_symbol;
  double? get amount_untaxed => invoice.amount_untaxed;
  double? get amount_tax => invoice.amount_tax;
  double? get amount_total => invoice.amount_total;
  double? get amount_residual => invoice.amount_residual;
  String? get payment_state => invoice.payment_state;
  int? get company_id => invoice.company_id;
  bool get is_order => invoice.is_order;
  bool get is_deleted => invoice.is_deleted;
  bool get is_synced => invoice.is_synced;


  /// Forward methods from the invoice
  bool isActive() => invoice.isActive();
  bool isDraft() => invoice.isDraft();
  bool isPosted() => invoice.isPosted();
  bool isCancelled() => invoice.isCancelled();
  bool isPaid() => invoice.isPaid();
  bool isInvoice() => invoice.isInvoice();
  bool isBill() => invoice.isBill();
  bool isCreditNote() => invoice.isCreditNote();
  bool isVendorCreditNote() => invoice.isVendorCreditNote();
  String getFormattedinvoice_date() => invoice.getFormattedinvoice_date();
  String getFormattedDueDate() => invoice.getFormattedDueDate();
  String getFormattedAmount() => invoice.getFormattedAmount();
  String getFormattedCurrencyAmount() => invoice.getFormattedCurrencyAmount();
  String getStatusDisplayText() => invoice.getStatusDisplayText();
  String getTypeDisplayText() => invoice.getTypeDisplayText();

  /// Create a copy with updated fields
  AccountMoveWithItems copyWith({
    AccountMoveTableData? invoice,
    List<AccountMoveLineTableData>? items,
    List<AccountPaymentTableData>? payments,
  }) {
    return AccountMoveWithItems(
      invoice: invoice ?? this.invoice,
      items: items ?? this.items,
      payments: payments ?? this.payments,
    );
  }
}
