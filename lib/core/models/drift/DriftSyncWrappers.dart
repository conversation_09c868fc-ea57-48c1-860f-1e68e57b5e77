import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_service.dart';
import 'package:invoicer/core/models/interfaces/DriftSyncClass.dart';
import 'package:invoicer/core/network/odoo_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/UserPreference.dart';

/// Utility function to map local company_id to Odoo company_id (universal_id)
Future<int?> mapLocalCompanyIdToOdoo(int localCompanyId) async {
  final db = DatabaseService().database;
  final company = await db.resCompanyDao.getCompanyById(localCompanyId);

  if (company != null && company.universal_id != null) {
    print("Mapped local company_id $localCompanyId to Odoo company_id ${company.universal_id}");
    return company.universal_id;
  } else {
    print("WARNING: Could not find universal_id for company with local ID $localCompanyId");
    return null;
  }
}

/// Utility function to map local currency_id to Odoo currency_id (universal_id)
/// With the new sync approach, local ID = universal_id for synced records,
/// so this function primarily validates that the currency exists and is synced
Future<int?> mapLocalCurrencyIdToOdoo(int localCurrencyId) async {
  final db = DatabaseService().database;
  final currency = await db.resCurrencyDao.getCurrencyById(localCurrencyId);

  if (currency != null && currency.is_synced && currency.universal_id != null) {
    // For synced currencies, local ID should equal universal_id
    if (currency.id == currency.universal_id) {
      print("SUCCESS: Currency $localCurrencyId is synced (local ID = universal_id = ${currency.universal_id})");
      return currency.universal_id;
    } else {
      print("INFO: Currency $localCurrencyId is synced but has different local/universal IDs (${currency.id}/${currency.universal_id})");
      return currency.universal_id;
    }
  } else if (currency != null && !currency.is_synced) {
    print("WARNING: Currency $localCurrencyId exists locally but is not synced to Odoo");
    return null;
  } else {
    print("WARNING: Could not find currency with local ID $localCurrencyId");
    return null;
  }
}

/// Wrapper class for AccountTaxTableData that implements DriftSyncClass
class AccountTaxSync implements DriftSyncClass {
  final AccountTaxTableData _tax;

  AccountTaxSync(this._tax);

  // DriftSyncClass properties
  @override
  int get id => _tax.id;

  @override
  int? get universal_id => _tax.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _tax.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _tax.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _tax.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _tax.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _tax.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  Future<dynamic> getByUni() async {
    // Implementation to get tax by universal_id
    final db = DatabaseService().database;
    return await db.accountTaxDao.getTaxById(_tax.id);
  }

  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = this.universal_id;
    data['is_synced'] = this.is_synced ?? false;
    data['synced'] = this.is_synced ?? false;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed;
    data['confirmed'] = this.is_confirmed;
    data['origin_id'] = this.id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = this.id;
    data['is_synced'] = this.is_synced ?? true;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed ?? true;
    data['confirmed'] = this.is_confirmed ?? true;
    data['synced'] = this.is_synced ?? true;
    data['origin_id'] = this.id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the tax's JSON data
    Map<String, dynamic> data = _tax.toJson();

    // Remove fields that are not valid in Odoo or are app-specific
    data.remove('id');
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('origin_id');
    data.remove('version');
    data.remove('is_confirmed');
    data.remove('is_deleted');

    // Map company_id to Odoo's universal_id if present
    if (_tax.company_id != null) {
      final odooCompanyId = await mapLocalCompanyIdToOdoo(_tax.company_id!);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      } else {
        data.remove('company_id');
      }
    }

    // Handle JSON fields for repartition lines and children taxes
    if (_tax.invoice_repartition_line_ids != null && _tax.invoice_repartition_line_ids!.isNotEmpty) {
      try {
        data['invoice_repartition_line_ids'] = json.decode(_tax.invoice_repartition_line_ids!);
      } catch (e) {
        data.remove('invoice_repartition_line_ids');
      }
    }

    if (_tax.refund_repartition_line_ids != null && _tax.refund_repartition_line_ids!.isNotEmpty) {
      try {
        data['refund_repartition_line_ids'] = json.decode(_tax.refund_repartition_line_ids!);
      } catch (e) {
        data.remove('refund_repartition_line_ids');
      }
    }

    if (_tax.children_tax_ids != null && _tax.children_tax_ids!.isNotEmpty) {
      try {
        data['children_tax_ids'] = json.decode(_tax.children_tax_ids!);
      } catch (e) {
        data.remove('children_tax_ids');
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // Get the Odoo ID - either from localData or from the universal_id property
    int odooId;
    if (localData == null) {
      // We just created a new record in Odoo, universal_id should be the Odoo ID
      // But since the setter doesn't work, universal_id might still be null
      odooId = universal_id ?? 0; // This shouldn't happen, but handle gracefully
    } else if (localData is int) {
      // We just created a new record in Odoo, localData is the Odoo ID
      odooId = localData;
    } else if (localData is DriftSyncClass) {
      // We're updating an existing record, use the universal_id from the existing record
      odooId = localData.universal_id ?? universal_id ?? 0;
    } else {
      // Fallback case
      odooId = universal_id ?? 0;
    }

    // Mark the tax as synced with the universal_id from Odoo
    await db.accountTaxDao.markTaxAsSynced(id, odooId);
  }

  String get syncModel => 'account.tax';

  String get syncMethod => 'create';

  String get syncAction => 'create';
}

/// Wrapper class for ResCompanyTableData that implements DriftSyncClass
class ResCompanySync implements DriftSyncClass {
  final ResCompanyTableData _company;

  ResCompanySync(this._company);

  // DriftSyncClass properties
  @override
  int get id => _company.id;

  @override
  int? get universal_id => _company.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _company.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _company.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _company.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _company.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _company.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    // Set the ID field for Odoo
    data['id'] = universal_id;

    // Remove app-specific fields that shouldn't be sent to Odoo
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('synced');
    data.remove('version');
    data.remove('is_deleted');
    data.remove('is_confirmed');
    data.remove('confirmed');
    data.remove('origin_id');

    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the company's JSON data
    Map<String, dynamic> data = _company.toJson();

    // Explicitly remove fields that are not valid in Odoo
    data.remove('favicon');
    data.remove('prefix');
    data.remove('last_used_id');
    data.remove('payment_info');
    data.remove('logo_web');

    // Remove app-specific fields
    data.remove('currency_symbol');
    data.remove('quick_edit_mode');
    data.remove('tax_rate');
    data.remove('is_order');

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // When syncing FROM Odoo, use the Odoo ID as the local ID to eliminate mapping issues
    // This ensures that local ID = universal_id, making currency references work seamlessly
    int localId = _company.id;
    if (universal_id != null && localData == null) {
      // This is a new record from Odoo - use the Odoo ID as local ID
      localId = universal_id!;
    }

    // Create a companion with the synced status
    final companion = ResCompanyTableCompanion(
      id: Value(localId),
      name: Value(_company.name),
      street: Value(_company.street),
      street2: Value(_company.street2),
      city: Value(_company.city),
      zip: Value(_company.zip),
      country_id: Value(_company.country_id),
      state_id: Value(_company.state_id),
      phone: Value(_company.phone),
      email: Value(_company.email),
      website: Value(_company.website),
      vat: Value(_company.vat),
      company_registry: Value(_company.company_registry),
      currency_id: Value(_company.currency_id),
      partner_id: Value(_company.partner_id),
      parent_id: Value(_company.parent_id),
      sequence: Value(_company.sequence),
      // favicon: Value(_company.favicon),
      logo: Value(_company.logo),
      logo_web: Value(_company.logo_web),
      color: Value(_company.color),
      prefix: Value(_company.prefix),
      last_used_id: Value(_company.last_used_id),
      payment_info: Value(_company.payment_info),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_company.origin_id),
      version: Value(_company.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_company.is_deleted),
    );

    // Save to database
    await db.resCompanyDao.insertOrUpdateCompany(companion);
    print('Saved company with ID: ${_company.id} as synced');

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(UserPreference.activeBusiness, _company.id);


  }

  @override
  Future<ResCompanySync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final company = await db.resCompanyDao.getCompanyByuniversal_id(universal_id!);

    if (company != null) {
      return ResCompanySync(company);
    }

    return null;
  }

  // Additional methods
  ResCompanyTableData get company => _company;
}

/// Wrapper class for ResPartnerTableData that implements DriftSyncClass
class ResPartnerSync implements DriftSyncClass {
  final ResPartnerTableData _partner;

  ResPartnerSync(this._partner);

  // DriftSyncClass properties
  @override
  int get id => _partner.id;

  @override
  int? get universal_id => _partner.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _partner.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _partner.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _partner.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _partner.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _partner.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    // Set the ID field for Odoo
    data['id'] = universal_id;

    // Remove app-specific fields that shouldn't be sent to Odoo
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('synced');
    data.remove('version');
    data.remove('is_deleted');
    data.remove('is_confirmed');
    data.remove('confirmed');
    data.remove('origin_id');

    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Validate required fields before syncing
    if (_partner.name == null || _partner.name!.trim().isEmpty) {
      throw Exception("Cannot sync partner with ID ${_partner.id}: name is required");
    }

    // Start with the partner's JSON data
    Map<String, dynamic> data = _partner.toJson();

    // Clean and validate data for Odoo compatibility
    _cleanDataForOdoo(data);

    // Log a warning if company_id is missing
    if (data['company_id'] == null) {
      print("WARNING: Partner with ID ${_partner.id} has no company_id");
      // We're not manually setting company_id to preserve the original data
    } else {
      // Map local company_id to Odoo company_id (universal_id)
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      } else {
        print("WARNING: Could not map company_id $localCompanyId to Odoo, removing from sync data");
        data.remove('company_id');
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  /// Clean and validate data for Odoo compatibility
  void _cleanDataForOdoo(Map<String, dynamic> data) {
    // Remove fields that shouldn't be synced to Odoo
    data.remove('id'); // Will be set by toSyncVars
    data.remove('is_synced');
    data.remove('is_confirmed');
    data.remove('is_deleted');
    data.remove('version');
    data.remove('origin_id');

    // Remove app-specific fields that don't exist in Odoo's res.partner model
    data.remove('currency'); // This field exists in our local model but not in Odoo
    data.remove('status'); // This field exists in our local model but not in Odoo

    // Clean string fields - remove null values and trim whitespace
    final stringFields = ['name', 'street', 'street2', 'city', 'zip', 'phone', 'mobile', 'email', 'website', 'function', 'vat', 'comment'];
    for (final field in stringFields) {
      if (data[field] != null) {
        final value = data[field].toString().trim();
        if (value.isEmpty) {
          data.remove(field);
        } else {
          data[field] = value;
        }
      } else {
        data.remove(field);
      }
    }

    // Validate email format if present
    if (data['email'] != null) {
      final email = data['email'].toString();
      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email)) {
        print("WARNING: Invalid email format for partner ${_partner.id}: $email, removing from sync");
        data.remove('email');
      }
    }

    // Ensure boolean fields are properly set
    data['is_company'] = _partner.is_company ?? false;
    data['active'] = _partner.active ?? true;

    // Set customer_rank and supplier_rank with defaults
    data['customer_rank'] = _partner.customer_rank ?? 1; // Default to customer
    data['supplier_rank'] = _partner.supplier_rank ?? 0;

    print("Cleaned partner data for Odoo sync: ${data.keys.join(', ')}");
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // When syncing FROM Odoo, use the Odoo ID as the local ID to eliminate mapping issues
    // This ensures that local ID = universal_id, making partner references work seamlessly
    int localId = _partner.id;
    if (universal_id != null && localData == null) {
      // This is a new record from Odoo - use the Odoo ID as local ID
      localId = universal_id!;
    }

    // Create a companion with the synced status
    final companion = ResPartnerTableCompanion(
      id: Value(localId),
      name: Value(_partner.name),
      street: Value(_partner.street),
      street2: Value(_partner.street2),
      city: Value(_partner.city),
      zip: Value(_partner.zip),
      country_id: Value(_partner.country_id),
      state_id: Value(_partner.state_id),
      phone: Value(_partner.phone),
      mobile: Value(_partner.mobile),
      email: Value(_partner.email),
      website: Value(_partner.website),
      function: Value(_partner.function),
      title: Value(_partner.title),
      company_id: Value(_partner.company_id),
      parent_id: Value(_partner.parent_id),
      is_company: Value(_partner.is_company),
      type: Value(_partner.type),
      vat: Value(_partner.vat),
      comment: Value(_partner.comment),
      active: Value(_partner.active),
      customer_rank: Value(_partner.customer_rank),
      supplier_rank: Value(_partner.supplier_rank),
      user_id: Value(_partner.user_id),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_partner.origin_id),
      version: Value(_partner.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_partner.is_deleted),
    );

    // Save to database
    await db.resPartnerDao.insertOrUpdatePartner(companion);
    print('Saved partner with ID: ${_partner.id} as synced');
  }

  @override
  Future<ResPartnerSync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final partner = await db.resPartnerDao.getPartnerByuniversal_id(universal_id!);

    if (partner != null) {
      return ResPartnerSync(partner);
    }

    return null;
  }

  // Additional methods
  ResPartnerTableData get partner => _partner;
}

/// Wrapper class for ProductCategoryTableData that implements DriftSyncClass
class ProductCategorySync implements DriftSyncClass {
  final ProductCategoryTableData _category;

  ProductCategorySync(this._category);

  // DriftSyncClass properties
  @override
  int get id => _category.id;

  @override
  int? get universal_id => _category.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _category.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _category.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _category.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _category.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _category.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the category's JSON data
    Map<String, dynamic> data = _category.toJson();

    // Map local company_id to Odoo company_id (universal_id) if present
    if (data['company_id'] != null) {
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // When syncing FROM Odoo, use the Odoo ID as the local ID to eliminate mapping issues
    // This ensures that local ID = universal_id, making category references work seamlessly
    int localId = _category.id;
    if (universal_id != null && localData == null) {
      // This is a new record from Odoo - use the Odoo ID as local ID
      localId = universal_id!;
    }

    // Create a companion with the synced status
    final companion = ProductCategoryTableCompanion(
      id: Value(localId),
      name: Value(_category.name),
      complete_name: Value(_category.complete_name),
      parent_id: Value(_category.parent_id),
      parent_path: Value(_category.parent_path),
      company_id: Value(_category.company_id),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_category.origin_id),
      version: Value(_category.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_category.is_deleted),
    );

    // Save to database
    await db.productCategoryDao.insertOrUpdateCategory(companion);
    print('Saved category with ID: ${_category.id} as synced');
  }

  @override
  Future<ProductCategorySync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final category = await db.productCategoryDao.getCategoryByuniversal_id(universal_id!);

    if (category != null) {
      return ProductCategorySync(category);
    }

    return null;
  }

  // Additional methods
  ProductCategoryTableData get category => _category;
}

/// Wrapper class for ProductProductTableData that implements DriftSyncClass
class ProductProductSync implements DriftSyncClass {
  final ProductProductTableData _product;

  ProductProductSync(this._product);

  // DriftSyncClass properties
  @override
  int get id => _product.id;

  @override
  int? get universal_id => _product.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _product.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _product.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _product.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _product.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _product.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the product's JSON data
    Map<String, dynamic> data = _product.toJson();

    // Map local company_id to Odoo company_id (universal_id) if present
    if (data['company_id'] != null) {
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // When syncing FROM Odoo, use the Odoo ID as the local ID to eliminate mapping issues
    // This ensures that local ID = universal_id, making product references work seamlessly
    int localId = _product.id;
    if (universal_id != null && localData == null) {
      // This is a new record from Odoo - use the Odoo ID as local ID
      localId = universal_id!;
    }

    // Create a companion with the synced status
    final companion = ProductProductTableCompanion(
      id: Value(localId),
      name: Value(_product.name),
      description: Value(_product.description),
      list_price: Value(_product.list_price),
      categ_id: Value(_product.categ_id),
      default_code: Value(_product.default_code),
      barcode: Value(_product.barcode),
      active: Value(_product.active),
      type: Value(_product.type),
      uom_id: Value(_product.uom_id),
      uom_po_id: Value(_product.uom_po_id),
      company_id: Value(_product.company_id),
      qty_available: Value(_product.qty_available),
      virtual_available: Value(_product.virtual_available),
      incoming_qty: Value(_product.incoming_qty),
      outgoing_qty: Value(_product.outgoing_qty),
      featured: Value(_product.featured),
      stock: Value(_product.stock),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_product.origin_id),
      version: Value(_product.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_product.is_deleted),
    );

    // Save to database
    await db.productProductDao.insertOrUpdateProduct(companion);
    print('Saved product with ID: ${_product.id} as synced');
  }

  @override
  Future<ProductProductSync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final product = await db.productProductDao.getProductByuniversal_id(universal_id!);

    if (product != null) {
      return ProductProductSync(product);
    }

    return null;
  }

  // Additional methods
  ProductProductTableData get product => _product;
}

/// Wrapper class for AccountMoveTableData that implements DriftSyncClass
class AccountMoveSync implements DriftSyncClass {
  AccountMoveTableData _move;

  AccountMoveSync(this._move);

  // DriftSyncClass properties
  @override
  int get id => _move.id;

  @override
  int? get universal_id => _move.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _move.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _move.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _move.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _move.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _move.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    // Set the ID field for Odoo
    data['id'] = universal_id;

    // Remove app-specific fields that shouldn't be sent to Odoo
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('synced');
    data.remove('version');
    data.remove('is_deleted');
    data.remove('is_confirmed');
    data.remove('confirmed');
    data.remove('origin_id');

    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the move's JSON data
    Map<String, dynamic> data = _move.toJson();

    // Remove app-specific fields that are not valid in Odoo account.move model
    data.remove('currency_symbol');
    data.remove('quick_edit_mode');
    data.remove('tax_rate');
    data.remove('is_order');
    data.remove('invoice_payment_state'); // Might be a duplicate of payment_state
    data.remove('invoice_partner_bank_id'); // This field exists in our local model but not in Odoo

    // Remove sync-related fields that shouldn't be sent to Odoo
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('is_confirmed');
    data.remove('is_deleted');
    data.remove('origin_id');
    data.remove('version');

    // Get invoice line items
    final db = DatabaseService().database;
    final lines = await db.accountMoveLineDao.getLinesForInvoice(_move.id);

    // Format invoice_line_ids for Odoo
    // In Odoo, invoice_line_ids should be formatted as [(0, 0, {line_data}), (0, 0, {line_data}), ...]
    if (lines.isNotEmpty) {
      List<List<dynamic>> lineCommands = [];

      print("DEBUG: Processing ${lines.length} invoice lines for Odoo sync");

      for (var line in lines) {
        // Remove fields that shouldn't be sent to Odoo
        Map<String, dynamic> lineData = line.toJson();
        lineData.remove('id');
        lineData.remove('move_id'); // This will be set automatically by Odoo
        lineData.remove('universal_id');
        lineData.remove('is_synced');
        lineData.remove('is_confirmed');
        lineData.remove('is_deleted');
        lineData.remove('origin_id');
        lineData.remove('version');

        // Validate and convert product_id to Odoo format
        if (lineData['product_id'] != null && lineData['product_id'] is int) {
          // Validate that the product has a universal_id (came from Odoo)
          final db = DatabaseService().database;
          final product = await db.productProductDao.getProductById(lineData['product_id']);

          if (product == null || product.universal_id == null) {
            throw Exception('Invoice line contains a product that is not synced from Odoo. Product ID: ${lineData['product_id']}. Only products synced from Odoo can be used in invoices.');
          }

          // Use the universal_id directly as an integer (not as an array)
          lineData['product_id'] = product.universal_id;
        }
        // Keep partner_id as integer - Many2one fields should be sent as integers to Odoo
        // The [id, name] format is only used when READING from Odoo, not when WRITING

        // Remove currency_id from line items as they inherit it from the parent invoice
        // In Odoo, invoice line items automatically use the invoice's currency
        lineData.remove('currency_id');

        // Remove company_id from line items as they inherit it from the parent invoice
        // In Odoo, invoice line items don't need their own company_id as they belong to the invoice
        lineData.remove('company_id');

        // Ensure the line item is properly linked to the invoice
        // Note: This happens automatically in Odoo when we send the line items as part of the invoice

        // Handle account_id for Odoo invoice lines
        // If account_id is null, remove it entirely so Odoo can auto-determine the correct account
        // based on the product's configuration and invoice type
        if (lineData['account_id'] == null) {
          lineData.remove('account_id');
          print("DEBUG: Removed null account_id for line: ${lineData['name']} - Odoo will auto-determine");
        } else {
          print("DEBUG: Using existing account_id: ${lineData['account_id']} for line: ${lineData['name']}");
        }

        // Ensure quantity and price_unit are set
        if (lineData['quantity'] == null) {
          lineData['quantity'] = 1.0;
          print("DEBUG: Set default quantity=1.0 for line: ${lineData['name']}");
        }
        if (lineData['price_unit'] == null) {
          lineData['price_unit'] = 0.0;
          print("DEBUG: Set default price_unit=0.0 for line: ${lineData['name']}");
        }

        // Debug line data before adding to commands
        print("DEBUG: Line data for '${lineData['name']}': quantity=${lineData['quantity']}, price_unit=${lineData['price_unit']}, price_total=${lineData['price_total']}, price_subtotal=${lineData['price_subtotal']}, product_id=${lineData['product_id']}");

        // Remove null values
        lineData.removeWhere((key, value) => value == null);

        // Add the line command (0, 0, {values}) means create a new record
        lineCommands.add([0, 0, lineData]);
      }

      // Add the invoice_line_ids to the data
      data['invoice_line_ids'] = lineCommands;
      print("DEBUG: Created ${lineCommands.length} line commands for Odoo");
    } else {
      print("DEBUG: No invoice lines found for invoice ID: ${_move.id}");
    }

    // Ensure required fields for account.move are set
    if (data['journal_id'] == null) {
      // In Odoo, journal_id is required for invoices
      // Try to get an appropriate journal for customer invoices
      data['journal_id'] = await _getAppropriateJournalId(data['move_type'] ?? 'out_invoice');
    } else if (data['journal_id'] is List) {
      // If journal_id is in Many2one format [id, name], extract just the ID
      data['journal_id'] = data['journal_id'][0];
    }

    // Ensure move_type is set correctly
    if (data['move_type'] == null) {
      data['move_type'] = 'out_invoice'; // Default to customer invoice
    }

    // Ensure state is set to 'draft' for Odoo sync
    // Odoo requires invoices to be in draft state when created/synced
    data['state'] = 'draft';

    // Ensure auto_post is set correctly for Odoo (required field since v16.0)
    // Convert various formats to proper Odoo selection values
    if (data['auto_post'] == null || data['auto_post'] == '' || data['auto_post'] == 'null') {
      data['auto_post'] = 'no'; // Default to 'no' for draft invoices
    } else if (data['auto_post'] is bool) {
      // Convert boolean to Odoo selection value
      data['auto_post'] = data['auto_post'] ? 'at_date' : 'no';
    } else if (data['auto_post'] is String) {
      // Handle string values
      switch (data['auto_post'].toString().toLowerCase()) {
        case 'true':
          data['auto_post'] = 'at_date';
          break;
        case 'false':
          data['auto_post'] = 'no';
          break;
        case 'no':
        case 'at_date':
        case 'at_date_end':
          // Valid Odoo selection values, keep as is
          break;
        default:
          data['auto_post'] = 'no'; // Default fallback
      }
    } else {
      data['auto_post'] = 'no'; // Default fallback for any other type
    }

    // Ensure date field is set (required field in Odoo)
    // In Odoo, the 'date' field is the accounting date and is mandatory
    // If not set, use invoice_date or current date as fallback
    if (data['date'] == null || data['date'] == 'null' || data['date'] == '') {
      if (data['invoice_date'] != null && data['invoice_date'] != 'null' && data['invoice_date'] != '') {
        data['date'] = data['invoice_date']; // Use invoice_date as accounting date
      } else {
        // Fallback to current date in YYYY-MM-DD format
        data['date'] = DateTime.now().toIso8601String().split('T')[0];
      }
    }

    // Ensure other potentially required fields are set
    if (data['payment_state'] == null) {
      data['payment_state'] = 'not_paid'; // Default payment state
    }

    // Ensure move_type is set (required for invoices)
    if (data['move_type'] == null) {
      data['move_type'] = 'out_invoice'; // Default to customer invoice
    }

    // Ensure partner_id is set (required for invoices)
    if (data['partner_id'] == null) {
      throw Exception('partner_id is required for invoice sync but is null');
    } else {
      // Map local partner_id to Odoo partner_id (universal_id)
      int localPartnerId = data['partner_id'];
      final db = DatabaseService().database;
      final partner = await db.resPartnerDao.getPartnerById(localPartnerId);
      if (partner != null && partner.universal_id != null) {
        data['partner_id'] = partner.universal_id;
        print("Mapped local partner_id $localPartnerId to Odoo partner_id ${partner.universal_id}");
      } else {
        print("WARNING: Could not find universal_id for partner with local ID $localPartnerId");
      }
    }

    // Log a warning if company_id is missing
    if (data['company_id'] == null) {
      print("WARNING: Invoice with ID ${_move.id} has no company_id");
      // We're not manually setting company_id to preserve the original data
    } else {
      // Map local company_id to Odoo company_id (universal_id)
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      }
    }

    // Map local currency_id to Odoo currency_id (universal_id)
    if (data['currency_id'] != null && data['currency_id'] is int) {
      int localCurrencyId = data['currency_id'];
      print("DEBUG: Attempting to map local currency_id $localCurrencyId to Odoo currency_id");

      final odooCurrencyId = await mapLocalCurrencyIdToOdoo(localCurrencyId);
      if (odooCurrencyId != null) {
        data['currency_id'] = odooCurrencyId;
        print("SUCCESS: Mapped currency_id $localCurrencyId -> $odooCurrencyId");
      } else {
        print("WARNING: Could not map currency_id $localCurrencyId for invoice with ID ${_move.id}");
        print("DEBUG: Attempting to find fallback currency...");

        // Try to get a fallback currency to prevent Odoo validation errors
        final fallbackCurrencyId = await _getFallbackCurrencyId(data['company_id']);
        if (fallbackCurrencyId != null) {
          data['currency_id'] = fallbackCurrencyId;
          print("SUCCESS: Using fallback currency_id $fallbackCurrencyId for invoice ${_move.id}");
        } else {
          print("ERROR: No fallback currency available - removing currency_id to let Odoo use company default");
          // Remove currency_id to let Odoo use company default
          data.remove('currency_id');
        }
      }
    } else if (data['currency_id'] == null) {
      print("DEBUG: Invoice has no currency_id, attempting to set fallback currency");
      // If no currency_id is set, try to get a fallback
      final fallbackCurrencyId = await _getFallbackCurrencyId(data['company_id']);
      if (fallbackCurrencyId != null) {
        data['currency_id'] = fallbackCurrencyId;
        print("SUCCESS: Set fallback currency_id $fallbackCurrencyId for invoice ${_move.id}");
      } else {
        print("INFO: No fallback currency available - Odoo will use company default");
      }
    }

    // Add sync variables
    final result = toSyncVars(data);

    // CRITICAL: Ensure date field is always set for Odoo (required field)
    // In Odoo, the 'date' field is the accounting date and is mandatory
    if (result['date'] == null || result['date'] == 'null' || result['date'] == '') {
      if (result['invoice_date'] != null && result['invoice_date'] != 'null' && result['invoice_date'] != '') {
        result['date'] = result['invoice_date']; // Use invoice_date as accounting date
      } else {
        // Fallback to current date in YYYY-MM-DD format
        result['date'] = DateTime.now().toIso8601String().split('T')[0];
      }
    }

    // Validate invoice data before sending to Odoo
    _validateInvoiceDataForOdoo(result);

    // Debug logging to verify critical field values for balancing
    print('DEBUG: Invoice sync data summary:');
    print('  - Invoice ID: ${_move.id}');
    print('  - Name: ${result['name']}');
    print('  - Move Type: ${result['move_type']}');
    print('  - State: ${result['state']}');
    print('  - Journal ID: ${result['journal_id']}');
    print('  - Partner ID: ${result['partner_id']}');
    print('  - Company ID: ${result['company_id']}');
    print('  - Currency ID: ${result['currency_id']}');
    print('  - Date: ${result['date']}');
    print('  - Invoice Date: ${result['invoice_date']}');
    print('  - Amount Untaxed: ${result['amount_untaxed']}');
    print('  - Amount Tax: ${result['amount_tax']}');
    print('  - Amount Total: ${result['amount_total']}');
    print('  - Invoice Lines Count: ${result['invoice_line_ids'] != null ? (result['invoice_line_ids'] as List).length : 0}');

    return result;
  }

  /// Validate invoice data to prevent balance issues in Odoo
  void _validateInvoiceDataForOdoo(Map<String, dynamic> data) {
    print('DEBUG: Validating invoice data for Odoo balance requirements...');

    // Check required fields
    final requiredFields = ['move_type', 'journal_id', 'date'];
    for (final field in requiredFields) {
      if (data[field] == null) {
        print('WARNING: Required field $field is null - this may cause balance issues');
      }
    }

    // Check invoice lines
    if (data['invoice_line_ids'] != null && data['invoice_line_ids'] is List) {
      final lines = data['invoice_line_ids'] as List;
      if (lines.isEmpty) {
        print('WARNING: Invoice has no line items - this will cause balance issues');
      } else {
        double totalLineAmount = 0.0;
        for (final lineCommand in lines) {
          if (lineCommand is List && lineCommand.length >= 3) {
            final lineData = lineCommand[2] as Map<String, dynamic>;
            final priceTotal = lineData['price_total'] ?? 0.0;
            totalLineAmount += priceTotal;

            // Check for missing critical line data
            if (lineData['product_id'] == null) {
              print('WARNING: Line "${lineData['name']}" has no product_id - may cause account resolution issues');
            }
            if (lineData['quantity'] == null || lineData['quantity'] == 0) {
              print('WARNING: Line "${lineData['name']}" has zero quantity');
            }
            if (lineData['price_unit'] == null) {
              print('WARNING: Line "${lineData['name']}" has no price_unit');
            }
          }
        }

        // Compare line total with invoice total
        final invoiceTotal = data['amount_total'] ?? 0.0;
        if ((totalLineAmount - invoiceTotal).abs() > 0.01) {
          print('WARNING: Line items total ($totalLineAmount) does not match invoice total ($invoiceTotal) - difference: ${totalLineAmount - invoiceTotal}');
        }
      }
    } else {
      print('WARNING: Invoice has no invoice_line_ids - this will cause balance issues');
    }

    print('DEBUG: Invoice validation complete');
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // When syncing FROM Odoo, use the Odoo ID as the local ID to eliminate mapping issues
    // This ensures that local ID = universal_id, making invoice references work seamlessly
    int localId = _move.id;
    int odooId;
    bool isNewRecordFromOdoo = false;
    bool isNewRecordToOdoo = false;

    if (localData == null) {
      // This is a new record from Odoo - use the Odoo ID as local ID
      if (universal_id != null) {
        localId = universal_id!;
        odooId = universal_id!;
        isNewRecordFromOdoo = true;
      } else {
        // Fallback case - shouldn't happen
        odooId = universal_id ?? 0;
      }
    } else if (localData is int) {
      // We just created a new record in Odoo, localData is the Odoo ID
      // This is the critical case where we need to update local ID to match Odoo ID
      odooId = localData;
      localId = localData; // Update local ID to match Odoo ID
      isNewRecordToOdoo = true;
      print("CRITICAL: Updating local invoice ID from ${_move.id} to $localId to match Odoo ID");
    } else if (localData is DriftSyncClass) {
      // We're updating an existing record, use the universal_id from the existing record
      odooId = localData.universal_id ?? universal_id ?? 0;
    } else {
      // Fallback case
      odooId = universal_id ?? 0;
    }

    // Create a companion to update the existing record with sync status
    final companion = AccountMoveTableCompanion(
      id: Value(localId), // Use synchronized local ID
      name: Value(_move.name),
      move_type: Value(_move.move_type),
      state: Value(_move.state),
      partner_id: Value(_move.partner_id),
      invoice_date: Value(_move.invoice_date),
      invoice_date_due: Value(_move.invoice_date_due),
      date: Value(_move.date),
      narration: Value(_move.narration),
      currency_id: Value(_move.currency_id),
      currency_symbol: Value(_move.currency_symbol),
      company_id: Value(_move.company_id),
      journal_id: Value(_move.journal_id),
      amount_untaxed: Value(_move.amount_untaxed),
      amount_tax: Value(_move.amount_tax),
      amount_total: Value(_move.amount_total),
      amount_residual: Value(_move.amount_residual),
      amount_untaxed_signed: Value(_move.amount_untaxed_signed),
      amount_tax_signed: Value(_move.amount_tax_signed),
      amount_total_signed: Value(_move.amount_total_signed),
      amount_residual_signed: Value(_move.amount_residual_signed),
      payment_state: Value(_move.payment_state),
      payment_reference: Value(_move.payment_reference),
      invoice_payment_term_id: Value(_move.invoice_payment_term_id),
      invoice_payment_state: Value(_move.invoice_payment_state),
      invoice_user_id: Value(_move.invoice_user_id),
      invoice_partner_display_name: Value(_move.invoice_partner_display_name),
      invoice_origin: Value(_move.invoice_origin),
      invoice_cash_rounding_id: Value(_move.invoice_cash_rounding_id),
      tax_cash_basis_rec_id: Value(_move.tax_cash_basis_rec_id),
      tax_cash_basis_origin_move_id: Value(_move.tax_cash_basis_origin_move_id),
      auto_post: Value(_move.auto_post),
      reversed_entry_id: Value(_move.reversed_entry_id),
      fiscal_position_id: Value(_move.fiscal_position_id),
      invoice_incoterm_id: Value(_move.invoice_incoterm_id),
      invoice_source_email: Value(_move.invoice_source_email),
      invoice_partner_bank_id: Value(_move.invoice_partner_bank_id),
      universal_id: Value(odooId), // Set the Odoo ID as universal_id
      is_synced: const Value(true),
      origin_id: Value(_move.origin_id),
      version: Value(_move.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_move.is_deleted),
    );

    // Save to database - this will insert or update the record
    try {
      var newId = await db.accountMoveDao.insertOrUpdateInvoice(companion);
      print('SUCCESS: Saved invoice with local ID: $localId, Odoo ID: $odooId, returned ID: $newId');

      // CRITICAL: Handle ID synchronization for records synced TO Odoo
      if (isNewRecordToOdoo && _move.id != localId) {
        // We need to update all related records that reference the old local ID
        await _updateRelatedRecordsAfterIdChange(_move.id, localId, db);

        // Update the internal _move object to reflect the new ID
        _move = _move.copyWith(id: localId, universal_id: odooId);
        print('UPDATED: Invoice internal state - old ID: ${_move.id}, new ID: $localId, universal_id: $odooId');
      }

      // CRITICAL: Fetch and update line item IDs after syncing TO Odoo
      if (isNewRecordToOdoo) {
        await _updateLineItemIdsAfterOdooSync(localId, db);
        // Note: Payments are currently not synced as separate entities to Odoo
        // They are managed locally within the app
      }

      // Verify the invoice was actually saved
      final savedInvoice = await db.accountMoveDao.getInvoiceById(newId);
      if (savedInvoice != null) {
        print('VERIFIED: Invoice exists in database with ID: ${savedInvoice.id}, universal_id: ${savedInvoice.universal_id}');
      } else {
        print('ERROR: Invoice was not found in database after save operation');
      }
    } catch (e, stackTrace) {
      print('ERROR: Failed to save invoice: $e');
      print('STACK TRACE: $stackTrace');
      print('INVOICE DATA: localId=$localId, odooId=$odooId, move.id=${_move.id}');
      rethrow;
    }
  }

  @override
  Future<AccountMoveSync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final move = await db.accountMoveDao.getInvoiceByuniversal_id(universal_id!);

    if (move != null) {
      return AccountMoveSync(move);
    }

    return null;
  }

  // Additional methods
  AccountMoveTableData get move => _move;

  /// Get an appropriate journal ID for the given move type
  Future<int> _getAppropriateJournalId(String moveType) async {
    try {
      // Define journal types based on move type
      String journalType;
      switch (moveType) {
        case 'out_invoice':
        case 'out_refund':
          journalType = 'sale';
          break;
        case 'in_invoice':
        case 'in_refund':
          journalType = 'purchase';
          break;
        default:
          journalType = 'sale'; // Default to sales journal
      }

      // Search for journals of the appropriate type
      final journals = await OdooClient.instance.executeKw(
        model: 'account.journal',
        method: 'search_read',
        args: [
          [
            ['type', '=', journalType],
            ['active', '=', true],
          ],
          ['id', 'name', 'type']
        ],
        kwargs: {'limit': 1},
      );

      if (journals is List && journals.isNotEmpty) {
        final journal = journals.first;
        print("Found appropriate journal for $moveType: ${journal['name']} (ID: ${journal['id']})");
        return journal['id'];
      }

      // Fallback: search for any active journal
      print("No $journalType journal found, searching for any active journal");
      final anyJournals = await OdooClient.instance.executeKw(
        model: 'account.journal',
        method: 'search_read',
        args: [
          [['active', '=', true]],
          ['id', 'name', 'type']
        ],
        kwargs: {'limit': 1},
      );

      if (anyJournals is List && anyJournals.isNotEmpty) {
        final journal = anyJournals.first;
        print("Using fallback journal: ${journal['name']} (ID: ${journal['id']})");
        return journal['id'];
      }

      // Last resort: use ID 1
      print("No journals found in Odoo, using default journal ID 1");
      return 1;
    } catch (e) {
      print("Error fetching journal from Odoo: $e, using default journal ID 1");
      return 1;
    }
  }

  /// Get a fallback currency ID when the original currency mapping fails
  Future<int?> _getFallbackCurrencyId(dynamic companyId) async {
    print("DEBUG: _getFallbackCurrencyId called with companyId: $companyId");

    try {
      // First, try to get the company's default currency from Odoo
      if (companyId != null) {
        print("DEBUG: Attempting to get company default currency from Odoo...");
        final companies = await OdooClient.instance.executeKw(
          model: 'res.company',
          method: 'search_read',
          args: [
            [['id', '=', companyId]],
            ['currency_id', 'name']
          ],
          kwargs: {'limit': 1},
        );

        if (companies is List && companies.isNotEmpty) {
          final company = companies.first;
          print("DEBUG: Found company: ${company['name']}");
          if (company['currency_id'] != null) {
            // When reading from Odoo, currency_id is returned as [id, name] format
            // But when writing to Odoo, we need to send just the integer ID
            if (company['currency_id'] is List && company['currency_id'].isNotEmpty) {
              final currencyId = company['currency_id'][0];
              final currencyName = company['currency_id'][1];
              print("SUCCESS: Found company default currency: $currencyName (ID: $currencyId)");
              return currencyId; // Return just the integer ID for writing to Odoo
            } else if (company['currency_id'] is int) {
              print("SUCCESS: Found company default currency_id: ${company['currency_id']}");
              return company['currency_id'];
            }
          } else {
            print("WARNING: Company has no currency_id set");
          }
        } else {
          print("WARNING: No company found with ID $companyId");
        }
      } else {
        print("DEBUG: No companyId provided, skipping company currency lookup");
      }

      // Fallback: try to find USD currency in Odoo
      print("DEBUG: Attempting to find USD currency in Odoo...");
      final usdCurrencies = await OdooClient.instance.executeKw(
        model: 'res.currency',
        method: 'search_read',
        args: [
          [['name', '=', 'USD'], ['active', '=', true]],
          ['id', 'name', 'symbol']
        ],
        kwargs: {'limit': 1},
      );

      if (usdCurrencies is List && usdCurrencies.isNotEmpty) {
        final usd = usdCurrencies.first;
        final usdId = usd['id'];
        print("SUCCESS: Found USD currency as fallback: ${usd['name']} ${usd['symbol']} (ID: $usdId)");
        return usdId;
      } else {
        print("WARNING: USD currency not found in Odoo");
      }

      // Last resort: try to find any active currency
      print("DEBUG: Attempting to find any active currency in Odoo...");
      final anyCurrencies = await OdooClient.instance.executeKw(
        model: 'res.currency',
        method: 'search_read',
        args: [
          [['active', '=', true]],
          ['id', 'name', 'symbol']
        ],
        kwargs: {'limit': 1},
      );

      if (anyCurrencies is List && anyCurrencies.isNotEmpty) {
        final currency = anyCurrencies.first;
        final currencyId = currency['id'];
        print("SUCCESS: Found fallback currency: ${currency['name']} ${currency['symbol']} (ID: $currencyId)");
        return currencyId;
      }

      print("ERROR: No active currencies found in Odoo - this is a serious configuration issue");
      return null;
    } catch (e) {
      print("ERROR: Failed to get fallback currency from Odoo: $e");
      print("STACK TRACE: ${StackTrace.current}");
      return null;
    }
  }

  /// Update all related records when an invoice ID changes after sync to Odoo
  /// This is critical to maintain data integrity when local IDs are updated to match Odoo IDs
  /// Handles potential ID conflicts with existing unsynced records
  Future<void> _updateRelatedRecordsAfterIdChange(int oldId, int newId, AppDatabase db) async {
    print("CRITICAL: Updating related records after invoice ID change: $oldId -> $newId");

    try {
      // STEP 1: Handle potential ID conflict with existing record
      await _handleIdConflict(oldId, newId, db);

      // STEP 2: Update invoice line items to reference the new invoice ID
      final lineItems = await db.accountMoveLineDao.getLinesForInvoice(oldId);
      for (final lineItem in lineItems) {
        if (lineItem.move_id == oldId) {
          await (db.update(db.accountMoveLineTable)
            ..where((tbl) => tbl.id.equals(lineItem.id)))
            .write(AccountMoveLineTableCompanion(
              move_id: Value(newId),
            ));
          print("Updated line item ${lineItem.id} to reference new invoice ID $newId");
        }
      }

      // STEP 3: Update payments to reference the new invoice ID
      final payments = await db.accountPaymentDao.getPaymentsForInvoice(oldId);
      for (final payment in payments) {
        if (payment.move_id == oldId) {
          await (db.update(db.accountPaymentTable)
            ..where((tbl) => tbl.id.equals(payment.id)))
            .write(AccountPaymentTableCompanion(
              move_id: Value(newId),
            ));
          print("Updated payment ${payment.id} to reference new invoice ID $newId");
        }
      }

      print("SUCCESS: Updated all related records for invoice ID change: $oldId -> $newId");
    } catch (e, stackTrace) {
      print("ERROR: Failed to update related records after ID change: $e");
      print("STACK TRACE: $stackTrace");
      rethrow;
    }
  }

  /// Fetch line items from Odoo and update local IDs after syncing invoice TO Odoo
  /// This ensures that local line item IDs match the IDs assigned by Odoo
  Future<void> _updateLineItemIdsAfterOdooSync(int invoiceId, AppDatabase db) async {
    print("CRITICAL: Fetching line items from Odoo to update local IDs for invoice $invoiceId");

    try {
      // Fetch line items from Odoo for this invoice
      final lineItems = await OdooClient.instance.searchRead(
        'account.move.line',
        [['move_id', '=', invoiceId]],
        [
          'id', 'name', 'product_id', 'move_id', 'quantity', 'price_unit',
          'discount', 'price_subtotal', 'price_total'
        ]
      );

      if (lineItems.isNotEmpty) {
        print("Found ${lineItems.length} line items in Odoo for invoice $invoiceId");

        // Get current local line items for this invoice
        final localLineItems = await db.accountMoveLineDao.getLinesForInvoice(invoiceId);

        // Match and update line items based on name and product
        for (final odooLineItem in lineItems) {
          final odooId = odooLineItem['id'] as int;
          final odooName = odooLineItem['name'] as String?;
          final odooProductId = odooLineItem['product_id'];

          // Extract product ID from Odoo format [id, name] or direct int
          int? odooProductUniversalId;
          if (odooProductId is List && odooProductId.isNotEmpty) {
            odooProductUniversalId = odooProductId[0] as int;
          } else if (odooProductId is int) {
            odooProductUniversalId = odooProductId;
          }

          // Find matching local line item
          AccountMoveLineTableData? matchingLocalItem;
          for (final localItem in localLineItems) {
            // Match by product and name
            if (localItem.name == odooName) {
              if (odooProductUniversalId != null && localItem.product_id != null) {
                // Get the product to check universal_id
                final product = await db.productProductDao.getProductById(localItem.product_id!);
                if (product?.universal_id == odooProductUniversalId) {
                  matchingLocalItem = localItem;
                  break;
                }
              } else if (odooProductUniversalId == null && localItem.product_id == null) {
                // Both have no product - match by name only
                matchingLocalItem = localItem;
                break;
              }
            }
          }

          if (matchingLocalItem != null && matchingLocalItem.id != odooId) {
            // Check for ID conflict before updating
            await _handleLineItemIdConflict(matchingLocalItem.id, odooId, db);

            // Update the local line item ID to match Odoo ID
            print("Updating line item '${matchingLocalItem.name}' ID from ${matchingLocalItem.id} to $odooId");

            await (db.update(db.accountMoveLineTable)
              ..where((tbl) => tbl.id.equals(matchingLocalItem!.id)))
              .write(AccountMoveLineTableCompanion(
                id: Value(odooId),
                universal_id: Value(odooId),
                is_synced: const Value(true),
              ));
          }
        }

        print("SUCCESS: Updated line item IDs for invoice $invoiceId");
      } else {
        print("WARNING: No line items found in Odoo for invoice $invoiceId");
      }
    } catch (e, stackTrace) {
      print("ERROR: Failed to update line item IDs after Odoo sync: $e");
      print("STACK TRACE: $stackTrace");
      // Don't rethrow - this is not critical enough to fail the entire sync
    }
  }

  /// Handle potential ID conflicts when updating a record's ID to match Odoo ID
  /// This occurs when there's already an existing local record with the target ID
  Future<void> _handleIdConflict(int oldId, int newId, AppDatabase db) async {
    if (oldId == newId) {
      // No conflict - IDs are the same
      return;
    }

    print("CONFLICT CHECK: Checking for existing record with target ID $newId");

    try {
      // Check if there's already a record with the target ID
      final existingRecord = await db.accountMoveDao.getInvoiceById(newId);

      if (existingRecord != null) {
        print("CONFLICT DETECTED: Record with ID $newId already exists");

        // Check if the existing record is synced
        if (existingRecord.is_synced == true && existingRecord.universal_id != null) {
          // The existing record is already synced - this is a serious conflict
          throw Exception(
            'CRITICAL CONFLICT: Cannot update invoice ID from $oldId to $newId because '
            'a synced record with ID $newId already exists (universal_id: ${existingRecord.universal_id}). '
            'This indicates a data integrity issue that requires manual resolution.'
          );
        } else {
          // The existing record is not synced - we can reassign it a new ID
          print("CONFLICT RESOLUTION: Reassigning unsynced record with ID $newId to a new ID");
          await _reassignRecordToNewId(existingRecord, db);
        }
      } else {
        print("NO CONFLICT: Target ID $newId is available");
      }
    } catch (e, stackTrace) {
      print("ERROR: Failed to handle ID conflict: $e");
      print("STACK TRACE: $stackTrace");
      rethrow;
    }
  }

  /// Reassign an unsynced record to a new auto-generated ID
  /// This is used to resolve ID conflicts with records that haven't been synced yet
  Future<void> _reassignRecordToNewId(AccountMoveTableData conflictingRecord, AppDatabase db) async {
    print("REASSIGNING: Moving record ${conflictingRecord.id} to new auto-generated ID");

    try {
      // Create a new record with auto-generated ID (by setting id to absent)
      final newRecordCompanion = AccountMoveTableCompanion(
        // id is absent - will be auto-generated
        name: Value(conflictingRecord.name),
        move_type: Value(conflictingRecord.move_type),
        state: Value(conflictingRecord.state),
        partner_id: Value(conflictingRecord.partner_id),
        invoice_date: Value(conflictingRecord.invoice_date),
        invoice_date_due: Value(conflictingRecord.invoice_date_due),
        date: Value(conflictingRecord.date),
        narration: Value(conflictingRecord.narration),
        currency_id: Value(conflictingRecord.currency_id),
        currency_symbol: Value(conflictingRecord.currency_symbol),
        company_id: Value(conflictingRecord.company_id),
        journal_id: Value(conflictingRecord.journal_id),
        amount_untaxed: Value(conflictingRecord.amount_untaxed),
        amount_tax: Value(conflictingRecord.amount_tax),
        amount_total: Value(conflictingRecord.amount_total),
        amount_residual: Value(conflictingRecord.amount_residual),
        amount_untaxed_signed: Value(conflictingRecord.amount_untaxed_signed),
        amount_tax_signed: Value(conflictingRecord.amount_tax_signed),
        amount_total_signed: Value(conflictingRecord.amount_total_signed),
        amount_residual_signed: Value(conflictingRecord.amount_residual_signed),
        payment_state: Value(conflictingRecord.payment_state),
        payment_reference: Value(conflictingRecord.payment_reference),
        invoice_payment_term_id: Value(conflictingRecord.invoice_payment_term_id),
        invoice_payment_state: Value(conflictingRecord.invoice_payment_state),
        invoice_user_id: Value(conflictingRecord.invoice_user_id),
        invoice_partner_display_name: Value(conflictingRecord.invoice_partner_display_name),
        invoice_origin: Value(conflictingRecord.invoice_origin),
        invoice_cash_rounding_id: Value(conflictingRecord.invoice_cash_rounding_id),
        tax_cash_basis_rec_id: Value(conflictingRecord.tax_cash_basis_rec_id),
        tax_cash_basis_origin_move_id: Value(conflictingRecord.tax_cash_basis_origin_move_id),
        auto_post: Value(conflictingRecord.auto_post),
        reversed_entry_id: Value(conflictingRecord.reversed_entry_id),
        fiscal_position_id: Value(conflictingRecord.fiscal_position_id),
        invoice_incoterm_id: Value(conflictingRecord.invoice_incoterm_id),
        invoice_source_email: Value(conflictingRecord.invoice_source_email),
        invoice_partner_bank_id: Value(conflictingRecord.invoice_partner_bank_id),
        quick_edit_mode: Value(conflictingRecord.quick_edit_mode),
        tax_rate: Value(conflictingRecord.tax_rate),
        is_order: Value(conflictingRecord.is_order),
        universal_id: Value(conflictingRecord.universal_id),
        is_synced: Value(conflictingRecord.is_synced),
        is_confirmed: Value(conflictingRecord.is_confirmed),
        is_deleted: Value(conflictingRecord.is_deleted),
        origin_id: Value(conflictingRecord.origin_id),
        version: Value(conflictingRecord.version),
      );

      // Insert the record with a new auto-generated ID
      final newId = await db.accountMoveDao.insertOrUpdateInvoice(newRecordCompanion);
      print("REASSIGNED: Record moved from ID ${conflictingRecord.id} to new ID $newId");

      // Update related records to reference the new ID
      await _updateRelatedRecordsAfterIdChange(conflictingRecord.id, newId, db);

      // Delete the old record
      await (db.delete(db.accountMoveTable)
        ..where((tbl) => tbl.id.equals(conflictingRecord.id)))
        .go();

      print("SUCCESS: Conflict resolved - old record deleted, new record created with ID $newId");
    } catch (e, stackTrace) {
      print("ERROR: Failed to reassign record to new ID: $e");
      print("STACK TRACE: $stackTrace");
      rethrow;
    }
  }

  /// Handle potential ID conflicts for line items when updating IDs to match Odoo IDs
  Future<void> _handleLineItemIdConflict(int oldId, int newId, AppDatabase db) async {
    if (oldId == newId) {
      return; // No conflict
    }

    print("LINE ITEM CONFLICT CHECK: Checking for existing line item with target ID $newId");

    try {
      // Check if there's already a line item with the target ID
      final existingLineItem = await (db.select(db.accountMoveLineTable)
        ..where((tbl) => tbl.id.equals(newId)))
        .getSingleOrNull();

      if (existingLineItem != null) {
        print("LINE ITEM CONFLICT DETECTED: Line item with ID $newId already exists");

        if (existingLineItem.is_synced == true && existingLineItem.universal_id != null) {
          // The existing line item is already synced - this is a serious conflict
          throw Exception(
            'CRITICAL LINE ITEM CONFLICT: Cannot update line item ID from $oldId to $newId because '
            'a synced line item with ID $newId already exists (universal_id: ${existingLineItem.universal_id}). '
            'This indicates a data integrity issue that requires manual resolution.'
          );
        } else {
          // The existing line item is not synced - reassign it to a new ID
          print("LINE ITEM CONFLICT RESOLUTION: Reassigning unsynced line item with ID $newId to a new ID");
          await _reassignLineItemToNewId(existingLineItem, db);
        }
      } else {
        print("NO LINE ITEM CONFLICT: Target ID $newId is available");
      }
    } catch (e, stackTrace) {
      print("ERROR: Failed to handle line item ID conflict: $e");
      print("STACK TRACE: $stackTrace");
      rethrow;
    }
  }

  /// Reassign an unsynced line item to a new auto-generated ID
  Future<void> _reassignLineItemToNewId(AccountMoveLineTableData conflictingLineItem, AppDatabase db) async {
    print("REASSIGNING LINE ITEM: Moving line item ${conflictingLineItem.id} to new auto-generated ID");

    try {
      // Create a new line item with auto-generated ID
      final newLineItemCompanion = AccountMoveLineTableCompanion(
        // id is absent - will be auto-generated
        move_id: Value(conflictingLineItem.move_id),
        name: Value(conflictingLineItem.name),
        product_id: Value(conflictingLineItem.product_id),
        account_id: Value(conflictingLineItem.account_id),
        partner_id: Value(conflictingLineItem.partner_id),
        quantity: Value(conflictingLineItem.quantity),
        price_unit: Value(conflictingLineItem.price_unit),
        discount: Value(conflictingLineItem.discount),
        price_subtotal: Value(conflictingLineItem.price_subtotal),
        price_total: Value(conflictingLineItem.price_total),
        tax_ids: Value(conflictingLineItem.tax_ids),
        currency_id: Value(conflictingLineItem.currency_id),
        universal_id: Value(conflictingLineItem.universal_id),
        is_synced: Value(conflictingLineItem.is_synced),
        origin_id: Value(conflictingLineItem.origin_id),
        version: Value(conflictingLineItem.version),
        is_confirmed: Value(conflictingLineItem.is_confirmed),
        is_deleted: Value(conflictingLineItem.is_deleted),
      );

      // Insert the line item with a new auto-generated ID
      final newId = await db.accountMoveLineDao.insertOrUpdateLine(newLineItemCompanion);
      print("REASSIGNED LINE ITEM: Moved from ID ${conflictingLineItem.id} to new ID $newId");

      // Delete the old line item
      await (db.delete(db.accountMoveLineTable)
        ..where((tbl) => tbl.id.equals(conflictingLineItem.id)))
        .go();

      print("SUCCESS: Line item conflict resolved - old item deleted, new item created with ID $newId");
    } catch (e, stackTrace) {
      print("ERROR: Failed to reassign line item to new ID: $e");
      print("STACK TRACE: $stackTrace");
      rethrow;
    }
  }
}

/// Wrapper class for ResCurrencyTableData that implements DriftSyncClass
class ResCurrencySync implements DriftSyncClass {
  final ResCurrencyTableData _currency;

  ResCurrencySync(this._currency);

  // DriftSyncClass properties
  @override
  int get id => _currency.id;

  @override
  int? get universal_id => _currency.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _currency.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _currency.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _currency.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _currency.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _currency.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the currency's JSON data
    Map<String, dynamic> data = _currency.toJson();
    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // When syncing FROM Odoo, use the Odoo ID as the local ID to eliminate mapping issues
    // This ensures that local ID = universal_id, making currency references work seamlessly
    int localId = _currency.id;
    if (universal_id != null && localData == null) {
      // This is a new record from Odoo - use the Odoo ID as local ID
      localId = universal_id!;
    }

    // Create a companion with the synced status
    final companion = ResCurrencyTableCompanion(
      id: Value(localId),
      name: Value(_currency.name),
      symbol: Value(_currency.symbol),
      full_name: Value(_currency.full_name),
      rate: Value(_currency.rate),
      decimal_places: Value(_currency.decimal_places),
      active: Value(_currency.active),
      position: Value(_currency.position),
      currency_unit_label: Value(_currency.currency_unit_label),
      currency_subunit_label: Value(_currency.currency_subunit_label),
      rounding: Value(_currency.rounding),
       universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_currency.origin_id),
      version: Value(_currency.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_currency.is_deleted),
    );

    // Save to database
    await db.resCurrencyDao.insertOrUpdateCurrency(companion);
    print('Saved currency with ID: ${_currency.id} as synced');
  }

  @override
  Future<ResCurrencySync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final currency = await db.resCurrencyDao.getCurrencyByuniversal_id(universal_id!);

    if (currency != null) {
      return ResCurrencySync(currency);
    }

    return null;
  }

  // Additional methods
  ResCurrencyTableData get currency => _currency;
}
