import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for AccountMoveTableData
/// It replaces the old AccountMove class with direct use of Drift's generated classes

/// Extension methods for AccountMoveTableData
extension AccountMoveTableDataExtensions on AccountMoveTableData {
  /// Check if the invoice is active (not deleted)
  bool isActive() => !is_deleted;

  /// Check if the invoice is a draft
  bool isDraft() => state == 'draft';

  /// Check if the invoice is posted
  bool isPosted() => state == 'posted';

  /// Check if the invoice is cancelled
  bool isCancelled() => state == 'cancel';

  /// Check if the invoice is paid
  bool isPaid() => payment_state == 'paid';

  /// Check if the invoice is an invoice (not a bill, etc.)
  bool isInvoice() => move_type == 'out_invoice';

  /// Check if the invoice is a bill
  bool isBill() => move_type == 'in_invoice';

  /// Check if the invoice is a credit note
  bool isCreditNote() => move_type == 'out_refund';

  /// Check if the invoice is a vendor credit note
  bool isVendorCreditNote() => move_type == 'in_refund';

  /// Get the formatted invoice date
  String getFormattedinvoice_date() {
    if (invoice_date == null) return '';
    return invoice_date!;
  }

  /// Get the formatted due date
  String getFormattedDueDate() {
    if (invoice_date_due == null) return '';
    return invoice_date_due!;
  }

  /// Get the formatted amount
  String getFormattedAmount() {
    if (amount_total == null) return '0.00';
    return amount_total!.toStringAsFixed(2);
  }

  /// Get the formatted currency amount
  String getFormattedCurrencyAmount() {
    if (amount_total == null) return '0.00';
    if (currency_symbol == null) return '${amount_total!.toStringAsFixed(2)}';
    return '${currency_symbol} ${amount_total!.toStringAsFixed(2)}';
  }

  /// Get the invoice status display text
  String getStatusDisplayText() {
    if (state == 'draft') return 'Draft';
    if (state == 'posted') return 'Posted';
    if (state == 'cancel') return 'Cancelled';
    return state ?? 'Unknown';
  }



  /// Get the invoice type display text
  String getTypeDisplayText() {
    if (move_type == 'out_invoice') return 'Invoice';
    if (move_type == 'in_invoice') return 'Bill';
    if (move_type == 'out_refund') return 'Credit Note';
    if (move_type == 'in_refund') return 'Vendor Credit Note';
    return move_type ?? 'Unknown';
  }

  /// Convert to AccountMoveTableCompanion for Drift operations
  /// [markAsUnsynced] - if true, marks the invoice as unsynced (needs sync to Odoo)
  AccountMoveTableCompanion toCompanion([bool markAsUnsynced = false]) {
    return AccountMoveTableCompanion(
      id: Value(id),
      name: Value(name),
      move_type: Value(move_type),
      state: Value(state),
      partner_id: Value(partner_id),
      invoice_date: Value(invoice_date),
      invoice_date_due: Value(invoice_date_due),
      date: Value(date),
      narration: Value(narration),
      currency_id: Value(currency_id),
      currency_symbol: Value(currency_symbol),
      amount_untaxed: Value(amount_untaxed),
      amount_tax: Value(amount_tax),
      amount_total: Value(amount_total),
      amount_residual: Value(amount_residual),
      amount_untaxed_signed: Value(amount_untaxed_signed),
      amount_tax_signed: Value(amount_tax_signed),
      amount_total_signed: Value(amount_total_signed),
      amount_residual_signed: Value(amount_residual_signed),
      company_id: Value(company_id),
      payment_reference: Value(payment_reference),
      payment_state: Value(payment_state),
      journal_id: Value(journal_id),
      invoice_payment_term_id: Value(invoice_payment_term_id),
      invoice_user_id: Value(invoice_user_id),
      invoice_partner_display_name: Value(invoice_partner_display_name),
      invoice_origin: Value(invoice_origin),
      invoice_payment_state: Value(invoice_payment_state),
      invoice_cash_rounding_id: Value(invoice_cash_rounding_id),
      tax_cash_basis_rec_id: Value(tax_cash_basis_rec_id),
      tax_cash_basis_origin_move_id: Value(tax_cash_basis_origin_move_id),
      auto_post: Value(auto_post),
      reversed_entry_id: Value(reversed_entry_id),
      fiscal_position_id: Value(fiscal_position_id),
      invoice_incoterm_id: Value(invoice_incoterm_id),
      invoice_source_email: Value(invoice_source_email),
      invoice_partner_bank_id: Value(invoice_partner_bank_id),
      quick_edit_mode: Value(quick_edit_mode),
      tax_rate: Value(tax_rate),
      is_order: Value(is_order),
      universal_id: Value(universal_id),
      is_synced: Value(markAsUnsynced ? false : is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  AccountMoveTableData copyWith({
    int? id,
    String? name,
    String? move_type,
    String? state,
    int? partner_id,
    String? invoice_date,
    String? invoice_date_due,
    String? date,
    String? ref,
    String? narration,
    int? currency_id,
    String? currency_symbol,
    double? amount_untaxed,
    double? amount_tax,
    double? amount_total,
    double? amount_residual,
    double? amount_untaxed_signed,
    double? amount_tax_signed,
    double? amount_total_signed,
    double? amount_residual_signed,
    int? company_id,
    String? payment_reference,
    String? payment_state,
    int? journal_id,
    String? invoice_payment_term_id,
    String? invoice_user_id,
    String? invoice_partner_display_name,
    String? invoice_origin,
    String? invoice_payment_state,
    String? invoice_cash_rounding_id,
    String? tax_cash_basis_rec_id,
    String? tax_cash_basis_origin_move_id,
    String? auto_post,
    String? reversed_entry_id,
    String? fiscal_position_id,
    String? invoice_incoterm_id,
    String? invoice_source_email,
    String? invoice_partner_bank_id,
    String? quick_edit_mode,
    String? tax_rate,
    bool? is_order,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return AccountMoveTableData(
      id: id ?? this.id,
      name: name ?? this.name,
      move_type: move_type ?? this.move_type,
      state: state ?? this.state,
      partner_id: partner_id ?? this.partner_id,
      invoice_date: invoice_date ?? this.invoice_date,
      invoice_date_due: invoice_date_due ?? this.invoice_date_due,
      date: date ?? this.date,
      narration: narration ?? this.narration,
      currency_id: currency_id ?? this.currency_id,
      currency_symbol: currency_symbol ?? this.currency_symbol,
      amount_untaxed: amount_untaxed ?? this.amount_untaxed,
      amount_tax: amount_tax ?? this.amount_tax,
      amount_total: amount_total ?? this.amount_total,
      amount_residual: amount_residual ?? this.amount_residual,
      amount_untaxed_signed: amount_untaxed_signed ?? this.amount_untaxed_signed,
      amount_tax_signed: amount_tax_signed ?? this.amount_tax_signed,
      amount_total_signed: amount_total_signed ?? this.amount_total_signed,
      amount_residual_signed: amount_residual_signed ?? this.amount_residual_signed,
      company_id: company_id ?? this.company_id,
      payment_reference: payment_reference ?? this.payment_reference,
      payment_state: payment_state ?? this.payment_state,
      journal_id: journal_id ?? this.journal_id,
      invoice_payment_term_id: invoice_payment_term_id ?? this.invoice_payment_term_id,
      invoice_user_id: invoice_user_id ?? this.invoice_user_id,
      invoice_partner_display_name: invoice_partner_display_name ?? this.invoice_partner_display_name,
      invoice_origin: invoice_origin ?? this.invoice_origin,
      invoice_payment_state: invoice_payment_state ?? this.invoice_payment_state,
      invoice_cash_rounding_id: invoice_cash_rounding_id ?? this.invoice_cash_rounding_id,
      tax_cash_basis_rec_id: tax_cash_basis_rec_id ?? this.tax_cash_basis_rec_id,
      tax_cash_basis_origin_move_id: tax_cash_basis_origin_move_id ?? this.tax_cash_basis_origin_move_id,
      auto_post: auto_post ?? this.auto_post,
      reversed_entry_id: reversed_entry_id ?? this.reversed_entry_id,
      fiscal_position_id: fiscal_position_id ?? this.fiscal_position_id,
      invoice_incoterm_id: invoice_incoterm_id ?? this.invoice_incoterm_id,
      invoice_source_email: invoice_source_email ?? this.invoice_source_email,
      invoice_partner_bank_id: invoice_partner_bank_id ?? this.invoice_partner_bank_id,
      quick_edit_mode: quick_edit_mode ?? this.quick_edit_mode,
      tax_rate: tax_rate ?? this.tax_rate,
      is_order: is_order ?? this.is_order,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }
 

 
}
/// Extension methods for AccountMoveTableCompanion
extension AccountMoveTableCompanionExtensions on AccountMoveTableCompanion {
  /// Create a companion for a new invoice
  static AccountMoveTableCompanion createInvoice({
    required String name,
    required String move_type,
    required String state,
    int? partner_id,
    String? invoice_date,
    String? invoice_dateDue,
    int? company_id,
  }) {
    return AccountMoveTableCompanion.insert(
      name: Value(name),
      move_type: Value(move_type),
      state: Value(state),
      partner_id: Value(partner_id),
      invoice_date: Value(invoice_date),
      invoice_date_due: Value(invoice_dateDue),
      company_id: Value(company_id),
      is_deleted: const Value(false),
      is_confirmed: const Value(true),
      is_synced: const Value(false),
      version: const Value(1),
    );
  }

  /// Mark an invoice as deleted
  AccountMoveTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark an invoice as active
  AccountMoveTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
    );
  }

  /// Mark an invoice as synced
  AccountMoveTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }

  /// Mark an invoice as unsynced (needs to be synced to Odoo)
  AccountMoveTableCompanion markAsUnsynced() {
    return copyWith(
      is_synced: const Value(false),
    );
  }

  /// Mark an invoice as draft
  AccountMoveTableCompanion markAsDraft() {
    return copyWith(
      state: const Value('draft'),
    );
  }

  /// Mark an invoice as posted
  AccountMoveTableCompanion markAsPosted() {
    return copyWith(
      state: const Value('posted'),
    );
  }

  /// Mark an invoice as cancelled
  AccountMoveTableCompanion markAsCancelled() {
    return copyWith(
      state: const Value('cancel'),
    );
  }

  /// Mark an invoice as paid
  AccountMoveTableCompanion markAsPaid() {
    return copyWith(
      payment_state: const Value('paid'),
    );
  }
}

/// Model class for an invoice with its related data
class AccountMoveWithRelations {
  final AccountMoveTableData invoice;
  final ResPartnerTableData? partner;
  final ResCompanyTableData? company;
  final List<AccountMoveLineTableData> lines;
  final List<AccountPaymentTableData> payments;

  AccountMoveWithRelations({
    required this.invoice,
    this.partner,
    this.company,
    required this.lines,
    required this.payments,
  });
}
