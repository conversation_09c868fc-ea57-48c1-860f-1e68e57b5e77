// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'LogosMap.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LogosMap _$LogosMapFromJson(Map<String, dynamic> json) => _LogosMap(
      logos: (json['logos'] as List<dynamic>?)
              ?.map((e) => e == null
                  ? null
                  : LogoMap.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$LogosMapToJson(_LogosMap instance) => <String, dynamic>{
      'logos': instance.logos,
    };
