/// Interface for Drift-compatible sync classes
abstract class DriftSyncClass {
  // Properties
  int get id;
  int? get universal_id;
  set universal_id(int? value);
  bool? get is_synced;
  set is_synced(bool? value);
  int? get origin_id;
  set origin_id(int? value);
  int? get version;
  set version(int? value);
  bool? get is_confirmed;
  set is_confirmed(bool? value);
  bool? get is_deleted;
  set is_deleted(bool? value);

  // Methods
  Future<Map<String, dynamic>> toSyncJson();
  Future<void> saveSynced(localCopy);
  Future<dynamic> getByUni();

  // Helper methods for converting to/from JSON
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = this.universal_id;
    data['is_synced'] = this.is_synced ?? false;
    data['synced'] = this.is_synced ?? false;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed;
    data['confirmed'] = this.is_confirmed;
    data['origin_id'] = this.id;

    return data;
  }

  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = this.id;
    data['is_synced'] = this.is_synced ?? true;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed ?? true;
    data['confirmed'] = this.is_confirmed ?? true;
    data['synced'] = this.is_synced ?? true;
    data['origin_id'] = this.id;

    return data;
  }
}
