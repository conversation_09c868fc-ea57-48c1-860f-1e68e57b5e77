import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ResCurrencyExtensions.dart';
import 'package:invoicer/core/network/odoo_client.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/core/utils/UserPreference.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for currency initialization
final currencyInitializerProvider = Provider<CurrencyInitializer>((ref) {
  return CurrencyInitializer(ref);
});

/// Class responsible for initializing currencies in the database
class CurrencyInitializer {
  final Ref ref;

  CurrencyInitializer(this.ref);

  /// Initialize currencies in the database
  Future<void> initializeCurrencies() async {
    try {
      print('Initializing currencies...');

      // Check if we already have currencies in the database
      final currencies = await ref.read(resCurrencyRepositoryProvider).getAll();

      if (currencies.isNotEmpty) {
        print('Currencies already exist in the database (${currencies.length} found)');
        // await _ensureDefaultCurrency();
        return;
      }

      // Try to fetch currencies from Odoo first
      final odooSuccess = await _fetchCurrenciesFromOdoo();

      // If Odoo fetch fails, create default currencies
      // if (!odooSuccess) {
      //   await _createDefaultCurrencies();
      // }

      // Ensure USD is set as the default currency
      // await _ensureDefaultCurrency();

      print('Currency initialization complete');
    } catch (e) {
      print('Error initializing currencies: $e');
      // If anything fails, create default currencies as fallback
      await _createDefaultCurrencies();
      // await _ensureDefaultCurrency();
    }
  }

  /// Fetch currencies from Odoo
  Future<bool> _fetchCurrenciesFromOdoo() async {
    try {
      print('Attempting to fetch currencies from Odoo...');

      // Check if we have Odoo credentials
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final String odooUrl = prefs.getString('odooUrl') ?? '';
      final String database = prefs.getString('database') ?? '';

      if (odooUrl.isEmpty || database.isEmpty) {
        print('No Odoo credentials found, skipping Odoo currency fetch');
        return false;
      }

      // Define fields to fetch for currencies
      final fields = [
        'id', 'name', 'symbol', 'full_name', 'rate', 'active',
        'position', 'currency_unit_label', 'currency_subunit_label',
        'decimal_places', 'rounding', 'company_id'
      ];

      // Fetch active currencies from Odoo
      final result = await OdooClient.instance.searchRead(
        'res.currency',
        [['active', '=', true]],
        fields
      );

      if (result.isEmpty) {
        print('No currencies found in Odoo');
        return false;
      }

      print('Found ${result.length} currencies in Odoo');

      // Save currencies to the database
      for (var currencyData in result) {
        try {
          // Create currency data from Odoo data
          final currency = ResCurrencyTableData(
            id: 0, // Local ID will be assigned by Drift
            name: currencyData['name'] ?? '',
            symbol: currencyData['symbol'],
            full_name: currencyData['full_name'],
            rate: currencyData['rate'] is int ? currencyData['rate'].toDouble() : currencyData['rate'],
            active: currencyData['active'] is bool ? (currencyData['active'] ? 1 : 0) : currencyData['active'],
            position: currencyData['position'],
            currency_unit_label: currencyData['currency_unit_label']?.toString(),
            currency_subunit_label: currencyData['currency_subunit_label']?.toString(),
            decimal_places: currencyData['decimal_places'],
            rounding: currencyData['rounding'],
            universal_id: currencyData['id'],
            is_synced: true,
            origin_id: currencyData['id'],
            version: 1,
            is_confirmed: true,
            is_deleted: false,
          );

          // Create companion for saving
          final companion = ResCurrencyTableCompanion(
            id: Value(currency.id),
            name: Value(currency.name),
            symbol: Value(currency.symbol),
            full_name: Value(currency.full_name),
            rate: Value(currency.rate),
            active: Value(currency.active),
            position: Value(currency.position),
            currency_unit_label: Value(currency.currency_unit_label),
            currency_subunit_label: Value(currency.currency_subunit_label),
            decimal_places: Value(currency.decimal_places),
            rounding: Value(currency.rounding),
            universal_id: Value(currency.universal_id),
            is_synced: Value(currency.is_synced),
            origin_id: Value(currency.origin_id),
            version: Value(currency.version),
            is_confirmed: Value(currency.is_confirmed),
            is_deleted: Value(currency.is_deleted),
          );

          // Save to database
          await ref.read(resCurrencyRepositoryProvider).save(companion);

          print('Saved currency: ${currency.name}');
        } catch (e) {
          print('Error saving currency from Odoo: $e');
        }
      }

      return true;
    } catch (e) {
      print('Error fetching currencies from Odoo: $e');
      return false;
    }
  }

  /// Create default currencies if Odoo sync fails
  Future<void> _createDefaultCurrencies() async {
    print('Creating default currencies...');

    // Define standard currencies
    final defaultCurrencies = [
      // _createCurrency('USD', '\$', 'US Dollar', 1.0, 2),
      // _createCurrency('EUR', '€', 'Euro', 0.85, 2),
      // _createCurrency('GBP', '£', 'British Pound', 0.75, 2),
      // _createCurrency('JPY', '¥', 'Japanese Yen', 110.0, 0),
      // _createCurrency('CAD', 'C\$', 'Canadian Dollar', 1.25, 2),
      // _createCurrency('AUD', 'A\$', 'Australian Dollar', 1.35, 2),
      // _createCurrency('CHF', 'Fr', 'Swiss Franc', 0.92, 2),
      // _createCurrency('CNY', '¥', 'Chinese Yuan', 6.45, 2),
      // _createCurrency('INR', '₹', 'Indian Rupee', 74.5, 2),
      // _createCurrency('BRL', 'R\$', 'Brazilian Real', 5.2, 2),
    ];

    // Save each currency to the database
    for (var currency in defaultCurrencies) {
      try {
        await ref.read(resCurrencyRepositoryProvider).save(currency);
        print('Created default currency: ${currency.name.value}');
      } catch (e) {
        print('Error creating default currency: $e');
      }
    }
  }

  /// Helper method to create a currency companion
  ResCurrencyTableCompanion _createCurrency(
    String code,
    String symbol,
    String fullName,
    double rate,
    int decimalPlaces
  ) {
    return ResCurrencyTableCompanion(
      name: Value(code),
      symbol: Value(symbol),
      full_name: Value(fullName),
      rate: Value(rate),
      decimal_places: Value(decimalPlaces),
      active: const Value(true),
      position: const Value(""), // 0 = before amount, 1 = after amount
      rounding: const Value(1),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Ensure USD is set as the default currency
  // Future<void> _ensureDefaultCurrency() async {
  //   try {
  //     print('Ensuring USD is set as the default currency...');

  //     // Get USD currency from database
  //     final usdCurrency = await ref.read(resCurrencyRepositoryProvider).getBySymbol('\$');

  //     if (usdCurrency == null) {
  //       print('USD currency not found in database');
  //       return;
  //     }

  //     // Set USD as the active currency in SharedPreferences
  //     SharedPreferences prefs = await SharedPreferences.getInstance();
  //     await prefs.setString(UserPreference.activeCurrency, 'USD');

  //     print('USD set as the default currency');
  //   } catch (e) {
  //     print('Error setting USD as default currency: $e');
  //   }
  // }
}
