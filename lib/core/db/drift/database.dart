import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import 'tables/account_move_table.dart';
import 'tables/account_move_line_table.dart';
import 'tables/account_payment_table.dart';
import 'tables/account_tax_table.dart';
import 'tables/res_company_table.dart';
import 'tables/res_partner_table.dart';
import 'tables/product_product_table.dart';
import 'tables/product_category_table.dart';
import 'tables/res_currency_table.dart';
import 'package:invoicer/core/models/drift/ResCompanyExtensions.dart';

part 'database.g.dart';

@DriftDatabase(
  tables: [
    AccountMoveTable,
    AccountMoveLineTable,
    AccountPaymentTable,
    AccountTaxTable,
    ResCompanyTable,
    ResPartnerTable,
    ProductProductTable,
    ProductCategoryTable,
    ResCurrencyTable,
  ],
  daos: [
    AccountMoveDao,
    AccountMoveLineDao,
    AccountPaymentDao,
    AccountTaxDao,
    ResCompanyDao,
    ResPartnerDao,
    ProductProductDao,
    ProductCategoryDao,
    ResCurrencyDao,
  ],
)

/// The main database class for the application.
/// IMPORTANT: Do not instantiate this class directly. Use the DatabaseService singleton
/// to access the database instance to avoid race conditions.
/// See database_service.dart for the singleton implementation.
class AppDatabase extends _$AppDatabase {
  AppDatabase([QueryExecutor? executor]) : super(executor ?? _openConnection());

  @override
  int get schemaVersion => 1;

  // Tables are automatically generated by Drift

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle future migrations here
      },
    );
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    // Use the application documents directory instead of external storage
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'invoicer.db'));
    return NativeDatabase(file);
  });
}

// DAOs (Data Access Objects)
@DriftAccessor(tables: [AccountMoveTable, AccountMoveLineTable, AccountPaymentTable])
class AccountMoveDao extends DatabaseAccessor<AppDatabase> with _$AccountMoveDaoMixin {
  AccountMoveDao(AppDatabase db) : super(db);

  // Get all invoices
  Future<List<AccountMoveTableData>> getAllInvoices() => select(accountMoveTable).get();

  // Get invoice by id
  Future<AccountMoveTableData?> getInvoiceById(int id) {
    return (select(accountMoveTable)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  // Get invoice by universal id
  Future<AccountMoveTableData?> getInvoiceByuniversal_id(int universal_id) {
    return (select(accountMoveTable)..where((tbl) => tbl.universal_id.equals(universal_id))).getSingleOrNull();
  }

  // Get invoices ready for sync
  Future<List<AccountMoveTableData>> getInvoicesForSync() {
    return (select(accountMoveTable)
      ..where((tbl) => tbl.is_synced.equals(false) & tbl.is_deleted.equals(false)))
      .get();
  }

  // Get unsynced invoices for Odoo sync
  Future<List<AccountMoveTableData>> getUnsyncedInvoices() {
    return (select(accountMoveTable)
      ..where((tbl) => tbl.is_synced.equals(false) & tbl.is_deleted.equals(false)))
      .get();
  }

  // Get invoices for a specific business
  Future<List<AccountMoveTableData>> getInvoicesForBusiness(int businessId) {
    return (select(accountMoveTable)
      ..where((tbl) => tbl.company_id.equals(businessId)))
      .get();
  }

  // Get invoices for a specific client
  Future<List<AccountMoveTableData>> getInvoicesForClient(int clientId) {
    return (select(accountMoveTable)
      ..where((tbl) => tbl.partner_id.equals(clientId)))
      .get();
  }

  // Get invoices by type
  Future<List<AccountMoveTableData>> getInvoicesByType(String type) {
    return (select(accountMoveTable)
      ..where((tbl) => tbl.move_type.equals(type)))
      .get();
  }

  // Get invoices by status
  Future<List<AccountMoveTableData>> getInvoicesByStatus(String status) {
    return (select(accountMoveTable)
      ..where((tbl) => tbl.state.equals(status)))
      .get();
  }

  // Insert or update invoice
  Future<int> insertOrUpdateInvoice(AccountMoveTableCompanion invoice) async {
    if (invoice.id == Value.absent() || invoice.id.value == 0) {
      // If the invoice ID is 0, it means we are inserting a new invoice
      // Use Value.absent() to let the database auto-generate the ID
      return await into(accountMoveTable).insert(
        invoice.copyWith(id: const Value.absent())
      );
    }

    // For existing invoices, use update instead of insertOnConflictUpdate
    // insertOnConflictUpdate has issues returning the correct ID for updates
    final existingId = invoice.id.value;

    // Perform the update
    final updateCount = await (update(accountMoveTable)
      ..where((tbl) => tbl.id.equals(existingId)))
      .write(invoice);

    // Return the original ID since the update was successful
    if (updateCount > 0) {
      return existingId;
    } else {
      // If no rows were updated, the record might not exist
      // Try to insert it instead
      return await into(accountMoveTable).insert(invoice);
    }
  }

  // Get invoice with related data (lines and payments)
  Future<InvoiceWithRelations> getInvoiceWithRelations(int id) async {
    final invoice = await getInvoiceById(id);
    if (invoice == null) {
      throw Exception('Invoice not found');
    }

    final lines = await (select(accountMoveLineTable)
      ..where((tbl) => tbl.move_id.equals(id)))
      .get();

    final payments = await (select(accountPaymentTable)
      ..where((tbl) => tbl.move_id.equals(id)))
      .get();

    return InvoiceWithRelations(
      invoice: invoice,
      lines: lines,
      payments: payments,
    );
  }

  // Soft delete invoice
  Future<bool> softDeleteInvoice(int id) async {
    return update(accountMoveTable)
      .replace(AccountMoveTableCompanion(
        id: Value(id),
        is_deleted: const Value(true),
      ));
  }

  // Search invoices by query
  Future<List<AccountMoveTableData>> searchInvoices(String query) {
    return (select(accountMoveTable)
      ..where((tbl) =>
        tbl.name.like('%$query%') |
        tbl.narration.like('%$query%')
      ))
      .get();
  }

  // Advanced query with filtering, sorting, and pagination
  Future<List<AccountMoveTableData>> getFilteredInvoices({
    String? move_type,
    String? status,
    int? clientId,
    int? company_id,
    String? startDate,
    String? endDate,
    String? searchQuery,
    required bool descending,
    required int limit,
    required int offset,
  }) {
    final query = select(accountMoveTable);

    // Apply filters
    Expression<bool> whereClause = const Constant(true);

    // Filter by company
    if (company_id != null) {
      whereClause = whereClause & accountMoveTable.company_id.equals(company_id);
    }

    // Filter by move type
    if (move_type != null && move_type.isNotEmpty) {
      whereClause = whereClause & accountMoveTable.move_type.equals(move_type);
    }

    // Filter by status
    if (status != null && status.isNotEmpty) {
      whereClause = whereClause & accountMoveTable.state.equals(status.toLowerCase());
    }

    // Filter by client
    if (clientId != null) {
      whereClause = whereClause & accountMoveTable.partner_id.equals(clientId);
    }

    // Filter by date range
    if (startDate != null && endDate != null) {
      whereClause = whereClause &
        accountMoveTable.invoice_date.isBiggerOrEqualValue(startDate) &
        accountMoveTable.invoice_date.isSmallerOrEqualValue(endDate);
    }

    // Apply search query
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final searchTerm = '%$searchQuery%';
      whereClause = whereClause & (
        accountMoveTable.name.like(searchTerm) |
        accountMoveTable.narration.like(searchTerm)
      );
    }

    // Apply where clause
    query.where((tbl) => whereClause);

    // Apply sorting
    if (descending) {
      query.orderBy([(t) => OrderingTerm.desc(t.invoice_date)]);
    } else {
      query.orderBy([(t) => OrderingTerm.asc(t.invoice_date)]);
    }

    // Apply pagination
    query.limit(limit, offset: offset);

    return query.get();
  }

  // Count total invoices matching filters (for pagination)
  Future<int> countFilteredInvoices({
    String? move_type,
    String? status,
    int? clientId,
    int? company_id,
    String? startDate,
    String? endDate,
    String? searchQuery,
  }) async {
    final query = selectOnly(accountMoveTable)..addColumns([accountMoveTable.id.count()]);

    // Apply filters
    Expression<bool> whereClause = const Constant(true);

    // Filter by company
    if (company_id != null) {
      whereClause = whereClause & accountMoveTable.company_id.equals(company_id);
    }

    // Filter by move type
    if (move_type != null && move_type.isNotEmpty) {
      whereClause = whereClause & accountMoveTable.move_type.equals(move_type);
    }

    // Filter by status
    if (status != null && status.isNotEmpty) {
      whereClause = whereClause & accountMoveTable.state.equals(status.toLowerCase());
    }

    // Filter by client
    if (clientId != null) {
      whereClause = whereClause & accountMoveTable.partner_id.equals(clientId);
    }

    // Filter by date range
    if (startDate != null && endDate != null) {
      whereClause = whereClause &
        accountMoveTable.invoice_date.isBiggerOrEqualValue(startDate) &
        accountMoveTable.invoice_date.isSmallerOrEqualValue(endDate);
    }

    // Apply search query
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final searchTerm = '%$searchQuery%';
      whereClause = whereClause & (
        accountMoveTable.name.like(searchTerm) |
        accountMoveTable.narration.like(searchTerm)
      );
    }

    // Apply where clause
    query.where(whereClause);

    final result = await query.getSingle();
    return result.read(accountMoveTable.id.count()) ?? 0;
  }
}

@DriftAccessor(tables: [AccountMoveLineTable, ProductProductTable])
class AccountMoveLineDao extends DatabaseAccessor<AppDatabase> with _$AccountMoveLineDaoMixin {
  AccountMoveLineDao(AppDatabase db) : super(db);

  // Get all lines for an invoice (excluding deleted lines)
  Future<List<AccountMoveLineTableData>> getLinesForInvoice(int move_id) {
    return (select(accountMoveLineTable)
      ..where((tbl) => tbl.move_id.equals(move_id) & tbl.is_deleted.equals(false)))
      .get();
  }

  // Get line by universal id
  Future<AccountMoveLineTableData?> getLineByuniversal_id(int universal_id) {
    return (select(accountMoveLineTable)..where((tbl) => tbl.universal_id.equals(universal_id))).getSingleOrNull();
  }

  // Insert or update line
  Future<int> insertOrUpdateLine(AccountMoveLineTableCompanion line) {
    if (line.id == const Value.absent() || line.id.value == 0) {
      // If the line ID is absent or 0, it means we are inserting a new line
      // Use Value.absent() to let the database auto-generate the ID
      return into(accountMoveLineTable).insert(
        line.copyWith(id: const Value.absent())
      );
    }
    return into(accountMoveLineTable).insertOnConflictUpdate(line);
  }

  // Get line with product
  Future<AccountMoveLineWithProduct> getLineWithProduct(int lineId) async {
    final line = await (select(accountMoveLineTable)..where((tbl) => tbl.id.equals(lineId))).getSingleOrNull();
    if (line == null) {
      throw Exception('Invoice line not found');
    }

    final product = line.product_id == null ? null :
      await (select(productProductTable)..where((tbl) => tbl.id.equals(line.product_id!))).getSingleOrNull();

    return AccountMoveLineWithProduct(
      line: line,
      product: product,
    );
  }
}

@DriftAccessor(tables: [AccountPaymentTable])
class AccountPaymentDao extends DatabaseAccessor<AppDatabase> with _$AccountPaymentDaoMixin {
  AccountPaymentDao(AppDatabase db) : super(db);

  // Get all payments for an invoice (excluding deleted payments)
  Future<List<AccountPaymentTableData>> getPaymentsForInvoice(int move_id) {
    return (select(accountPaymentTable)
      ..where((tbl) => tbl.move_id.equals(move_id) & tbl.is_deleted.equals(false)))
      .get();
  }

  // Insert or update payment
  Future<int> insertOrUpdatePayment(AccountPaymentTableCompanion payment) {
    if (payment.id == const Value.absent() || payment.id.value == 0) {
      // If the payment ID is absent or 0, it means we are inserting a new payment
      // Use Value.absent() to let the database auto-generate the ID
      return into(accountPaymentTable).insert(
        payment.copyWith(id: const Value.absent())
      );
    }
    return into(accountPaymentTable).insertOnConflictUpdate(payment);
  }
}

@DriftAccessor(tables: [AccountTaxTable])
class AccountTaxDao extends DatabaseAccessor<AppDatabase> with _$AccountTaxDaoMixin {
  AccountTaxDao(AppDatabase db) : super(db);

  /// Get all active taxes
  Future<List<AccountTaxTableData>> getAllActiveTaxes() {
    return (select(accountTaxTable)
      ..where((t) => t.active.equals(true) & t.is_deleted.equals(false))
      ..orderBy([(t) => OrderingTerm(expression: t.sequence)]))
        .get();
  }

  /// Get taxes by type (sale, purchase, none)
  Future<List<AccountTaxTableData>> getTaxesByType(String type) {
    return (select(accountTaxTable)
      ..where((t) => t.type_tax_use.equals(type) & t.active.equals(true) & t.is_deleted.equals(false))
      ..orderBy([(t) => OrderingTerm(expression: t.sequence)]))
        .get();
  }

  /// Get taxes for a specific company
  Future<List<AccountTaxTableData>> getTaxesForCompany(int companyId) {
    return (select(accountTaxTable)
      ..where((t) => t.company_id.equals(companyId) & t.active.equals(true) & t.is_deleted.equals(false))
      ..orderBy([(t) => OrderingTerm(expression: t.sequence)]))
        .get();
  }

  /// Get tax by ID
  Future<AccountTaxTableData?> getTaxById(int id) {
    return (select(accountTaxTable)..where((t) => t.id.equals(id))).getSingleOrNull();
  }

  /// Get taxes by IDs
  Future<List<AccountTaxTableData>> getTaxesByIds(List<int> ids) {
    if (ids.isEmpty) return Future.value([]);
    return (select(accountTaxTable)
      ..where((t) => t.id.isIn(ids) & t.is_deleted.equals(false)))
        .get();
  }

  /// Create a new tax
  Future<int> createTax(AccountTaxTableCompanion tax) {
    return into(accountTaxTable).insert(tax);
  }

  /// Update a tax
  Future<bool> updateTax(int id, AccountTaxTableCompanion tax) async {
    final result = await (update(accountTaxTable)..where((t) => t.id.equals(id))).write(tax);
    return result > 0;
  }

  /// Soft delete a tax
  Future<bool> deleteTax(int id) async {
    final result = await (update(accountTaxTable)..where((t) => t.id.equals(id)))
        .write(const AccountTaxTableCompanion(is_deleted: Value(true)));
    return result > 0;
  }

  /// Get sale taxes for company
  Future<List<AccountTaxTableData>> getSaleTaxesForCompany(int companyId) {
    return (select(accountTaxTable)
      ..where((t) =>
        t.company_id.equals(companyId) &
        t.type_tax_use.equals('sale') &
        t.active.equals(true) &
        t.is_deleted.equals(false))
      ..orderBy([(t) => OrderingTerm(expression: t.sequence)]))
        .get();
  }

  /// Get purchase taxes for company
  Future<List<AccountTaxTableData>> getPurchaseTaxesForCompany(int companyId) {
    return (select(accountTaxTable)
      ..where((t) =>
        t.company_id.equals(companyId) &
        t.type_tax_use.equals('purchase') &
        t.active.equals(true) &
        t.is_deleted.equals(false))
      ..orderBy([(t) => OrderingTerm(expression: t.sequence)]))
        .get();
  }

  /// Insert or update tax
  Future<int> insertOrUpdateTax(AccountTaxTableCompanion tax) {
    if (tax.id == const Value.absent() || tax.id.value == 0) {
      return into(accountTaxTable).insert(
        tax.copyWith(id: const Value.absent())
      );
    }
    return into(accountTaxTable).insertOnConflictUpdate(tax);
  }

  /// Mark tax as synced
  Future<bool> markTaxAsSynced(int id, int universalId) async {
    final result = await (update(accountTaxTable)..where((t) => t.id.equals(id)))
        .write(AccountTaxTableCompanion(
          is_synced: const Value(true),
          universal_id: Value(universalId),
        ));
    return result > 0;
  }
}

@DriftAccessor(tables: [ResCompanyTable, ResCurrencyTable])
class ResCompanyDao extends DatabaseAccessor<AppDatabase> with _$ResCompanyDaoMixin {
  ResCompanyDao(AppDatabase db) : super(db);

  // Get all companies
  Future<List<ResCompanyTableData>> getAllCompanies() => select(resCompanyTable).get();

  // Get company by id
  Future<ResCompanyTableData?> getCompanyById(int id) {
    return (select(resCompanyTable)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  // Get company by universal id
  Future<ResCompanyTableData?> getCompanyByuniversal_id(int universal_id) {
    return (select(resCompanyTable)..where((tbl) => tbl.universal_id.equals(universal_id))).getSingleOrNull();
  }

  // Insert or update company
  Future<int> insertOrUpdateCompany(ResCompanyTableCompanion company) {
    if (company.id == const Value.absent() || company.id.value == 0) {
      // If the company ID is absent or 0, it means we are inserting a new company
      // Use Value.absent() to let the database auto-generate the ID
      return into(resCompanyTable).insert(
        company.copyWith(id: const Value.absent())
      );
    }
    return into(resCompanyTable).insertOnConflictUpdate(company);
  }

  // Custom method: Get active companies (not deleted)
  Future<List<ResCompanyTableData>> getActiveCompanies() {
    return (select(resCompanyTable)
      ..where((tbl) => tbl.is_deleted.equals(false)))
      .get();
  }

  // Custom method: Get companies with valid email
  Future<List<ResCompanyTableData>> getCompaniesWithValidEmail() {
    return (select(resCompanyTable)
      ..where((tbl) => tbl.email.isNotNull() & tbl.email.like('%@%')))
      .get();
  }

  // Custom method: Get companies with contact information
  Future<List<ResCompanyTableData>> getCompaniesWithContactInfo() {
    return (select(resCompanyTable)
      ..where((tbl) =>
          tbl.phone.isNotNull() |
          tbl.email.isNotNull() |
          tbl.website.isNotNull()))
      .get();
  }

  // Custom method: Get companies ordered by name
  Future<List<ResCompanyTableData>> getCompaniesOrderedByName() {
    return (select(resCompanyTable)
      ..orderBy([(t) => OrderingTerm(expression: t.name)]))
      .get();
  }

  // Custom method: Get parent companies (not subsidiaries)
  Future<List<ResCompanyTableData>> getParentCompanies() {
    return (select(resCompanyTable)
      ..where((tbl) => tbl.parent_id.isNull()))
      .get();
  }

  // Custom method: Get subsidiary companies for a parent
  Future<List<ResCompanyTableData>> getSubsidiaries(int parent_id) {
    return (select(resCompanyTable)
      ..where((tbl) => tbl.parent_id.equals(parent_id)))
      .get();
  }

  // Custom method: Get company with its currency
  Future<CompanyWithCurrency?> getCompanyWithCurrency(int company_id) async {
    final company = await getCompanyById(company_id);
    if (company == null || company.currency_id == null) {
      return company != null ? CompanyWithCurrency(company: company, currency: null) : null;
    }

    final currency = await (select(attachedDatabase.resCurrencyTable)
      ..where((tbl) => tbl.id.equals(company.currency_id!)))
      .getSingleOrNull();

    return CompanyWithCurrency(
      company: company,
      currency: currency,
    );
  }

  // Custom method: Search companies by name, email, phone, or VAT
  Future<List<ResCompanyTableData>> searchCompanies(String query) {
    final searchTerm = '%$query%';
    return (select(resCompanyTable)
      ..where((tbl) =>
          tbl.name.like(searchTerm) |
          tbl.email.like(searchTerm) |
          tbl.phone.like(searchTerm) |
          tbl.vat.like(searchTerm)))
      .get();
  }

  // Custom method: Get formatted address for a company
  Future<String?> getFormattedAddress(int company_id) async {
    final company = await getCompanyById(company_id);
    if (company == null) {
      return null;
    }

    // Use the extension method from res_company_extensions.dart
    return company.getFormattedAddress();
  }

  // Custom method: Create a new company with default values
  Future<int> createCompany(String name, {
    String? street,
    String? city,
    String? email,
    String? phone,
  }) {
    // Use the extension method from res_company_extensions.dart
    final company = ResCompanyTableCompanionExtensions.createActive(
      name: name,
      street: street,
      city: city,
      email: email,
      phone: phone,
    );

    return insertOrUpdateCompany(company);
  }

  // Custom method: Mark a company as deleted
  Future<bool> markAsDeleted(int id) async {
    final company = await getCompanyById(id);
    if (company == null) {
      return false;
    }

    // Create a companion with only the fields we want to update
    final companion = ResCompanyTableCompanion(
      id: Value(id),
      is_deleted: const Value(true),
    );

    return await into(resCompanyTable).insertOnConflictUpdate(companion) > 0;
  }

  // Custom method: Mark a company as active
  Future<bool> markAsActive(int id) async {
    final company = await getCompanyById(id);
    if (company == null) {
      return false;
    }

    // Create a companion with only the fields we want to update
    final companion = ResCompanyTableCompanion(
      id: Value(id),
      is_deleted: const Value(false),
    );

    return await into(resCompanyTable).insertOnConflictUpdate(companion) > 0;
  }

  // Custom method: Get companies with logos
  Future<List<ResCompanyTableData>> getCompaniesWithLogos() {
    return (select(resCompanyTable)
      ..where((tbl) => tbl.logo.isNotNull()))
      .get();
  }

  // Get unsynced companies for Odoo sync
  Future<List<ResCompanyTableData>> getUnsyncedCompanies() {
    return (select(resCompanyTable)
      ..where((tbl) => tbl.is_synced.equals(false) & tbl.is_deleted.equals(false)))
      .get();
  }
}

@DriftAccessor(tables: [ResPartnerTable, ResCompanyTable])
class ResPartnerDao extends DatabaseAccessor<AppDatabase> with _$ResPartnerDaoMixin {
  ResPartnerDao(AppDatabase db) : super(db);

  // Get all partners
  Future<List<ResPartnerTableData>> getAllPartners() => select(resPartnerTable).get();

  // Get partner by id
  Future<ResPartnerTableData?> getPartnerById(int id) {
    return (select(resPartnerTable)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  // Get partner by universal id
  Future<ResPartnerTableData?> getPartnerByuniversal_id(int universal_id) {
    return (select(resPartnerTable)..where((tbl) => tbl.universal_id.equals(universal_id))).getSingleOrNull();
  }

  // Insert or update partner
  Future<int> insertOrUpdatePartner(ResPartnerTableCompanion partner) {
    if (partner.id == const Value.absent() || partner.id.value == 0) {
      // If the partner ID is absent or 0, it means we are inserting a new partner
      // Use Value.absent() to let the database auto-generate the ID
      return into(resPartnerTable).insert(
        partner.copyWith(id: const Value.absent())
      );
    }
    return into(resPartnerTable).insertOnConflictUpdate(partner);
  }

  // Get partner with company
  Future<ResPartnerWithCompany> getPartnerWithCompany(int partner_id) async {
    final partner = await getPartnerById(partner_id);
    if (partner == null) {
      throw Exception('Partner not found');
    }

    // For now, return without company since we need to update the table definition
    return ResPartnerWithCompany(
      partner: partner,
      company: null,
    );
  }

  // Get unsynced partners for Odoo sync
  Future<List<ResPartnerTableData>> getUnsyncedPartners() {
    return (select(resPartnerTable)
      ..where((tbl) => tbl.is_synced.equals(false) & tbl.is_deleted.equals(false)))
      .get();
  }
}

@DriftAccessor(tables: [ProductProductTable, ProductCategoryTable])
class ProductProductDao extends DatabaseAccessor<AppDatabase> with _$ProductProductDaoMixin {
  ProductProductDao(AppDatabase db) : super(db);

  // Get all products
  Future<List<ProductProductTableData>> getAllProducts() => select(productProductTable).get();

  // Get product by id
  Future<ProductProductTableData?> getProductById(int id) {
    return (select(productProductTable)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  // Get product by universal id
  Future<ProductProductTableData?> getProductByuniversal_id(int universal_id) {
    return (select(productProductTable)..where((tbl) => tbl.universal_id.equals(universal_id))).getSingleOrNull();
  }

  // Insert or update product
  Future<int> insertOrUpdateProduct(ProductProductTableCompanion product) {
    if (product.id == const Value.absent() || product.id.value == 0) {
      // If the product ID is absent or 0, it means we are inserting a new product
      // Use Value.absent() to let the database auto-generate the ID
      return into(productProductTable).insert(
        product.copyWith(id: const Value.absent())
      );
    }
    return into(productProductTable).insertOnConflictUpdate(product);
  }

  // Get product with category
  Future<ProductWithCategory> getProductWithCategory(int product_id) async {
    final product = await getProductById(product_id);
    if (product == null) {
      throw Exception('Product not found');
    }

    // For now, return without category since we need to update the table definition
    return ProductWithCategory(
      product: product,
      category: null,
    );
  }

  // Get unsynced products for Odoo sync
  Future<List<ProductProductTableData>> getUnsyncedProducts() {
    return (select(productProductTable)
      ..where((tbl) => tbl.is_synced.equals(false) & tbl.is_deleted.equals(false)))
      .get();
  }
}

@DriftAccessor(tables: [ProductCategoryTable])
class ProductCategoryDao extends DatabaseAccessor<AppDatabase> with _$ProductCategoryDaoMixin {
  ProductCategoryDao(AppDatabase db) : super(db);

  // Get all categories
  Future<List<ProductCategoryTableData>> getAllCategories() => select(productCategoryTable).get();

  // Get category by id
  Future<ProductCategoryTableData?> getCategoryById(int id) {
    return (select(productCategoryTable)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  // Get category by universal id
  Future<ProductCategoryTableData?> getCategoryByuniversal_id(int universal_id) {
    return (select(productCategoryTable)..where((tbl) => tbl.universal_id.equals(universal_id))).getSingleOrNull();
  }

  // Get unsynced categories for Odoo sync
  Future<List<ProductCategoryTableData>> getUnsyncedCategories() {
    return (select(productCategoryTable)
      ..where((tbl) => tbl.is_synced.equals(false) & tbl.is_deleted.equals(false)))
      .get();
  }

  // Insert or update category
  Future<int> insertOrUpdateCategory(ProductCategoryTableCompanion category) {
    if (category.id == const Value.absent() || category.id.value == 0) {
      // If the category ID is absent or 0, it means we are inserting a new category
      // Use Value.absent() to let the database auto-generate the ID
      return into(productCategoryTable).insert(
        category.copyWith(id: const Value.absent())
      );
    }
    return into(productCategoryTable).insertOnConflictUpdate(category);
  }
}

@DriftAccessor(tables: [ResCurrencyTable])
class ResCurrencyDao extends DatabaseAccessor<AppDatabase> with _$ResCurrencyDaoMixin {
  ResCurrencyDao(AppDatabase db) : super(db);

  // Get all currencies (excluding deleted ones)
  Future<List<ResCurrencyTableData>> getAllCurrencies() {
    return (select(resCurrencyTable)
      ..where((tbl) => tbl.is_deleted.equals(false)))
      .get();
  }

  // Get active currencies (excluding deleted and inactive ones)
  Future<List<ResCurrencyTableData>> getActiveCurrencies() {
    return (select(resCurrencyTable)
      ..where((tbl) => tbl.is_deleted.equals(false) & tbl.active.equals(true)))
      .get();
  }

  // Get currency by id
  Future<ResCurrencyTableData?> getCurrencyById(int id) {
    return (select(resCurrencyTable)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  // Get currency by universal id
  Future<ResCurrencyTableData?> getCurrencyByuniversal_id(int universal_id) {
    return (select(resCurrencyTable)..where((tbl) => tbl.universal_id.equals(universal_id))).getSingleOrNull();
  }

  // Get unsynced currencies for Odoo sync
  Future<List<ResCurrencyTableData>> getUnsyncedCurrencies() {
    return (select(resCurrencyTable)
      ..where((tbl) => tbl.is_synced.equals(false) & tbl.is_deleted.equals(false)))
      .get();
  }

  // Insert or update currency
  Future<int> insertOrUpdateCurrency(ResCurrencyTableCompanion currency) {
    if (currency.id == const Value.absent() || currency.id.value == 0) {
      // If the currency ID is absent or 0, it means we are inserting a new currency
      // Use Value.absent() to let the database auto-generate the ID
      return into(resCurrencyTable).insert(
        currency.copyWith(id: const Value.absent())
      );
    }
    return into(resCurrencyTable).insertOnConflictUpdate(currency);
  }
}

// Data classes for related entities
class InvoiceWithRelations {
  final AccountMoveTableData invoice;
  final List<AccountMoveLineTableData> lines;
  final List<AccountPaymentTableData> payments;

  InvoiceWithRelations({
    required this.invoice,
    required this.lines,
    required this.payments,
  });
}

class AccountMoveLineWithProduct {
  final AccountMoveLineTableData line;
  final ProductProductTableData? product;

  AccountMoveLineWithProduct({
    required this.line,
    this.product,
  });
}

class CompanyWithCurrency {
  final ResCompanyTableData company;
  final ResCurrencyTableData? currency;

  CompanyWithCurrency({
    required this.company,
    this.currency,
  });
}

class ResPartnerWithCompany {
  final ResPartnerTableData partner;
  final ResCompanyTableData? company;

  ResPartnerWithCompany({
    required this.partner,
    this.company,
  });
}

class ProductWithCategory {
  final ProductProductTableData product;
  final ProductCategoryTableData? category;

  ProductWithCategory({
    required this.product,
    this.category,
  });
}
