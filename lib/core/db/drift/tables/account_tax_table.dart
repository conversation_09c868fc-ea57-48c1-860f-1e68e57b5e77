import 'package:drift/drift.dart';
import 'res_company_table.dart' as company;

class AccountTaxTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic fields
  TextColumn get name => text()();
  TextColumn get description => text().nullable()();
  
  // Tax computation
  TextColumn get amount_type => text().withDefault(const Constant('percent'))(); // 'percent', 'fixed', 'group', 'code'
  RealColumn get amount => real().withDefault(const Constant(0.0))();
  
  // Tax type and scope
  TextColumn get type_tax_use => text().withDefault(const Constant('sale'))(); // 'sale', 'purchase', 'none'
  TextColumn get tax_scope => text().nullable()(); // 'service', 'consu', null for all
  
  // Advanced options
  BoolColumn get price_include => boolean().withDefault(const Constant(false))();
  BoolColumn get include_base_amount => boolean().withDefault(const Constant(false))();
  BoolColumn get is_base_affected => boolean().withDefault(const Constant(false))();
  BoolColumn get analytic => boolean().withDefault(const Constant(false))();
  
  // Display and grouping
  IntColumn get sequence => integer().withDefault(const Constant(1))();
  BoolColumn get active => boolean().withDefault(const Constant(true))();
  TextColumn get invoice_label => text().nullable()();
  IntColumn get tax_group_id => integer().nullable()();
  
  // Company relationship
  IntColumn get company_id => integer().nullable().references(company.ResCompanyTable, #id)();
  
  // Repartition and accounts (simplified for now)
  TextColumn get invoice_repartition_line_ids => text().nullable()(); // JSON array of repartition lines
  TextColumn get refund_repartition_line_ids => text().nullable()(); // JSON array of repartition lines
  
  // Children taxes for groups
  TextColumn get children_tax_ids => text().nullable()(); // JSON array of child tax IDs
  
  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();
}
