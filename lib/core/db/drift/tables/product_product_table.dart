import 'package:drift/drift.dart';
import 'res_company_table.dart' as company;
import 'table_references.dart';

class ProductProductTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic fields
  TextColumn get name => text().nullable()();
  TextColumn get sequence => text().nullable()();
  TextColumn get description => text().nullable()();
  TextColumn get description_purchase => text().nullable()();
  TextColumn get description_sale => text().nullable()();
  IntColumn get categ_id => integer().nullable()(); // Will be linked to ProductCategoryTable

  // Price and quantity fields
  RealColumn get list_price => real().nullable()();
  RealColumn get standard_price => real().nullable()();
  RealColumn get volume => real().nullable()();
  RealColumn get weight => real().nullable()();
  IntColumn get sale_ok => integer().nullable()();
  BoolColumn get purchase_ok => boolean().nullable()();
  BoolColumn get active => boolean().nullable()();

  // Appearance and identification
  TextColumn get color => text().nullable()();
  TextColumn get default_code => text().nullable()();
  TextColumn get barcode => text().nullable()();
  IntColumn get company_id => integer().nullable().references(company.ResCompanyTable, #id)();
  TextColumn get type => text().nullable()();
  IntColumn get uom_id => integer().nullable()();
  IntColumn get uom_po_id => integer().nullable()();

  // Image fields
  TextColumn get image_1920 => text().nullable()();
  TextColumn get image_1024 => text().nullable()();
  TextColumn get image_512 => text().nullable()();
  TextColumn get image_256 => text().nullable()();
  TextColumn get image_128 => text().nullable()();

  // Stock fields
  RealColumn get qty_available => real().nullable()();
  RealColumn get virtual_available => real().nullable()();
  RealColumn get incoming_qty => real().nullable()();
  RealColumn get outgoing_qty => real().nullable()();
  IntColumn get featured => integer().nullable()();
  IntColumn get stock => integer().nullable()();

  // Tax fields - following Odoo's product.template model
  TextColumn get taxes_id => text().nullable()(); // Customer taxes (JSON array of tax IDs)
  TextColumn get supplier_taxes_id => text().nullable()(); // Supplier taxes (JSON array of tax IDs)

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();


}


