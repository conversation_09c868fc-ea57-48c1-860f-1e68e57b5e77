import 'package:drift/drift.dart';
import 'res_company_table.dart';

class ResCurrencyTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic fields
  TextColumn get name => text()();
  TextColumn get symbol => text().nullable()();
  TextColumn get full_name => text().nullable()();
  RealColumn get rate => real().nullable()();
  IntColumn get decimal_places => integer().nullable()();
  BoolColumn get active => boolean().nullable()();
  TextColumn get position => text().nullable()();
  TextColumn get currency_unit_label => text().nullable()();
  TextColumn get currency_subunit_label => text().nullable()();
  RealColumn get rounding => real().nullable()();
  // IntColumn get company_id => integer().nullable().references(ResCompanyTable, #id)();

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();


}
