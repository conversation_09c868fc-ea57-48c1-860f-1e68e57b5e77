import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/tables/product_product_table.dart';
import 'package:invoicer/core/db/drift/tables/res_partner_table.dart' show ResPartnerTable;
import 'account_move_table.dart' as move;

class AccountMoveLineTable extends Table {
  // Primary key with constraint to prevent negative values
  IntColumn get id => integer().autoIncrement()();

  // Foreign key to invoice
  IntColumn get move_id => integer().nullable().references(move.AccountMoveTable, #id)();

  // Basic fields
  TextColumn get name => text().nullable()();
  IntColumn get product_id => integer().nullable().references(ProductProductTable, #id)();
  IntColumn get account_id => integer().nullable()();
  IntColumn get partner_id => integer().nullable().references(ResPartnerTable, #id)();

  // Quantity and price fields
  RealColumn get quantity => real().nullable()();
  RealColumn get price_unit => real().nullable()();
  RealColumn get discount => real().nullable()();
  RealColumn get price_subtotal => real().nullable()();
  RealColumn get price_total => real().nullable()();

  // Accounting fields
  RealColumn get balance => real().nullable()();
  RealColumn get amount_currency => real().nullable()();
  RealColumn get debit => real().nullable()();
  RealColumn get credit => real().nullable()();
  IntColumn get currency_id => integer().nullable()();
  IntColumn get tax_base_amount => integer().nullable()();
  IntColumn get tax_line_id => integer().nullable()();
  IntColumn get tax_group_id => integer().nullable()();
  TextColumn get tax_ids => text().nullable()();

  // Date fields
  TextColumn get date => text().nullable()();
  TextColumn get date_maturity => text().nullable()();

  // Company fields
  IntColumn get company_id => integer().nullable()();
  IntColumn get company_currency_id => integer().nullable()();

  // Display fields
  IntColumn get sequence => integer().nullable()();
  TextColumn get display_type => text().nullable()();

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();


}


