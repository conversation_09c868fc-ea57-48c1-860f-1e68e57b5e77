import 'package:drift/drift.dart';
import 'res_company_table.dart' as company;

class ProductCategoryTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic fields
  TextColumn get name => text().nullable()();
  TextColumn get complete_name => text().nullable()();
  IntColumn get parent_id => integer().nullable()(); // Self-reference to ProductCategoryTable
  TextColumn get parent_path => text().nullable()();
  IntColumn get sequence => integer().nullable()();
  TextColumn get description => text().nullable()();
  IntColumn get company_id => integer().nullable().references(company.ResCompanyTable, #id)();

  // Property fields
  IntColumn get removal_strategy_id => integer().nullable()();
  IntColumn get property_cost_method => integer().nullable()();
  IntColumn get property_valuation => integer().nullable()();
  IntColumn get property_account_income_category_id => integer().nullable()();
  IntColumn get property_account_expense_category_id => integer().nullable()();
  IntColumn get property_stock_account_input_category_id => integer().nullable()();
  IntColumn get property_stock_account_output_category_id => integer().nullable()();
  IntColumn get property_stock_valuation_account_id => integer().nullable()();
  IntColumn get property_stock_journal => integer().nullable()();

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();


}
