// import 'package:drift/drift.dart';

// // This file contains forward references for tables to avoid circular dependencies

// class ResPartnerTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }

// class ResCompanyTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }

// class ProductCategoryTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }

// class ProductProductTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }

// class AccountMoveTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }

// class AccountMoveLineTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }

// class AccountPaymentTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }

// class ResCurrencyTable extends Table {
//   IntColumn get id => integer().autoIncrement()();
// }
