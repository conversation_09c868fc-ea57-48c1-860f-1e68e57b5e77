import 'package:intl/intl.dart';

const useTestUrl = false;

// final String testBaseUrl = 'http://********:8765/';
// final String testBaseUrl = 'http://localhost:8765/';
final String testBaseUrl = 'https://api.invoicer.kanjan.co.zw/';
final String liveBaseUrl = 'https://api.invoicer.kanjan.co.zw/';

final String baseUrl = useTestUrl ? testBaseUrl : liveBaseUrl;
const String invoicerService = 'invoicer-service/api/v1/'; // worklink-api-test // worklink-api-live
const String invoicerSyncService = 'invoicer-service/api/v1/sync'; // worklink-api-test // worklink-api-live
const String authService = 'oauth-service/';
const String userService = 'user-service/';

// Odoo Authentication Constants
const String odooJsonRpcEndpoint = '/jsonrpc';
bool strictWeb = false;
DateFormat dateFormat = DateFormat("yyyy-MM-dd");
DateFormat dateTimeFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
/// loader state enum
enum Loader {
  None,
  Loading,
  Complete,
  Error,
}

enum RequestState {
  None,
  Loaded,
  Error,
  Failed,
}
