import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ProductCategoryExtensions.dart';

abstract class ProductCategoryRepository {
  Future<List<ProductCategoryTableData>> getAll();
  Future<ProductCategoryTableData?> getById(int id);
  Future<ProductCategoryTableData?> getByuniversal_id(int universal_id);
  Future<List<ProductCategoryTableData>> getForParent(int parent_id);
  Future<int> save(ProductCategoryTableCompanion category);
  Future<bool> delete(int id);
  Future<List<ProductCategoryTableData>> search(String query);
  Future<ProductCategoryWithParent> getWithParent(int id);
}
