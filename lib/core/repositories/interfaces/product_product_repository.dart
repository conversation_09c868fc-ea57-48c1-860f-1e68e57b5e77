import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ProductProductExtensions.dart';

abstract class ProductProductRepository {
  Future<List<ProductProductTableData>> getAll();
  Future<ProductProductTableData?> getById(int id);
  Future<ProductProductTableData?> getByuniversal_id(int universal_id);
  Future<List<ProductProductTableData>> getForCategory(int categoryId);
  Future<List<ProductProductTableData>> getForCompany(int company_id);
  Future<int> save(ProductProductTableCompanion product);
  Future<bool> delete(int id);
  Future<List<ProductProductTableData>> search(String query);
  Future<ProductProductWithRelations> getWithRelations(int id);
}
