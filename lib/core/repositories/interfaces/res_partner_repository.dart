import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ResPartnerExtensions.dart';

abstract class ResPartnerRepository {
  Future<List<ResPartnerTableData>> getAll();
  Future<ResPartnerTableData?> getById(int id);
  Future<ResPartnerTableData?> getByuniversal_id(int universal_id);
  Future<List<ResPartnerTableData>> getCustomers();
  Future<List<ResPartnerTableData>> getSuppliers();
  Future<List<ResPartnerTableData>> getForCompany(int company_id);
  Future<int> save(ResPartnerTableCompanion partner);
  Future<bool> delete(int id);
  Future<List<ResPartnerTableData>> search(String query);
  Future<ResPartnerWithRelations> getWithRelations(int id);
}
