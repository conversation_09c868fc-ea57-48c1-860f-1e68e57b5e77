import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ResPartnerExtensions.dart';
import 'package:invoicer/core/repositories/interfaces/res_partner_repository.dart';

class ResPartnerRepositoryDrift implements ResPartnerRepository {
  final AppDatabase _db;
  final ResPartnerDao _resPartnerDao;

  ResPartnerRepositoryDrift(this._db)
      : _resPartnerDao = _db.resPartnerDao;

  @override
  Future<List<ResPartnerTableData>> getAll() async {
    return _resPartnerDao.getAllPartners();
  }

  @override
  Future<ResPartnerTableData?> getById(int id) async {
    return _resPartnerDao.getPartnerById(id);
  }

  @override
  Future<ResPartnerTableData?> getByuniversal_id(int universal_id) async {
    return _resPartnerDao.getPartnerByuniversal_id(universal_id);
  }

  @override
  Future<List<ResPartnerTableData>> getCustomers() async {
    return (_db.select(_db.resPartnerTable)
      ..where((tbl) => tbl.customer_rank.isBiggerThan(Constant(0))))
      .get();
  }

  @override
  Future<List<ResPartnerTableData>> getSuppliers() async {
    return (_db.select(_db.resPartnerTable)
      ..where((tbl) => tbl.supplier_rank.isBiggerThan(Constant(0))))
      .get();
  }

  @override
  Future<List<ResPartnerTableData>> getForCompany(int company_id) async {
    return (_db.select(_db.resPartnerTable)).get();
  }

  @override
  Future<int> save(ResPartnerTableCompanion partner) async {
    return _resPartnerDao.insertOrUpdatePartner(partner);
  }

  @override
  Future<bool> delete(int id) async {
    return await (_db.update(_db.resPartnerTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const ResPartnerTableCompanion(
        is_deleted: Value(true),
      )) > 0;
  }

  @override
  Future<List<ResPartnerTableData>> search(String query) async {
    return (_db.select(_db.resPartnerTable)
      ..where((tbl) =>
        tbl.name.like('%$query%') |
        tbl.email.like('%$query%') |
        tbl.phone.like('%$query%') |
        tbl.mobile.like('%$query%')
      ))
      .get();
  }

  @override
  Future<ResPartnerWithRelations> getWithRelations(int id) async {
    final partnerWithCompany = await _resPartnerDao.getPartnerWithCompany(id);

    // Get parent if available
    ResPartnerTableData? parent;
    if (partnerWithCompany.partner.parent_id != null) {
      parent = await _resPartnerDao.getPartnerById(partnerWithCompany.partner.parent_id!);
    }

    return ResPartnerWithRelations(
      partner: partnerWithCompany.partner,
      company: partnerWithCompany.company,
      parent: parent,
    );
  }
}
