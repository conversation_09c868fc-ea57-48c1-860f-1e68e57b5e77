import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveLineExtensions.dart' as extensions;
import 'package:invoicer/core/repositories/drift/account_move_repository_drift.dart';
import 'package:invoicer/core/repositories/interfaces/account_move_line_repository.dart';

class AccountMoveLineRepositoryDrift implements AccountMoveLineRepository {
  final AppDatabase _db;
  final AccountMoveLineDao _accountMoveLineDao;
  late final AccountMoveRepositoryDrift _accountMoveRepository;

  AccountMoveLineRepositoryDrift(this._db)
      : _accountMoveLineDao = _db.accountMoveLineDao {
    _accountMoveRepository = AccountMoveRepositoryDrift(_db);
  }

  @override
  Future<List<AccountMoveLineTableData>> getAll() async {
    return _db.select(_db.accountMoveLineTable).get();
  }

  @override
  Future<AccountMoveLineTableData?> getById(int id) async {
    return (_db.select(_db.accountMoveLineTable)
      ..where((tbl) => tbl.id.equals(id)))
      .getSingleOrNull();
  }

  @override
  Future<List<AccountMoveLineTableData>> getForInvoice(int invoiceId) async {
    return _accountMoveLineDao.getLinesForInvoice(invoiceId);
  }

  @override
  Future<int> save(AccountMoveLineTableCompanion line) async {
    final id = await _accountMoveLineDao.insertOrUpdateLine(line);

    // Get the move_id (invoice id) for this line item
    if (line.move_id.present && line.move_id.value != null) {
      // Update the invoice totals
      await _accountMoveRepository.updateInvoiceTotals(line.move_id.value!);
    } else {
      // If move_id is not present, get the line item to find its move_id
      final savedLine = await getById(id);
      if (savedLine != null && savedLine.move_id != null) {
        await _accountMoveRepository.updateInvoiceTotals(savedLine.move_id!);
      }
    }

    return id;
  }

  @override
  Future<bool> delete(int id) async {
    // Get the line item to find its move_id before deleting
    final lineItem = await getById(id);
    final moveId = lineItem?.move_id;

    // Mark the line as deleted
    final result = await (_db.update(_db.accountMoveLineTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const AccountMoveLineTableCompanion(
        is_deleted: Value(true),
      )) > 0;

    // Update the invoice totals if we have a move_id
    if (result && moveId != null) {
      await _accountMoveRepository.updateInvoiceTotals(moveId);
    }

    return result;
  }

  @override
  Future<extensions.AccountMoveLineWithProduct> getWithProduct(int id) async {
    final result = await _accountMoveLineDao.getLineWithProduct(id);
    // Convert from database.AccountMoveLineWithProduct to extensions.AccountMoveLineWithProduct
    return extensions.AccountMoveLineWithProduct(
      line: result.line,
      product: result.product,
    );
  }
}
