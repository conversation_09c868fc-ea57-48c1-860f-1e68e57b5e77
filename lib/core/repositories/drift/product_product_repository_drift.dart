import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ProductProductExtensions.dart';
import 'package:invoicer/core/repositories/interfaces/product_product_repository.dart';

class ProductProductRepositoryDrift implements ProductProductRepository {
  final AppDatabase _db;
  final ProductProductDao _productProductDao;

  ProductProductRepositoryDrift(this._db)
      : _productProductDao = _db.productProductDao;

  @override
  Future<List<ProductProductTableData>> getAll() async {
    print("DEBUG: ProductRepository.getAll called");

    // Get all products without filters for debugging
    final allProductsRaw = await _db.select(_db.productProductTable).get();
    print("DEBUG: Total products in database (no filters): ${allProductsRaw.length}");

    // Only return products synced from Odoo
    final result = await (_db.select(_db.productProductTable)
      ..where((tbl) =>
        tbl.universal_id.isNotNull() & // Only products synced from Odoo
        tbl.is_deleted.equals(false) // Only non-deleted products
      ))
      .get();

    print("DEBUG: Products after filtering (synced & not deleted): ${result.length}");
    return result;
  }

  @override
  Future<ProductProductTableData?> getById(int id) async {
    return _productProductDao.getProductById(id);
  }

  @override
  Future<ProductProductTableData?> getByuniversal_id(int universal_id) async {
    return _productProductDao.getProductByuniversal_id(universal_id);
  }

  @override
  Future<List<ProductProductTableData>> getForCategory(int categoryId) async {
    return (_db.select(_db.productProductTable)
      ..where((tbl) => tbl.categ_id.equals(categoryId)))
      .get();
  }

  @override
  Future<List<ProductProductTableData>> getForCompany(int company_id) async {
    print("DEBUG: ProductRepository.getForCompany called with company_id: $company_id");

    final result = await (_db.select(_db.productProductTable)
      ..where((tbl) =>
        tbl.company_id.equals(company_id) &
        tbl.universal_id.isNotNull() & // Only products synced from Odoo
        tbl.is_deleted.equals(false) // Only non-deleted products
      ))
      .get();

    print("DEBUG: ProductRepository.getForCompany returning ${result.length} products");
    return result;
  }

  @override
  Future<int> save(ProductProductTableCompanion product) async {
    return _productProductDao.insertOrUpdateProduct(product);
  }

  @override
  Future<bool> delete(int id) async {
    return await (_db.update(_db.productProductTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const ProductProductTableCompanion(
        is_deleted: Value(true),
      )) > 0;
  }

  @override
  Future<List<ProductProductTableData>> search(String query) async {
    return (_db.select(_db.productProductTable)
      ..where((tbl) =>
        (tbl.name.like('%$query%') |
        tbl.default_code.like('%$query%') |
        tbl.barcode.like('%$query%')) &
        tbl.universal_id.isNotNull() & // Only products synced from Odoo
        tbl.is_deleted.equals(false) // Only non-deleted products
      ))
      .get();
  }

  @override
  Future<ProductProductWithRelations> getWithRelations(int id) async {
    final product = await _productProductDao.getProductById(id);
    if (product == null) {
      throw Exception('Product not found');
    }

    // Get category if available
    ProductCategoryTableData? category;
    if (product.categ_id != null) {
      category = await _db.productCategoryDao.getCategoryById(product.categ_id!);
    }

    // Get company if available
    ResCompanyTableData? company;
    if (product.company_id != null) {
      company = await _db.resCompanyDao.getCompanyById(product.company_id!);
    }

    return ProductProductWithRelations(
      product: product,
      category: category,
      company: company,
    );
  }
}
