import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ProductCategoryExtensions.dart';
import 'package:invoicer/core/repositories/interfaces/product_category_repository.dart';

class ProductCategoryRepositoryDrift implements ProductCategoryRepository {
  final AppDatabase _db;
  final ProductCategoryDao _productCategoryDao;

  ProductCategoryRepositoryDrift(this._db)
      : _productCategoryDao = _db.productCategoryDao;

  @override
  Future<List<ProductCategoryTableData>> getAll() async {
    return _productCategoryDao.getAllCategories();
  }

  @override
  Future<ProductCategoryTableData?> getById(int id) async {
    return _productCategoryDao.getCategoryById(id);
  }

  @override
  Future<ProductCategoryTableData?> getByuniversal_id(int universal_id) async {
    return _productCategoryDao.getCategoryByuniversal_id(universal_id);
  }

  @override
  Future<List<ProductCategoryTableData>> getForParent(int parent_id) async {
    return (_db.select(_db.productCategoryTable)
      ..where((tbl) => tbl.parent_id.equals(parent_id)))
      .get();
  }

  @override
  Future<int> save(ProductCategoryTableCompanion category) async {
    return _productCategoryDao.insertOrUpdateCategory(category);
  }

  @override
  Future<bool> delete(int id) async {
    return await (_db.update(_db.productCategoryTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const ProductCategoryTableCompanion(
        is_deleted: Value(true),
      )) > 0;
  }

  @override
  Future<List<ProductCategoryTableData>> search(String query) async {
    return (_db.select(_db.productCategoryTable)
      ..where((tbl) =>
        tbl.name.like('%$query%') |
        tbl.complete_name.like('%$query%')
      ))
      .get();
  }

  @override
  Future<ProductCategoryWithParent> getWithParent(int id) async {
    final category = await _productCategoryDao.getCategoryById(id);
    if (category == null) {
      throw Exception('Category not found');
    }

    // Get parent if available
    ProductCategoryTableData? parent;
    if (category.parent_id != null) {
      parent = await _productCategoryDao.getCategoryById(category.parent_id!);
    }

    return ProductCategoryWithParent(
      category: category,
      parent: parent,
    );
  }
}
