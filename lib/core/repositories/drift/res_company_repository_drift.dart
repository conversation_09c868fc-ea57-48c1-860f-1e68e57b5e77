import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/interfaces/res_company_repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';

/// Model class for a company with its currency
class CompanyWithCurrencyModel {
  final ResCompanyTableData company;
  final ResCurrencyTableData? currency;

  CompanyWithCurrencyModel({
    required this.company,
    this.currency,
  });
}

class ResCompanyRepositoryDrift implements ResCompanyRepository {
  final AppDatabase _db;
  final ResCompanyDao _resCompanyDao;

  ResCompanyRepositoryDrift(this._db)
      : _resCompanyDao = _db.resCompanyDao;

  @override
  Future<List<ResCompanyTableData>> getAll() async {
    return _resCompanyDao.getAllCompanies();
  }

  @override
  Future<ResCompanyTableData?> getById(int id) async {
    return _resCompanyDao.getCompanyById(id);
  }

  @override
  Future<ResCompanyTableData?> getByuniversal_id(int universal_id) async {
    return _resCompanyDao.getCompanyByuniversal_id(universal_id);
  }

  @override
  Future<int> save(ResCompanyTableCompanion company) async {
    // Save the company
    final savedId = await _resCompanyDao.insertOrUpdateCompany(company);

    // Check if there's an active business
    final prefs = await SharedPreferences.getInstance();
    final activeBusinessId = prefs.getInt(UserPreference.activeBusiness);

    // If there's no active business, set this one as active
    if (activeBusinessId == null) {
      // Set as active business
      prefs.setInt(UserPreference.activeBusiness, savedId);

      // Also save the business name if available
      final savedBusiness = await _resCompanyDao.getCompanyById(savedId);
      if (savedBusiness != null && savedBusiness.name.isNotEmpty) {
        prefs.setString(UserPreference.activeBusinessName, savedBusiness.name);
      }
    }

    return savedId;
  }

  @override
  Future<bool> delete(int id) async {
    return await (_db.update(_db.resCompanyTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const ResCompanyTableCompanion(
        is_deleted: Value(true),
      )) > 0;
  }

  @override
  Future<List<ResCompanyTableData>> search(String query) async {
    return _resCompanyDao.searchCompanies(query);
  }

  // New methods that use the custom DAO methods

  /// Get active companies (not deleted)
  Future<List<ResCompanyTableData>> getActiveCompanies() async {
    return _resCompanyDao.getActiveCompanies();
  }

  /// Get companies with valid email addresses
  Future<List<ResCompanyTableData>> getCompaniesWithValidEmail() async {
    return _resCompanyDao.getCompaniesWithValidEmail();
  }

  /// Get companies with any contact information (phone, email, website)
  Future<List<ResCompanyTableData>> getCompaniesWithContactInfo() async {
    return _resCompanyDao.getCompaniesWithContactInfo();
  }

  /// Get companies ordered by name
  Future<List<ResCompanyTableData>> getCompaniesOrderedByName() async {
    return _resCompanyDao.getCompaniesOrderedByName();
  }

  /// Get parent companies (not subsidiaries)
  Future<List<ResCompanyTableData>> getParentCompanies() async {
    return _resCompanyDao.getParentCompanies();
  }

  /// Get subsidiary companies for a parent
  Future<List<ResCompanyTableData>> getSubsidiaries(int parent_id) async {
    return _resCompanyDao.getSubsidiaries(parent_id);
  }

  /// Get company with its currency
  Future<CompanyWithCurrencyModel?> getCompanyWithCurrency(int company_id) async {
    final result = await _resCompanyDao.getCompanyWithCurrency(company_id);
    if (result == null) {
      return null;
    }

    return CompanyWithCurrencyModel(
      company: result.company,
      currency: result.currency,
    );
  }

  /// Get formatted address for a company
  Future<String?> getFormattedAddress(int company_id) async {
    return _resCompanyDao.getFormattedAddress(company_id);
  }
}
