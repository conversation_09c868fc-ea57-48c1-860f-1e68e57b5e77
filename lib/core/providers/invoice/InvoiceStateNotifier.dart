import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/constants.dart'; 
import 'package:invoicer/core/models/drift/AccountMoveWithItems.dart';
import 'package:invoicer/core/providers/invoice/InvoicesRequest.dart';
import 'package:invoicer/core/providers/invoice/PaginatedAccountMoveTableData.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';

import '../../exceptions/custom_exception.dart';
import 'InvoicesState.dart';

// Define the provider with autoDispose to clean up resources when no longer needed
final invoicesNotifierProvider =
    StateNotifierProvider.autoDispose<InvoicesStateNotifier, InvoicesState>(
  (ref) => InvoicesStateNotifier(ref),
);

class InvoicesStateNotifier extends StateNotifier<InvoicesState> {
  InvoicesStateNotifier(this.ref) : super(InvoicesState.initial());
  final Ref ref;

  // Reset the state to initial
  void resetState() {
    state = InvoicesState.initial();
    debugPrint("Invoice state has been reset.");
  }

  // Get invoices based on the provided request
  Future<void> getInvoices(InvoicesRequest req) async {
    // Set state to loading to show loading indicator
    state = InvoicesState.loading();

    try {
      final result = await _fetchInvoicesFromLocalDb(req);

      // Create paginated result with proper values
      PaginatedAccountMoveTableData paginatedInvoices = PaginatedAccountMoveTableData(
        content: result,
        totalItems: await ref.read(accountMoveRepositoryProvider).countFilteredInvoices(
          move_type: _getmove_typeFromRequestType(req.type),
          status: req.invoice_status,
          clientId: req.client_id != null ? int.tryParse(req.client_id!) : null,
          company_id: await SharedPreferences.getInstance().then((prefs) => prefs.getInt(UserPreference.activeBusiness)),
          startDate: req.start_date,
          endDate: req.end_date,
          searchQuery: null,
        ),
        offset: req.page_number * req.page_size,
        itemCount: result.length,
        page_number: req.page_number,
      );

      // Update state with the fetched data
      state = InvoicesState.data(invoices: paginatedInvoices);
      debugPrint("Invoice state has been updated with ${result.length} invoices.");
    } catch (e,st) {
      print('$e \n$st');
      // Handle errors
      if (e is CustomException) {
        state = InvoicesState.error(e.message);
      } else {
        state = InvoicesState.error(e.toString());
      }
      debugPrint("Error fetching invoices: ${e.toString()}");
    }
  }

  // Fetch invoices from local database
  Future<List<AccountMoveWithItems>> _fetchInvoicesFromLocalDb(InvoicesRequest req) async {
    List<AccountMoveWithItems> invoices = [];

    try {
      // Map app types to Odoo move_types
      var move_type = _getmove_typeFromRequestType(req.type);

      // Get active business ID
      var prefs = await SharedPreferences.getInstance();
      var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

      if (activeBusiness == null) {
        throw Exception("Go to settings and create or select active business.");
      }

      // Calculate pagination parameters
      final limit = req.page_size;
      final offset = req.page_number * req.page_size;

      // Get total count for pagination
      await ref.read(accountMoveRepositoryProvider).countFilteredInvoices(
        move_type: move_type,
        status: req.invoice_status,
        clientId: req.client_id != null ? int.tryParse(req.client_id!) : null,
        company_id: activeBusiness,
        startDate: req.start_date,
        endDate: req.end_date,
        searchQuery: null, // Search query is handled separately
      );

      // Get filtered invoices with pagination
      final filteredInvoices = await ref.read(accountMoveRepositoryProvider).getFilteredInvoices(
        move_type: move_type,
        status: req.invoice_status,
        clientId: req.client_id != null ? int.tryParse(req.client_id!) : null,
        company_id: activeBusiness,
        startDate: req.start_date,
        endDate: req.end_date,
        searchQuery: null, // Search query is handled separately
        descending: req.date_sort, // true = descending, false = ascending
        limit: limit,
        offset: offset,
      );

      // Add filtered invoices to the result list
      invoices.addAll(filteredInvoices);
    } catch (e,st) {
      print('$e \n$st');
      debugPrint("Error fetching invoices from local database: ${e.toString()}");
      rethrow;
    }

    return invoices;
  }

  // Map app types to Odoo move_types
  String _getmove_typeFromRequestType(String type) {
    switch (type) {
      case 'invoice':
        return 'out_invoice'; // Customer Invoice
      case 'bill':
        return 'in_invoice'; // Vendor Bill
      case 'credit':
        return 'out_refund'; // Customer Credit Note
      case 'debit':
        return 'in_refund'; // Vendor Credit Note
      default:
        return ''; // All types
    }
  }

  // Search invoices by query
  Future<void> searchInvoices(InvoicesRequest req, String query) async {
    if (query.isEmpty) {
      // If query is empty, just get invoices with the current request
      await getInvoices(req);
      return;
    }

    // Set state to loading
    state = InvoicesState.loading();

    try {
      List<AccountMoveWithItems> invoices = [];
      int totalItems = 0;

      if (kIsWeb || strictWeb) {

      } else {
        // Local database implementation using Drift
        // Get active business ID
        var prefs = await SharedPreferences.getInstance();
        var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

        if (activeBusiness == null) {
          throw Exception("Go to settings and create or select active business.");
        }

        // Map app types to Odoo move_types
        var move_type = req.type.isNotEmpty && req.type != "ALL"
            ? _getmove_typeFromRequestType(req.type)
            : null;

        // Get total count for pagination
        totalItems = await ref.read(accountMoveRepositoryProvider).countFilteredInvoices(
          move_type: move_type,
          status: req.invoice_status,
          clientId: req.client_id != null ? int.tryParse(req.client_id!) : null,
          company_id: activeBusiness,
          startDate: req.start_date,
          endDate: req.end_date,
          searchQuery: query,
        );

        // Get filtered invoices with pagination
        invoices = await ref.read(accountMoveRepositoryProvider).getFilteredInvoices(
          move_type: move_type,
          status: req.invoice_status,
          clientId: req.client_id != null ? int.tryParse(req.client_id!) : null,
          company_id: activeBusiness,
          startDate: req.start_date,
          endDate: req.end_date,
          searchQuery: query,
          descending: req.date_sort, // true = descending, false = ascending
          limit: req.page_size,
          offset: req.page_number * req.page_size,
        );
      }

      // Create paginated result
      PaginatedAccountMoveTableData paginatedInvoices = PaginatedAccountMoveTableData(
        content: invoices,
        totalItems: totalItems,
        offset: req.page_number * req.page_size,
        itemCount: invoices.length,
        page_number: req.page_number,
      );

      // Update state with search results
      state = InvoicesState.data(invoices: paginatedInvoices);
      debugPrint("Search completed with ${invoices.length} results for query: $query");
    } catch (e,st) {
      print('$e \n$st');
      // Handle errors
      if (e is CustomException) {
        state = InvoicesState.error(e.message);
      } else {
        state = InvoicesState.error(e.toString());
      }
      debugPrint("Error searching invoices: ${e.toString()}");
    }
  }
}
