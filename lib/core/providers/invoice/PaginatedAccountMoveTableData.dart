import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveWithItems.dart';

class PaginatedAccountMoveTableData {
  final List<AccountMoveWithItems> content;
  final int totalItems;
  final int offset;
  final int itemCount;
  final int page_number;

  PaginatedAccountMoveTableData({
    required this.content,
    required this.totalItems,
    required this.offset,
    required this.itemCount,
    required this.page_number,
  });
}
