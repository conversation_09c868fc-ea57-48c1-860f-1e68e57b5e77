// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'InvoicesRequest.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_InvoicesRequest _$InvoicesRequestFromJson(Map<String, dynamic> json) =>
    _InvoicesRequest(
      type: json['type'] as String? ?? "INVOICE",
      date_sort: json['date_sort'] as bool? ?? false,
      page_size: (json['page_size'] as num?)?.toInt() ?? 20,
      page_number: (json['page_number'] as num?)?.toInt() ?? 0,
      include_cancelled: json['include_cancelled'] as bool?,
      get_order: json['get_order'] as bool?,
      invoice_status: json['invoice_status'] as String?,
      client_id: json['client_id'] as String?,
      start_date: json['start_date'] as String?,
      end_date: json['end_date'] as String?,
    );

Map<String, dynamic> _$InvoicesRequestToJson(_InvoicesRequest instance) =>
    <String, dynamic>{
      'type': instance.type,
      'date_sort': instance.date_sort,
      'page_size': instance.page_size,
      'page_number': instance.page_number,
      'include_cancelled': instance.include_cancelled,
      'get_order': instance.get_order,
      'invoice_status': instance.invoice_status,
      'client_id': instance.client_id,
      'start_date': instance.start_date,
      'end_date': instance.end_date,
    };
