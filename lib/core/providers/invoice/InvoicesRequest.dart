
import 'package:freezed_annotation/freezed_annotation.dart';

part 'InvoicesRequest.freezed.dart';
part 'InvoicesRequest.g.dart';

@freezed
abstract class InvoicesRequest with _$InvoicesRequest {

  factory InvoicesRequest({
    @Default("INVOICE") String type,
    @Default(false) bool date_sort,
    @Default(20) int page_size,
    @Default(0) int page_number,
    bool? include_cancelled,
    bool? get_order,
    String? invoice_status,
    String? client_id,
    String? start_date,
    String? end_date,
  }) = _InvoicesRequest;

  factory InvoicesRequest.fromJson(Map<String, dynamic> json) =>
      _$InvoicesRequestFromJson(json);

}

