import 'package:invoicer/core/db/drift/database.dart';

class PaginatedProductProductTableData {
  final List<ProductProductTableData> content;
  final int totalItems;
  final int offset;
  final int itemCount;
  final int page_number;

  PaginatedProductProductTableData({
    required this.content,
    required this.totalItems,
    required this.offset,
    required this.itemCount,
    required this.page_number,
  });
}
