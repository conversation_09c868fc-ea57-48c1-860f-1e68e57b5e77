
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/product/ProductsRequest.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';

import 'PaginatedProductProductTableData.dart';
import 'ProductsState.dart';


final productAsyncController =
StateNotifierProvider.autoDispose<CounterAsyncNotifier, ProductsState>(
      (ref) => CounterAsyncNotifier(ref),
);


class CounterAsyncNotifier extends StateNotifier<ProductsState>{
  CounterAsyncNotifier(this.ref): super(ProductsState.initial());
  final Ref ref;

  void resetState() {
    state = ProductsState.initial();
    print("Product state has been reset.");
  }

  void getProducts(ProductsRequest req) async {
    state = ProductsState.loading();

    if(!(kIsWeb || strictWeb)){
      // Get active business ID
      var prefs = await SharedPreferences.getInstance();
      var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

      if (activeBusiness == null) {
        throw Exception("Go to settings and create or select active business.");
      }

      // Get all products for the company
      final allProducts = await ref.read(productProductRepositoryProvider).getForCompany(activeBusiness);

      // Sort products by name
      allProducts.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

      // Pagination
      int totalItems = allProducts.length;
      int startIndex = req.page_number * req.page_size;
      int endIndex = (startIndex + req.page_size) > allProducts.length
          ? allProducts.length
          : (startIndex + req.page_size);

      List<ProductProductTableData> pagedProducts = [];
      if (startIndex < allProducts.length) {
        pagedProducts = allProducts.sublist(startIndex, endIndex);
      }

      // Create paginated result
      PaginatedProductProductTableData products = PaginatedProductProductTableData(
        content: pagedProducts,
        totalItems: totalItems,
        offset: req.page_number * req.page_size,
        itemCount: pagedProducts.length,
        page_number: req.page_number,
      );

      state = ProductsState.data(products: products);
    }

    print("Product state has been updated.");
  }


}




