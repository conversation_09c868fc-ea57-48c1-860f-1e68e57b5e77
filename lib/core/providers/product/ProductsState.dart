import 'package:freezed_annotation/freezed_annotation.dart';

import 'PaginatedProductProductTableData.dart';


part 'ProductsState.freezed.dart';

// extension method for easy comparison
extension ProductGetters on ProductsState {
  bool get isLoading => this is _ProductsStateLoading;
  bool get isError=> this is _ProductsStateError;
}

@freezed
class ProductsState with _$ProductsState {
  /// initial
  factory ProductsState.initial() = _ProductsStateInitial;

  /// loading
  factory ProductsState.loading() = _ProductsStateLoading;

  /// data
  factory ProductsState.data({required PaginatedProductProductTableData products}) =
      _ProductsStateData;

  /// other data different from products
  factory ProductsState.loaded([@Default(0) dynamic data]) = _ProductsStateLoaded;

  /// Error
  factory ProductsState.error([String? error]) = _ProductsStateError;
}
