import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/user_preference.dart';
import 'paginated_product_product_table_data.dart';
import 'products_request.dart';

// Use the Drift-based repository for products
final productProvider = FutureProvider.autoDispose.family<ProductProductTableData?, int>((ref, id) {
  return ref.read(productProductRepositoryProvider).getById(id);
});

// Provider for paginated products
final productsProvider = FutureProvider.autoDispose.family<PaginatedProductProductTableData, ProductsRequest>((ref, req) async {
  // Get active business ID
  var prefs = await SharedPreferences.getInstance();
  var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

  if (activeBusiness == null) {
    throw Exception("Go to settings and create or select active business.");
  }

  if (kIsWeb || strictWeb) {
    // Web implementation would be handled here
    throw UnimplementedError("Web implementation not available in this version");
  } else {
    // Local database implementation using Drift
    // Get all products for the company
    final products = await ref.read(productProductRepositoryProvider).getForCompany(activeBusiness);

    // Apply any additional filters from the request
    var filteredProducts = products;

    // Filter by category if needed (not implemented in the request yet)

    // Sort products by name (default)
    filteredProducts.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    // Pagination
    int totalItems = filteredProducts.length;
    int startIndex = req.page_number * req.page_size;
    int endIndex = (startIndex + req.page_size) > filteredProducts.length
        ? filteredProducts.length
        : (startIndex + req.page_size);

    List<ProductProductTableData> pagedProducts = [];
    if (startIndex < filteredProducts.length) {
      pagedProducts = filteredProducts.sublist(startIndex, endIndex);
    }

    // Create paginated result
    return PaginatedProductProductTableData(
      content: pagedProducts,
      totalItems: totalItems,
      offset: req.page_number * req.page_size,
      itemCount: pagedProducts.length,
      page_number: req.page_number,
    );
  }
});

// Provider for product dashboard data
final getProductDashProvider = FutureProvider.autoDispose.family<List<double>, int>((ref, _) async {
  if (kIsWeb || strictWeb) {
    throw UnimplementedError("Web implementation not available in this version");
  } else {
    // Get active business ID
    var prefs = await SharedPreferences.getInstance();
    var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

    if (activeBusiness == null) {
      throw Exception("Go to settings and create or select active business.");
    }

    // Get all products for the company
    final products = await ref.read(productProductRepositoryProvider).getForCompany(activeBusiness);

    // Calculate dashboard metrics
    double totalProducts = products.length.toDouble();
    double activeProducts = products.where((p) => p.active == 1).length.toDouble();
    double totalValue = 0.0;

    for (var product in products) {
      if (product.list_price != null && product.qty_available != null) {
        totalValue += product.list_price! * product.qty_available!;
      }
    }

    return [totalProducts, activeProducts, totalValue];
  }
});

// Provider for detailed product dashboard data
final getProductsDashProvider = FutureProvider.autoDispose.family<List<Map<String, dynamic>>, int>((ref, _) async {
  if (kIsWeb || strictWeb) {
    throw UnimplementedError("Web implementation not available in this version");
  } else {
    // Get active business ID
    var prefs = await SharedPreferences.getInstance();
    var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

    if (activeBusiness == null) {
      throw Exception("Go to settings and create or select active business.");
    }

    // Get all products for the company
    final products = await ref.read(productProductRepositoryProvider).getForCompany(activeBusiness);

    // Get all categories
    final categories = await ref.read(productCategoryRepositoryProvider).getAll();

    // Group products by category
    Map<int, List<ProductProductTableData>> productsByCategory = {};
    for (var product in products) {
      if (product.categ_id != null) {
        if (!productsByCategory.containsKey(product.categ_id)) {
          productsByCategory[product.categ_id!] = [];
        }
        productsByCategory[product.categ_id!]!.add(product);
      }
    }

    // Create dashboard data
    List<Map<String, dynamic>> dashboardData = [];

    for (var categoryId in productsByCategory.keys) {
      var categoryProducts = productsByCategory[categoryId]!;
      var category = categories.firstWhere(
        (c) => c.id == categoryId,
        orElse: () => ProductCategoryTableData(
          id: categoryId,
          name: 'Unknown',
          is_synced: false,
          is_confirmed: true,
          is_deleted: false,
          version: 1
        )
      );

      double totalValue = 0.0;
      for (var product in categoryProducts) {
        if (product.list_price != null && product.qty_available != null) {
          totalValue += product.list_price! * product.qty_available!;
        }
      }

      dashboardData.add({
        'category_id': categoryId,
        'category_name': category.name,
        'product_count': categoryProducts.length,
        'total_value': totalValue,
      });
    }

    // Sort by product count (descending)
    dashboardData.sort((a, b) => (b['product_count'] as int).compareTo(a['product_count'] as int));

    return dashboardData;
  }
});


