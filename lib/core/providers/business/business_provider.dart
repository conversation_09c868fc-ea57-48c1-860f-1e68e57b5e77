import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ResCompanyExtensions.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/core/network/dio_client.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';

// Use the Drift-based repository for businesses
final businessProvider = FutureProvider.autoDispose.family<ResCompanyTableData?, int>((ref, id) {
  return ref.read(resCompanyRepositoryProvider).getById(id);
});

// Provider for all businesses
final businessesProvider = FutureProvider<List<ResCompanyTableData>>((ref) async {
  if (kIsWeb || strictWeb) {
    // Web implementation
    var result = await DioClient.instance.get('$invoicerService/businesses');
    var res = result as List;

    // Convert the JSON data to ResCompanyTableData objects using our extension method
    return res.map((json) => ResCompanyTableData.fromJson(json)).toList();
  } else {
    // Local database implementation using Drift
    return ref.read(resCompanyRepositoryProvider).getAll();
  }
});

// Provider for active business name
final businessesNameProvider = FutureProvider<String?>((ref) async {
  var prefs = await SharedPreferences.getInstance();
  return await prefs.getString(UserPreference.activeBusinessName);
});

// Provider for active business ID
final activeBusinessIdProvider = FutureProvider<int>((ref) async {
  var prefs = await SharedPreferences.getInstance();
  var activeBusinessId = await prefs.getInt(UserPreference.activeBusiness);
  if (activeBusinessId == null) {
    throw Exception("Go to settings and create or select active business.");
  }
  return activeBusinessId;
});

// Provider for active business
final activeBusinessProvider = FutureProvider<ResCompanyTableData>((ref) async {
  final activeBusinessId = await ref.watch(activeBusinessIdProvider.future);
  final business = await ref.read(resCompanyRepositoryProvider).getById(activeBusinessId);
  if (business == null) {
    throw Exception("Active business not found. Go to settings and create or select active business.");
  }
  return business;
});
