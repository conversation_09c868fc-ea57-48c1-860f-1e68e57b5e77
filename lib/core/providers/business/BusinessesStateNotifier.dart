
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';

import '../../constants/constants.dart';
import 'BusinessesState.dart';

final businessAsyncController =
StateNotifierProvider.autoDispose<CounterAsyncNotifier, BusinessesState>(
      (ref) => CounterAsyncNotifier(ref),
);

class CounterAsyncNotifier extends StateNotifier<BusinessesState>{
  CounterAsyncNotifier(this.ref): super(BusinessesState.initial());
  final Ref ref;

  void resetState() {
    state = BusinessesState.initial();
    print("Company state has been reset.");
  }

  void getBusinesses() async {
    state = BusinessesState.loading();

    if(!(kIsWeb||strictWeb)){
      List<ResCompanyTableData> businesses = await ref.read(resCompanyRepositoryProvider).getAll();
      state = BusinessesState.data(businesss: businesses);
    }

    print("Company state has been updated.");
  }


}




