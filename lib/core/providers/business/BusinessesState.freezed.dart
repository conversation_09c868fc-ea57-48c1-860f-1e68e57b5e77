// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'BusinessesState.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BusinessesState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BusinessesState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BusinessesState()';
  }
}

/// @nodoc
class $BusinessesStateCopyWith<$Res> {
  $BusinessesStateCopyWith(
      BusinessesState _, $Res Function(BusinessesState) __);
}

/// @nodoc

class _BusinessesStateInitial implements BusinessesState {
  _BusinessesStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _BusinessesStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BusinessesState.initial()';
  }
}

/// @nodoc

class _BusinessesStateLoading implements BusinessesState {
  _BusinessesStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _BusinessesStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BusinessesState.loading()';
  }
}

/// @nodoc

class _BusinessesStateData implements BusinessesState {
  _BusinessesStateData({required final List<ResCompanyTableData> businesss})
      : _businesss = businesss;

  final List<ResCompanyTableData> _businesss;
  List<ResCompanyTableData> get businesss {
    if (_businesss is EqualUnmodifiableListView) return _businesss;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_businesss);
  }

  /// Create a copy of BusinessesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BusinessesStateDataCopyWith<_BusinessesStateData> get copyWith =>
      __$BusinessesStateDataCopyWithImpl<_BusinessesStateData>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BusinessesStateData &&
            const DeepCollectionEquality()
                .equals(other._businesss, _businesss));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_businesss));

  @override
  String toString() {
    return 'BusinessesState.data(businesss: $businesss)';
  }
}

/// @nodoc
abstract mixin class _$BusinessesStateDataCopyWith<$Res>
    implements $BusinessesStateCopyWith<$Res> {
  factory _$BusinessesStateDataCopyWith(_BusinessesStateData value,
          $Res Function(_BusinessesStateData) _then) =
      __$BusinessesStateDataCopyWithImpl;
  @useResult
  $Res call({List<ResCompanyTableData> businesss});
}

/// @nodoc
class __$BusinessesStateDataCopyWithImpl<$Res>
    implements _$BusinessesStateDataCopyWith<$Res> {
  __$BusinessesStateDataCopyWithImpl(this._self, this._then);

  final _BusinessesStateData _self;
  final $Res Function(_BusinessesStateData) _then;

  /// Create a copy of BusinessesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? businesss = null,
  }) {
    return _then(_BusinessesStateData(
      businesss: null == businesss
          ? _self._businesss
          : businesss // ignore: cast_nullable_to_non_nullable
              as List<ResCompanyTableData>,
    ));
  }
}

/// @nodoc

class _BusinessesStateLoaded implements BusinessesState {
  _BusinessesStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of BusinessesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BusinessesStateLoadedCopyWith<_BusinessesStateLoaded> get copyWith =>
      __$BusinessesStateLoadedCopyWithImpl<_BusinessesStateLoaded>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BusinessesStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'BusinessesState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$BusinessesStateLoadedCopyWith<$Res>
    implements $BusinessesStateCopyWith<$Res> {
  factory _$BusinessesStateLoadedCopyWith(_BusinessesStateLoaded value,
          $Res Function(_BusinessesStateLoaded) _then) =
      __$BusinessesStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$BusinessesStateLoadedCopyWithImpl<$Res>
    implements _$BusinessesStateLoadedCopyWith<$Res> {
  __$BusinessesStateLoadedCopyWithImpl(this._self, this._then);

  final _BusinessesStateLoaded _self;
  final $Res Function(_BusinessesStateLoaded) _then;

  /// Create a copy of BusinessesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_BusinessesStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _BusinessesStateError implements BusinessesState {
  _BusinessesStateError([this.error]);

  final String? error;

  /// Create a copy of BusinessesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BusinessesStateErrorCopyWith<_BusinessesStateError> get copyWith =>
      __$BusinessesStateErrorCopyWithImpl<_BusinessesStateError>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BusinessesStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'BusinessesState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$BusinessesStateErrorCopyWith<$Res>
    implements $BusinessesStateCopyWith<$Res> {
  factory _$BusinessesStateErrorCopyWith(_BusinessesStateError value,
          $Res Function(_BusinessesStateError) _then) =
      __$BusinessesStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$BusinessesStateErrorCopyWithImpl<$Res>
    implements _$BusinessesStateErrorCopyWith<$Res> {
  __$BusinessesStateErrorCopyWithImpl(this._self, this._then);

  final _BusinessesStateError _self;
  final $Res Function(_BusinessesStateError) _then;

  /// Create a copy of BusinessesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_BusinessesStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
