import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/types/syncresult.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../exceptions/custom_exception.dart';
import '../../models/interfaces/DriftSyncClass.dart';
import '../../utils/UserPreference.dart';
import 'data/sync_repository.dart';
import 'sync_state.dart';

class SyncNotifier extends StateNotifier<SyncState> {
  SyncNotifier({
    required ISyncRepository syncRepository,
  })  : _syncRepository = syncRepository,
        super(SyncState.initial());

  final ISyncRepository _syncRepository;

  void resetState() {
    if (mounted) state = SyncState.initial();
    print("Invoice state has been reset.");
  }
  // In case of sync conflict
  void resolveConflict(AccountMoveTableData invoice) {
    if (mounted) state = SyncState.data(sync: invoice);
    print("Invoice state has been reset.");
  }

  Future<bool> syncAll( fullSync) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var userId = (await prefs.getString(UserPreference.userId));
    final String odooUrl = prefs.getString('odooUrl') ?? '';
    final String database = prefs.getString('database') ?? '';

    try {
      // Check if we have the necessary credentials
      if (userId == null || odooUrl.isEmpty || database.isEmpty) {
        if (mounted) state = SyncState.error("Sign in with Odoo credentials to sync");
        return false;
      } else {
        // Define the entity types to sync in order
        final List<Map<String, String>> entityTypes = [
          {'type': 'businesses', 'displayName': 'Businesses'},
          {'type': 'clients', 'displayName': 'Clients'},
          {'type': 'categories', 'displayName': 'Categories'},
          {'type': 'products', 'displayName': 'Products'},
          {'type': 'currencies', 'displayName': 'Currencies'},
          {'type': 'invoices', 'displayName': 'Invoices'}
        ];

        // STAGE 1: Get data from Odoo
        if (mounted) state = SyncState.loaded("STAGE 1: Starting sync FROM Odoo (getting data)");
        print("STAGE 1: Starting sync FROM Odoo (getting data)");
        bool getRes = await this.getSync(fullSync);

        // If getting data from Odoo failed, stop the sync process
        if (!getRes) {
          print("STAGE 1 FAILED: Could not get data from Odoo");
          return false;
        }

        // STAGE 2: Post data to Odoo
        if (mounted) state = SyncState.loaded("STAGE 2: Starting sync TO Odoo (posting data)");
        print("STAGE 2: Starting sync TO Odoo (posting data)");

        // Initialize result objects
        SyncResult syncResult;
        Map<String, List<DriftSyncClass>> itemsToSync = {};

        // Get local data that needs to be synced to Odoo
        if (mounted) state = SyncState.loaded("Getting local data ready for sync to Odoo");
        print("Getting local data ready for sync to Odoo");

        // Fetch all data that needs to be synced
        for (var entity in entityTypes) {
          final entityType = entity['type']!;
          final displayName = entity['displayName']!;

          print("Fetching local $displayName for sync to Odoo");
          List<DriftSyncClass> items = await _syncRepository.getReadyForSync(entityType);
          itemsToSync[entityType] = items;
          print("Found ${items.length} $displayName ready for sync");
        }

        // Sync each entity type to Odoo sequentially
        // If any step fails, stop the sync process
        for (var entity in entityTypes) {
          final entityType = entity['type']!;
          final displayName = entity['displayName']!;
          final items = itemsToSync[entityType]!;

          if (items.isEmpty) {
            print("No $displayName to sync to Odoo");
            continue;
          }

          print("Syncing $displayName to Odoo (${items.length} items)");
          if (mounted) state = SyncState.loaded("Syncing $displayName to Odoo (${items.length} items)");

          syncResult = await _syncRepository.postSyncObjects(entityType, entityType, items);

          if (!syncResult.success) {
            final errorMessage = "Failed to sync $displayName: ${syncResult.message}";
            print(errorMessage);
            if (mounted) state = SyncState.error(errorMessage);
            return false;
          }

          print("Successfully synced $displayName to Odoo");
        }

        print("Sync to Odoo complete");

        // Update the last sync date
        DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
        await prefs.setString(UserPreference.lastSyncDate, dateFormat.format(DateTime.now()).toString());

        // All syncs were successful
        if (mounted) state = SyncState.loaded("Sync Complete");
        return true;
      }
    } catch (e,st) {
      print('$e \n$st');
      log("Sync error: ${e.toString()}");
      if (e is CustomException) {
        if (mounted) state = SyncState.error(e.message);
      } else {
        if (mounted) state = SyncState.error("Sync failed: ${e.toString()}");
      }
      return false;
    }
  }


  Future<bool> getSync( fullSync) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var userId = (await prefs.getString(UserPreference.userId));
    final String odooUrl = prefs.getString('odooUrl') ?? '';
    final String database = prefs.getString('database') ?? '';

    try {
      // Check if we have the necessary credentials
      if (userId == null || odooUrl.isEmpty || database.isEmpty) {
        if (mounted) state = SyncState.error("Sign in with Odoo credentials to sync");
        return false;
      } else {
        if (mounted) state = SyncState.loaded("Starting sync from Odoo");
        print("Starting sync from Odoo");

        // Initialize result objects
        SyncResult syncResult;

        // Define the entity types to sync in order
        final List<Map<String, String>> entityTypes = [
          {'type': 'currencies', 'displayName': 'Currencies'},
          {'type': 'businesses', 'displayName': 'Businesses'},
          {'type': 'clients', 'displayName': 'Clients'},
          {'type': 'categories', 'displayName': 'Categories'},
          {'type': 'products', 'displayName': 'Products'},
          {'type': 'invoices', 'displayName': 'Invoices'}
        ];

        // Get data from Odoo for each entity type sequentially
        // If any step fails, stop the sync process
        for (var entity in entityTypes) {
          final entityType = entity['type']!;
          final displayName = entity['displayName']!;

          print("Fetching $displayName from Odoo");
          if (mounted) state = SyncState.loaded("Fetching $displayName from Odoo...");

          syncResult = await _syncRepository.getSyncObjects(entityType, entityType, fullSync);

          if (!syncResult.success) {
            final errorMessage = "Failed to fetch $displayName: ${syncResult.message}";
            print(errorMessage);
            if (mounted) state = SyncState.error(errorMessage);
            return false;
          }

          print("Successfully fetched $displayName from Odoo");
        }

        // All fetches were successful
        print("Sync from Odoo complete");

        // Download company logos after sync completion
        await _downloadCompanyLogos();

        // Update the last sync date
        DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
        await prefs.setString(UserPreference.lastSyncDate, dateFormat.format(DateTime.now()).toString());
        if (mounted) state = SyncState.loaded("Sync from Odoo Complete");
        return true;
      }
    } catch (e,st) {
      print('$e \n$st');
      log("Sync from Odoo error: ${e.toString()}");
      if (e is CustomException) {
        if (mounted) state = SyncState.error(e.message);
      } else {
        if (mounted) state = SyncState.error("Sync from Odoo failed: ${e.toString()}");
      }
      return false;
    }
  }

  /// Download company logos after sync completion
  Future<void> _downloadCompanyLogos() async {
    try {
      if (mounted) state = SyncState.loaded("Downloading company logos...");
      print("Starting company logo download");

      // Get all synced companies
      final companies = await _syncRepository.getAllCompanies();

      if (companies.isNotEmpty) {
        // Download logos for all companies
        await _syncRepository.downloadCompanyLogos(companies);
        print("Company logo download completed");
      } else {
        print("No companies found to download logos for");
      }
    } catch (e, st) {
      print("Error downloading company logos: $e");
      print("Stack trace: $st");
      // Don't fail the sync if logo download fails
    }
  }

}
