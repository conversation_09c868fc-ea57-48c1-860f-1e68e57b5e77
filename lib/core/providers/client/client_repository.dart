// import 'package:flutter/foundation.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/core/constants/constants.dart';
// import 'package:invoicer/core/providers/client/ClientsRequest.dart';

// import '../../db/databaseHelper.dart';
// import '../../models/core/ResPartner.dart';
// import '../../network/dio_client.dart';
// import '../business/business_repository.dart';
// import 'PaginatedClients.dart';

// // Import the global database helper instance
// import '../../../main.dart';

// final clientsRepositoryProvider = Provider<ClientRepository>((ref) => ClientRepository(ref));


// class ClientRepository {
//   final Ref ref;
//   ClientRepository(this.ref);

//   Future<PaginatedClients> getClients(ClientsRequest req) async {
//     int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.get(
//         '$invoicerService/clients/business/${activeBusinessId}/${req.page_number}/${req.page_size}'
//       );

//       var res = result['content'] as List;

//       PaginatedClients paginaded = PaginatedClients(
//         content: res.map((e) => ResPartner.fromSyncJson(e)).toList(),
//         totalItems: result['totalElements'],
//         offset: result['number']*result['size'],
//         itemCount: result['numberOfElements'],
//         page_number: result['number'],
//       );

//       return paginaded;
//     }
//     else{
//       print("Getting paginated clients: page_size=${req.page_size}, page_number=${req.page_number}");
//       return await getDbPaginatedResPartners(req.page_size, req.page_number);
//     }
//   }


//   Future<PaginatedClients> searchClients(String query, {int page_size = 20, int page_number = 0}) async {
//     int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.get(
//         '$invoicerService/clients/search/${activeBusinessId}/${query}/${page_number}/${page_size}'
//       );

//       var res = result['content'] as List;

//       return PaginatedClients(
//         content: res.map((e) => ResPartner.fromSyncJson(e)).toList(),
//         totalItems: result['totalElements'],
//         offset: result['number']*result['size'],
//         itemCount: result['numberOfElements'],
//         page_number: result['number'],
//       );
//     }
//     else{
//       print("Searching clients with query: $query, page_size=$page_size, page_number=$page_number");
//       var result = await dbHelper.softQueryPaginatedRowsActiveBusiness("res_partner", page_size, page_number);

//       List<ResPartner> partners = [];
//       for (var map in result.data) {
//         var partner = await mapToResPartner(map);
//         if (partner != null && partner.name != null &&
//             partner.name!.toLowerCase().contains(query.toLowerCase())) {
//           partners.add(partner);
//         }
//       }

//       // Count total matching partners for accurate pagination
//       var allPartners = await getDbResPartners();
//       var totalMatching = allPartners.where((partner) =>
//         partner.name != null && partner.name!.toLowerCase().contains(query.toLowerCase())
//       ).length;

//       return PaginatedClients(
//         content: partners,
//         totalItems: totalMatching,
//         offset: page_number * page_size,
//         itemCount: partners.length,
//         page_number: page_number,
//       );
//     }
//   }

//   Future<int> createClient(ResPartner inv) async {


//     if((kIsWeb||strictWeb)){
//       await DioClient.instance.post( '$invoicerService/client', data: inv.toJson());

//       return 1;

//     }
//     else{

//       await inv.dbSave();

//         return 1;



//     }

//   }

//   Future<ResPartner?> getClient(int req) async {
//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.get( '$invoicerService/client/$req' );
//       return  ResPartner.fromSyncJson(result) ;
//     }
//     else{
//       var partners = await getDbResPartners();
//       // Find partner by ID
//       for (var partner in partners) {
//         if (partner.id == req) {
//           return partner;
//         }
//       }
//       return null;
//     }

//   }


//   Future<bool?> deleteClient(ResPartner req) async {
//     if((kIsWeb||strictWeb)){
//       await DioClient.instance.delete( '$invoicerService/client/${req.id}' );
//       return  true;
//     }
//     else{
//       req.dbDelete();
//       return  true;
//     }

//   }

//   Future<ResPartner> getNewClient() async {

//     ResPartner client = new ResPartner();
//     // Get active business
//       var activeBusiness = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       client.company_id = activeBusiness;

//     return client;
//   }


// }
