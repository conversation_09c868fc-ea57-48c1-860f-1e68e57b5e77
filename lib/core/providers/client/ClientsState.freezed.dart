// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ClientsState.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ClientsState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ClientsState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ClientsState()';
  }
}

/// @nodoc
class $ClientsStateCopyWith<$Res> {
  $ClientsStateCopyWith(ClientsState _, $Res Function(ClientsState) __);
}

/// @nodoc

class _ClientsStateInitial implements ClientsState {
  _ClientsStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClientsStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ClientsState.initial()';
  }
}

/// @nodoc

class _ClientsStateLoading implements ClientsState {
  _ClientsStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClientsStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ClientsState.loading()';
  }
}

/// @nodoc

class _ClientsStateData implements ClientsState {
  _ClientsStateData({required this.clients});

  final PaginatedClients clients;

  /// Create a copy of ClientsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ClientsStateDataCopyWith<_ClientsStateData> get copyWith =>
      __$ClientsStateDataCopyWithImpl<_ClientsStateData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ClientsStateData &&
            (identical(other.clients, clients) || other.clients == clients));
  }

  @override
  int get hashCode => Object.hash(runtimeType, clients);

  @override
  String toString() {
    return 'ClientsState.data(clients: $clients)';
  }
}

/// @nodoc
abstract mixin class _$ClientsStateDataCopyWith<$Res>
    implements $ClientsStateCopyWith<$Res> {
  factory _$ClientsStateDataCopyWith(
          _ClientsStateData value, $Res Function(_ClientsStateData) _then) =
      __$ClientsStateDataCopyWithImpl;
  @useResult
  $Res call({PaginatedClients clients});
}

/// @nodoc
class __$ClientsStateDataCopyWithImpl<$Res>
    implements _$ClientsStateDataCopyWith<$Res> {
  __$ClientsStateDataCopyWithImpl(this._self, this._then);

  final _ClientsStateData _self;
  final $Res Function(_ClientsStateData) _then;

  /// Create a copy of ClientsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? clients = null,
  }) {
    return _then(_ClientsStateData(
      clients: null == clients
          ? _self.clients
          : clients // ignore: cast_nullable_to_non_nullable
              as PaginatedClients,
    ));
  }
}

/// @nodoc

class _ClientsStateLoaded implements ClientsState {
  _ClientsStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of ClientsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ClientsStateLoadedCopyWith<_ClientsStateLoaded> get copyWith =>
      __$ClientsStateLoadedCopyWithImpl<_ClientsStateLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ClientsStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'ClientsState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$ClientsStateLoadedCopyWith<$Res>
    implements $ClientsStateCopyWith<$Res> {
  factory _$ClientsStateLoadedCopyWith(
          _ClientsStateLoaded value, $Res Function(_ClientsStateLoaded) _then) =
      __$ClientsStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$ClientsStateLoadedCopyWithImpl<$Res>
    implements _$ClientsStateLoadedCopyWith<$Res> {
  __$ClientsStateLoadedCopyWithImpl(this._self, this._then);

  final _ClientsStateLoaded _self;
  final $Res Function(_ClientsStateLoaded) _then;

  /// Create a copy of ClientsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_ClientsStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _ClientsStateError implements ClientsState {
  _ClientsStateError([this.error]);

  final String? error;

  /// Create a copy of ClientsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ClientsStateErrorCopyWith<_ClientsStateError> get copyWith =>
      __$ClientsStateErrorCopyWithImpl<_ClientsStateError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ClientsStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'ClientsState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$ClientsStateErrorCopyWith<$Res>
    implements $ClientsStateCopyWith<$Res> {
  factory _$ClientsStateErrorCopyWith(
          _ClientsStateError value, $Res Function(_ClientsStateError) _then) =
      __$ClientsStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$ClientsStateErrorCopyWithImpl<$Res>
    implements _$ClientsStateErrorCopyWith<$Res> {
  __$ClientsStateErrorCopyWithImpl(this._self, this._then);

  final _ClientsStateError _self;
  final $Res Function(_ClientsStateError) _then;

  /// Create a copy of ClientsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_ClientsStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
