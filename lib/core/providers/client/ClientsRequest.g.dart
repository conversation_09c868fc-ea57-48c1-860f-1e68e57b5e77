// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ClientsRequest.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ClientsRequest _$ClientsRequestFromJson(Map<String, dynamic> json) =>
    _ClientsRequest(
      type: json['type'] as String? ?? "INVOICE",
      dateSort: json['dateSort'] as String? ?? "asc",
      page_size: (json['page_size'] as num?)?.toInt() ?? 20,
      page_number: (json['page_number'] as num?)?.toInt() ?? 0,
      includeCancelled: json['includeCancelled'] as bool?,
      query: json['query'] as String?,
      clientStatusFilter: json['clientStatusFilter'] as String?,
      clientId: json['clientId'] as String?,
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
    );

Map<String, dynamic> _$ClientsRequestToJson(_ClientsRequest instance) =>
    <String, dynamic>{
      'type': instance.type,
      'dateSort': instance.dateSort,
      'page_size': instance.page_size,
      'page_number': instance.page_number,
      'includeCancelled': instance.includeCancelled,
      'query': instance.query,
      'clientStatusFilter': instance.clientStatusFilter,
      'clientId': instance.clientId,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
    };
