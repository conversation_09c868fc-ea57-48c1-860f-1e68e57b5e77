
import 'package:freezed_annotation/freezed_annotation.dart';

part 'ClientsRequest.freezed.dart';
part 'ClientsRequest.g.dart';

@freezed
abstract class ClientsRequest with _$ClientsRequest {

  factory ClientsRequest({
    @Default("INVOICE") String type,
    @Default("asc") String dateSort,
    @Default(20) int page_size,
    @Default(0) int page_number,
    bool? includeCancelled,
    String? query,
    String? clientStatusFilter,
    String? clientId,
    String? startDate,
    String? endDate,
  }) = _ClientsRequest;

  factory ClientsRequest.fromJson(Map<String, dynamic> json) =>
      _$ClientsRequestFromJson(json);

}

