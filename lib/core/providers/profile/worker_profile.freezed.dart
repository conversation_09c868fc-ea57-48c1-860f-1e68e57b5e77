// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'worker_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkerProfile {
  int? get id;
  set id(int? value);
  String? get firstname;
  set firstname(String? value);
  String? get lastname;
  set lastname(String? value);
  String? get gender;
  set gender(String? value);
  String? get phoneNumber;
  set phoneNumber(String? value);
  String? get email;
  set email(String? value);
  String? get username;
  set username(String? value);
  String? get assignmentCode;
  set assignmentCode(String? value);
  String? get status;
  set status(String? value);
  String? get createdBy;
  set createdBy(String? value);

  /// Create a copy of WorkerProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkerProfileCopyWith<WorkerProfile> get copyWith =>
      _$WorkerProfileCopyWithImpl<WorkerProfile>(
          this as WorkerProfile, _$identity);

  /// Serializes this WorkerProfile to a JSON map.
  Map<String, dynamic> toJson();

  @override
  String toString() {
    return 'WorkerProfile(id: $id, firstname: $firstname, lastname: $lastname, gender: $gender, phoneNumber: $phoneNumber, email: $email, username: $username, assignmentCode: $assignmentCode, status: $status, createdBy: $createdBy)';
  }
}

/// @nodoc
abstract mixin class $WorkerProfileCopyWith<$Res> {
  factory $WorkerProfileCopyWith(
          WorkerProfile value, $Res Function(WorkerProfile) _then) =
      _$WorkerProfileCopyWithImpl;
  @useResult
  $Res call(
      {int? id,
      String? firstname,
      String? lastname,
      String? gender,
      String? phoneNumber,
      String? email,
      String? username,
      String? assignmentCode,
      String? status,
      String? createdBy});
}

/// @nodoc
class _$WorkerProfileCopyWithImpl<$Res>
    implements $WorkerProfileCopyWith<$Res> {
  _$WorkerProfileCopyWithImpl(this._self, this._then);

  final WorkerProfile _self;
  final $Res Function(WorkerProfile) _then;

  /// Create a copy of WorkerProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstname = freezed,
    Object? lastname = freezed,
    Object? gender = freezed,
    Object? phoneNumber = freezed,
    Object? email = freezed,
    Object? username = freezed,
    Object? assignmentCode = freezed,
    Object? status = freezed,
    Object? createdBy = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      firstname: freezed == firstname
          ? _self.firstname
          : firstname // ignore: cast_nullable_to_non_nullable
              as String?,
      lastname: freezed == lastname
          ? _self.lastname
          : lastname // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      assignmentCode: freezed == assignmentCode
          ? _self.assignmentCode
          : assignmentCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _self.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _WorkerProfile implements WorkerProfile {
  _WorkerProfile(
      {this.id,
      this.firstname,
      this.lastname,
      this.gender,
      this.phoneNumber,
      this.email,
      this.username,
      this.assignmentCode,
      this.status,
      this.createdBy});
  factory _WorkerProfile.fromJson(Map<String, dynamic> json) =>
      _$WorkerProfileFromJson(json);

  @override
  int? id;
  @override
  String? firstname;
  @override
  String? lastname;
  @override
  String? gender;
  @override
  String? phoneNumber;
  @override
  String? email;
  @override
  String? username;
  @override
  String? assignmentCode;
  @override
  String? status;
  @override
  String? createdBy;

  /// Create a copy of WorkerProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkerProfileCopyWith<_WorkerProfile> get copyWith =>
      __$WorkerProfileCopyWithImpl<_WorkerProfile>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkerProfileToJson(
      this,
    );
  }

  @override
  String toString() {
    return 'WorkerProfile(id: $id, firstname: $firstname, lastname: $lastname, gender: $gender, phoneNumber: $phoneNumber, email: $email, username: $username, assignmentCode: $assignmentCode, status: $status, createdBy: $createdBy)';
  }
}

/// @nodoc
abstract mixin class _$WorkerProfileCopyWith<$Res>
    implements $WorkerProfileCopyWith<$Res> {
  factory _$WorkerProfileCopyWith(
          _WorkerProfile value, $Res Function(_WorkerProfile) _then) =
      __$WorkerProfileCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int? id,
      String? firstname,
      String? lastname,
      String? gender,
      String? phoneNumber,
      String? email,
      String? username,
      String? assignmentCode,
      String? status,
      String? createdBy});
}

/// @nodoc
class __$WorkerProfileCopyWithImpl<$Res>
    implements _$WorkerProfileCopyWith<$Res> {
  __$WorkerProfileCopyWithImpl(this._self, this._then);

  final _WorkerProfile _self;
  final $Res Function(_WorkerProfile) _then;

  /// Create a copy of WorkerProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? firstname = freezed,
    Object? lastname = freezed,
    Object? gender = freezed,
    Object? phoneNumber = freezed,
    Object? email = freezed,
    Object? username = freezed,
    Object? assignmentCode = freezed,
    Object? status = freezed,
    Object? createdBy = freezed,
  }) {
    return _then(_WorkerProfile(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      firstname: freezed == firstname
          ? _self.firstname
          : firstname // ignore: cast_nullable_to_non_nullable
              as String?,
      lastname: freezed == lastname
          ? _self.lastname
          : lastname // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      assignmentCode: freezed == assignmentCode
          ? _self.assignmentCode
          : assignmentCode // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _self.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
