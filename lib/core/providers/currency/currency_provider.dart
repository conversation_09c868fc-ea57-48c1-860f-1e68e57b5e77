import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/core/repositories/drift/res_currency_repository_drift.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';

// Use the Drift-based repository for currencies
final currencyProvider = FutureProvider.autoDispose.family<ResCurrencyTableData?, int>((ref, id) {
  return ref.read(resCurrencyRepositoryProvider).getById(id);
});

// Provider for all currencies (including inactive ones, but excluding deleted)
final currenciesProvider = FutureProvider<List<ResCurrencyTableData>>((ref) async {
  if (kIsWeb || strictWeb) {
    // Web implementation would be handled here
    throw UnimplementedError("Web implementation not available in this version");
  } else {
    // Local database implementation using Drift
    return ref.read(resCurrencyRepositoryProvider).getAll();
  }
});

// Provider for active currencies only (excluding deleted and inactive)
final activeCurrenciesProvider = FutureProvider<List<ResCurrencyTableData>>((ref) async {
  if (kIsWeb || strictWeb) {
    // Web implementation would be handled here
    throw UnimplementedError("Web implementation not available in this version");
  } else {
    // Local database implementation using Drift - get only active currencies
    final repository = ref.read(resCurrencyRepositoryProvider);
    if (repository is ResCurrencyRepositoryDrift) {
      return repository.getActive();
    } else {
      // Fallback to filtering manually if not using Drift repository
      final allCurrencies = await repository.getAll();
      return allCurrencies.where((currency) =>
        !currency.is_deleted && (currency.active == true || currency.active == 1)
      ).toList();
    }
  }
});

// Provider for active currency code
final activeCurrencyCodeProvider = FutureProvider<String>((ref) async {
  var prefs = await SharedPreferences.getInstance();
  var activeCurrencyCode = await prefs.getString(UserPreference.activeCurrency);
  return activeCurrencyCode ?? "USD"; // Default to USD if not set
});

// Provider for active currency
final activeCurrencyProvider = FutureProvider<ResCurrencyTableData?>((ref) async {
  var activeCurrencyCode = await ref.watch(activeCurrencyCodeProvider.future);

  // Get all currencies
  var currencies = await ref.read(resCurrencyRepositoryProvider).getAll();

  // Find the currency with the matching code
  try {
    return currencies.firstWhere(
      (currency) => currency.name == activeCurrencyCode
    );
  } catch (e) {
    // Return a default currency if not found
    return ResCurrencyTableData(
      id:0,
      name: "USD",
      symbol: "\$",
      active: true,
      is_synced: false,
      is_confirmed: true,
      is_deleted: false,
      version: 1
    );
  }
});

// Functions to get and set the active currency code
Future<String> getActiveCurrencyCode() async {
  var prefs = await SharedPreferences.getInstance();
  return prefs.getString(UserPreference.activeCurrency) ?? "USD";
}

Future<void> setActiveCurrencyCode(String code) async {
  var prefs = await SharedPreferences.getInstance();
  await prefs.setString(UserPreference.activeCurrency, code);
}
