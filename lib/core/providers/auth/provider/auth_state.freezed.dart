// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AuthState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AuthState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthState()';
  }
}

/// @nodoc
class $AuthStateCopyWith<$Res> {
  $AuthStateCopyWith(AuthState _, $Res Function(AuthState) __);
}

/// @nodoc

class _AuthStateInitial implements AuthState {
  _AuthStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _AuthStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthState.initial()';
  }
}

/// @nodoc

class _AuthStateLoading implements AuthState {
  _AuthStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _AuthStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthState.loading()';
  }
}

/// @nodoc

class _AuthStateData implements AuthState {
  _AuthStateData({required this.workerProfile});

  final WorkerProfile workerProfile;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AuthStateDataCopyWith<_AuthStateData> get copyWith =>
      __$AuthStateDataCopyWithImpl<_AuthStateData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AuthStateData &&
            (identical(other.workerProfile, workerProfile) ||
                other.workerProfile == workerProfile));
  }

  @override
  int get hashCode => Object.hash(runtimeType, workerProfile);

  @override
  String toString() {
    return 'AuthState.data(workerProfile: $workerProfile)';
  }
}

/// @nodoc
abstract mixin class _$AuthStateDataCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$AuthStateDataCopyWith(
          _AuthStateData value, $Res Function(_AuthStateData) _then) =
      __$AuthStateDataCopyWithImpl;
  @useResult
  $Res call({WorkerProfile workerProfile});

  $WorkerProfileCopyWith<$Res> get workerProfile;
}

/// @nodoc
class __$AuthStateDataCopyWithImpl<$Res>
    implements _$AuthStateDataCopyWith<$Res> {
  __$AuthStateDataCopyWithImpl(this._self, this._then);

  final _AuthStateData _self;
  final $Res Function(_AuthStateData) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? workerProfile = null,
  }) {
    return _then(_AuthStateData(
      workerProfile: null == workerProfile
          ? _self.workerProfile
          : workerProfile // ignore: cast_nullable_to_non_nullable
              as WorkerProfile,
    ));
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorkerProfileCopyWith<$Res> get workerProfile {
    return $WorkerProfileCopyWith<$Res>(_self.workerProfile, (value) {
      return _then(_self.copyWith(workerProfile: value));
    });
  }
}

/// @nodoc

class _AuthStateLoaded implements AuthState {
  _AuthStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AuthStateLoadedCopyWith<_AuthStateLoaded> get copyWith =>
      __$AuthStateLoadedCopyWithImpl<_AuthStateLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AuthStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'AuthState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$AuthStateLoadedCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$AuthStateLoadedCopyWith(
          _AuthStateLoaded value, $Res Function(_AuthStateLoaded) _then) =
      __$AuthStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$AuthStateLoadedCopyWithImpl<$Res>
    implements _$AuthStateLoadedCopyWith<$Res> {
  __$AuthStateLoadedCopyWithImpl(this._self, this._then);

  final _AuthStateLoaded _self;
  final $Res Function(_AuthStateLoaded) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_AuthStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _AuthStateError implements AuthState {
  _AuthStateError([this.error]);

  final String? error;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AuthStateErrorCopyWith<_AuthStateError> get copyWith =>
      __$AuthStateErrorCopyWithImpl<_AuthStateError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AuthStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'AuthState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$AuthStateErrorCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$AuthStateErrorCopyWith(
          _AuthStateError value, $Res Function(_AuthStateError) _then) =
      __$AuthStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$AuthStateErrorCopyWithImpl<$Res>
    implements _$AuthStateErrorCopyWith<$Res> {
  __$AuthStateErrorCopyWithImpl(this._self, this._then);

  final _AuthStateError _self;
  final $Res Function(_AuthStateError) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_AuthStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
