import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/screens/profile/edit/profile_screen.dart' hide getDownloadPath2;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:drift/drift.dart';
import '../../../db/drift/database.dart'; // Import for Drift-generated classes
import '../../../init/app_providers.dart';
import '../../../network/dio_client.dart';
import '../../../utils/UserPreference.dart';
import '../../../constants/constants.dart';
import '../../../utils/shared_pref_service.dart';
import '../../../utils/invoicegenerator.dart'; // For getDownloadPath2
import '../../../repositories/drift/res_company_repository_drift.dart';
import '../../../repositories/drift/repository_provider_riverpod.dart';
import '../../profile/worker_profile.dart';
import '../../sync/sync_provider.dart';
import '../model/login_response.dart';
import 'package:http/http.dart' show get;

abstract class IAuthRepository {
  Future login(Map<String, dynamic>? credentials, {bool rememberMe = false});

  Future forgotPaswd(String email);

  Future resetPassword(Map<String, String>? credentials);

  Future getTokenPair(Map<String, String>? credentials);

  Future getWorkerProfile(String id);

  Future refreshTokenPair();

  Future register(Map<String, dynamic> userData);
}

class AuthRepository implements IAuthRepository {
  final Ref ref;
  AuthRepository(this.ref);

  @override
  Future getWorkerProfile(String email) async {
    // Get the Odoo credentials from SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final String odooUrl = prefs.getString('odooUrl') ?? '';
    final String database = prefs.getString('database') ?? '';
    final String username = email;
    final String password = prefs.getString('password') ?? '';
    final int userId = int.parse(prefs.getString(UserPreference.userId) ?? '0');

    if (odooUrl.isEmpty || database.isEmpty || password.isEmpty || userId == 0) {
      throw Exception('Missing Odoo credentials');
    }

    // Get user profile from Odoo
    final result = await DioClient.instance.getOdooUserProfile(
      odooUrl,
      database,
      userId,
      username,
      password
    );

    if (result == null) {
      throw Exception('Empty response from Odoo');
    }

    if (result['error'] != null) {
      final error = result['error'];
      throw Exception('Odoo error: ${error['message'] ?? 'Unknown error'}');
    }

    if (result['result'] == null) {
      throw Exception('No result in Odoo response');
    }

    // Log the full response for debugging
    print('Full Odoo response: $result');

    // Extract user data from the Odoo response
    final resultData = result['result'];

    // Handle different response formats
    Map<String, dynamic> userData = {};
    if (resultData is List) {
      if (resultData.isEmpty) {
        print('Empty result list from Odoo, using default values');
      } else {
        userData = Map<String, dynamic>.from(resultData[0]);
      }
    } else if (resultData is Map) {
      userData = Map<String, dynamic>.from(resultData);
    } else {
      print('Unexpected result format from Odoo: ${resultData.runtimeType}, using default values');
    }

    // Log the response for debugging
    print('Odoo user profile response: $userData');

    // Create a WorkerProfile from the Odoo user data
    final String fullName = userData['name'] ?? '';
    final List<String> nameParts = fullName.split(' ');
    final String firstName = nameParts.isNotEmpty ? nameParts[0] : '';
    final String lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    try {
      // Create a WorkerProfile with the available data
      return WorkerProfile(
        id: userData['id'] is int ? userData['id'] : int.tryParse(userData['id']?.toString() ?? '') ?? userId,
        firstname: firstName,
        lastname: lastName,
        email: userData['email'] ?? email,
        phoneNumber: userData['phone'] ?? '',
        username: userData['login'] ?? email
      );
    } catch (e, st) {
      print('Error creating WorkerProfile: $e \n $st');
      // Fallback to a minimal profile if there's an error
      return WorkerProfile(
        id: userId,
        firstname: firstName,
        lastname: lastName,
        email: email,
        username: email
      );
    }
  }


  @override
  Future resetPassword(Map<String, dynamic>? credentials) async {
    await DioClient.instance.post(
      '$userService/api/v1/user-management/user/savePassword',
      data: credentials,
        ignoreAuthToken: true
    );
    return true;
  }




  @override
  Future getTokenPair(Map<String, String>? credentials) async {
    final result = await DioClient.instance.post('users/login/', data: credentials, ignoreAuthToken: true);
    // Return the result directly
    return result;
  }



  @override
  Future refreshTokenPair() async {
    final result = await DioClient.instance.post('auth/users/');
    return WorkerProfile.fromJson(result);
  }


  @override
  Future forgotPaswd(String email) async {
    final result = await DioClient.instance.post("$userService/api/v1/user-management/user/resetPassword?email=$email");
    Map<String?, String?> message = Map<String?, String?>.from(result);
    return message;
  }


  @override
  Future login(Map<String, dynamic>? credentials, {bool rememberMe = false}) async {
    // Extract Odoo-specific fields from credentials
    final String odooUrl = credentials?['odooUrl'] ?? '';
    final String database = credentials?['database'] ?? '';
    final String username = credentials?['username'] ?? '';
    final String password = credentials?['password'] ?? '';

    // Call Odoo authentication endpoint
    final result = await DioClient.instance.postOdooJsonRpc(
      odooUrl,
      database,
      username,
      password
    );

    // Check if authentication was successful
    if (result == null || result['result'] == false) {
      throw Exception('Authentication failed: Invalid credentials');
    }

    // Create a LoginResponse object from the Odoo response
    final odooUserId = result['result'] as int;

    // Create a simplified LoginResponse with the available data
    final LoginResponse resp = LoginResponse(
      id: odooUserId,
      email: username,
      firstName: username.split('@').first,
      lastName: '',
      accessToken: odooUserId.toString(), // Using userId as a simple token
      refreshToken: odooUserId.toString(),
    );
    bool wasPreviuslyLogged = false;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var storedUserId = await prefs.getString(UserPreference.userId);

    if(storedUserId != null){
      wasPreviuslyLogged = true;
    }
    // Store user information in SharedPreferences
    prefs.setString(UserPreference.firstName, resp.firstName.toString());
    prefs.setString(UserPreference.email, resp.email.toString());
    prefs.setString(UserPreference.lastName, resp.lastName.toString());
    prefs.setString(UserPreference.accessToken, resp.accessToken!);
    prefs.setString(UserPreference.refreshToken, resp.refreshToken!);
    prefs.setString(UserPreference.userId, resp.id.toString());

    // Store Odoo credentials in SharedPreferences for later use
    prefs.setString('odooUrl', odooUrl);
    prefs.setString('database', database);
    prefs.setString('password', password);

    // Log the stored credentials for debugging
    print('Stored Odoo credentials - URL: $odooUrl, DB: $database, User ID: ${resp.id}');

    List<String> permissions = [];
    resp.roles?.forEach((r) {
      r?.permissions?.forEach((p) {
        permissions.add(p!.authority!);
      });
    });

    prefs.setStringList(UserPreference.permissions, permissions);
    ref.read(authKeyProvider.notifier).state = resp.accessToken!;
    ref.read(loginResponseProvider.notifier).state = (resp);

    WorkerProfile workerProfile;
    if(kIsWeb || strictWeb){
      workerProfile = await getWorkerProfile(resp.email!);
    }else{
      workerProfile = await getWorkerProfile(resp.email!);
    }



    if(workerProfile.id != null)prefs.setString(UserPreference.userId, workerProfile.id.toString());





    ref.read(workerProfileProvider.notifier).state = workerProfile;

    if (rememberMe) {
      await ref.read(sharedPreferencesServiceProvider)
          .cacheUserCredentials(credentials!);
    }


    if(!(kIsWeb|| strictWeb)) {

      if(wasPreviuslyLogged){
        ref
            .read(syncNotifierProvider.notifier)
            .getSync(true);
        // if(workerProfile.businesses!=null) downloadLogos(workerProfile.businesses!);
        // if(workerProfile.businesses!=null) await savePrefixes(workerProfile.devices!);

      }else{
        await ref
            .read(syncNotifierProvider.notifier)
            .getSync(true);

        // if(workerProfile.businesses!=null)await downloadLogos(workerProfile.businesses!);

        return workerProfile;
      }

    }

    return workerProfile;
  }

  Future<void> downloadLogos(List<ResCompanyTableData> businesses) async{
    print("Starting logo download for ${businesses.length} companies");

    for(int i = 0; i < businesses.length; i++){
      var company = businesses[i];

      try {
        // Skip if company doesn't have a logo or universal_id
        if (company.logo == null || company.universal_id == null) {
          print("Skipping company ${company.name}: no logo or universal_id");
          continue;
        }

        print("Downloading logo for company: ${company.name} (ID: ${company.universal_id})");

        // Download logo from Odoo as base64 data
        final logoData = await _downloadCompanyLogoFromOdoo(company.universal_id!);

        if (logoData != null) {
          // Save logo to local storage
          final logoFileName = await _saveLogoToLocal(logoData, company.id);

          if (logoFileName != null) {
            // Update company record with local logo filename
            await _updateCompanyLogo(company.id, logoFileName);
            print("Successfully downloaded and saved logo for company: ${company.name}");
          }
        }
      } catch (e, st) {
        print("Error downloading logo for company ${company.name}: $e");
        print("Stack trace: $st");
      }
    }
  }

  /// Download company logo from Odoo as base64 data
  Future<List<int>?> _downloadCompanyLogoFromOdoo(int companyId) async {
    try {
      // Get Odoo credentials
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final String odooUrl = prefs.getString('odooUrl') ?? '';
      final String database = prefs.getString('database') ?? '';
      final int userId = int.parse(prefs.getString(UserPreference.userId) ?? '0');
      final String password = prefs.getString('password') ?? '';

      if (odooUrl.isEmpty || database.isEmpty || userId == 0 || password.isEmpty) {
        throw Exception('Missing Odoo credentials');
      }

      // Read the logo field from Odoo (it's stored as base64)
      final Map<String, dynamic> data = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            userId,
            password,
            "res.company",
            "read",
            [[companyId]],
            {"fields": ["logo"]}
          ]
        }
      };

      final Dio odooClient = Dio(
        BaseOptions(
          baseUrl: odooUrl,
          connectTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
          responseType: ResponseType.json,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        )
      );

      final Response response = await odooClient.post(
        odooJsonRpcEndpoint,
        data: data,
      );

      if (response.data != null && response.data['result'] != null) {
        final result = response.data['result'];
        if (result is List && result.isNotEmpty) {
          final companyData = result[0];
          final logoBase64 = companyData['logo'];

          if (logoBase64 != null && logoBase64 is String && logoBase64.isNotEmpty) {
            // Decode base64 to bytes
            return base64Decode(logoBase64);
          }
        }
      }

      return null;
    } catch (e) {
      print("Error downloading logo from Odoo: $e");
      return null;
    }
  }

  /// Save logo bytes to local storage and return filename
  Future<String?> _saveLogoToLocal(List<int> logoBytes, int companyId) async {
    try {
      final directory = await getDownloadPath2();
      if (directory == null) {
        print("Could not get download directory");
        return null;
      }

      // Create filename with company ID
      final logoFileName = "company_${companyId}.png";
      final logoPath = "$directory$logoFileName";

      // Save file
      final file = File(logoPath);
      await file.create(recursive: true);
      await file.writeAsBytes(logoBytes);

      print("Logo saved to: $logoPath");
      return logoFileName;
    } catch (e) {
      print("Error saving logo to local storage: $e");
      return null;
    }
  }

  /// Update company record with local logo filename
  Future<void> _updateCompanyLogo(int companyId, String logoFileName) async {
    try {
      final resCompanyRepository = ref.read(resCompanyRepositoryProvider);
      final company = await resCompanyRepository.getById(companyId);

      if (company != null) {
        final updatedCompany = company.copyWith(logo: Value(logoFileName));
        await resCompanyRepository.save(updatedCompany.toCompanion(false));
      }
    } catch (e) {
      print("Error updating company logo: $e");
    }
  }

  @override
  Future register(Map<String, dynamic> userData) async {
    try {
      // Extract Odoo-specific fields from userData
      final String odooUrl = userData['odooUrl'] ?? '';
      final String database = userData['database'] ?? '';
      final String email = userData['email'] ?? '';
      final String password = userData['password'] ?? '';
      final String firstName = userData['firstName'] ?? '';
      final String lastName = userData['lastName'] ?? '';

      // Validate required fields
      if (odooUrl.isEmpty || database.isEmpty || email.isEmpty || password.isEmpty) {
        throw Exception('Missing required fields for registration');
      }

      // Call Odoo registration endpoint
      final result = await DioClient.instance.postOdooRegistration(
        odooUrl,
        database,
        email,
        password,
        firstName,
        lastName
      );

      // Check if registration was successful
      if (result == null) {
        throw Exception('Registration failed: Empty response');
      }

      if (result['error'] != null) {
        final error = result['error'];
        throw Exception('Registration error: ${error['message'] ?? 'Unknown error'}');
      }

      // Return success message or user data
      return result['result'] ?? {'message': 'Registration successful'};
    } catch (e ,st) {
      print('Registration error: $e \n $st');
      throw e;
    }
  }
}
