// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'CategorysRequest.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CategorysRequest {
  String get type;
  String get dateSort;
  int get page_size;
  int get page_number;
  bool? get includeCancelled;
  bool? get isOrder;
  String? get categoryStatusFilter;
  String? get clientId;
  String? get startDate;
  String? get endDate;

  /// Create a copy of CategorysRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CategorysRequestCopyWith<CategorysRequest> get copyWith =>
      _$CategorysRequestCopyWithImpl<CategorysRequest>(
          this as CategorysRequest, _$identity);

  /// Serializes this CategorysRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CategorysRequest &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.dateSort, dateSort) ||
                other.dateSort == dateSort) &&
            (identical(other.page_size, page_size) ||
                other.page_size == page_size) &&
            (identical(other.page_number, page_number) ||
                other.page_number == page_number) &&
            (identical(other.includeCancelled, includeCancelled) ||
                other.includeCancelled == includeCancelled) &&
            (identical(other.isOrder, isOrder) || other.isOrder == isOrder) &&
            (identical(other.categoryStatusFilter, categoryStatusFilter) ||
                other.categoryStatusFilter == categoryStatusFilter) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      dateSort,
      page_size,
      page_number,
      includeCancelled,
      isOrder,
      categoryStatusFilter,
      clientId,
      startDate,
      endDate);

  @override
  String toString() {
    return 'CategorysRequest(type: $type, dateSort: $dateSort, page_size: $page_size, page_number: $page_number, includeCancelled: $includeCancelled, isOrder: $isOrder, categoryStatusFilter: $categoryStatusFilter, clientId: $clientId, startDate: $startDate, endDate: $endDate)';
  }
}

/// @nodoc
abstract mixin class $CategorysRequestCopyWith<$Res> {
  factory $CategorysRequestCopyWith(
          CategorysRequest value, $Res Function(CategorysRequest) _then) =
      _$CategorysRequestCopyWithImpl;
  @useResult
  $Res call(
      {String type,
      String dateSort,
      int page_size,
      int page_number,
      bool? includeCancelled,
      bool? isOrder,
      String? categoryStatusFilter,
      String? clientId,
      String? startDate,
      String? endDate});
}

/// @nodoc
class _$CategorysRequestCopyWithImpl<$Res>
    implements $CategorysRequestCopyWith<$Res> {
  _$CategorysRequestCopyWithImpl(this._self, this._then);

  final CategorysRequest _self;
  final $Res Function(CategorysRequest) _then;

  /// Create a copy of CategorysRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? dateSort = null,
    Object? page_size = null,
    Object? page_number = null,
    Object? includeCancelled = freezed,
    Object? isOrder = freezed,
    Object? categoryStatusFilter = freezed,
    Object? clientId = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_self.copyWith(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      dateSort: null == dateSort
          ? _self.dateSort
          : dateSort // ignore: cast_nullable_to_non_nullable
              as String,
      page_size: null == page_size
          ? _self.page_size
          : page_size // ignore: cast_nullable_to_non_nullable
              as int,
      page_number: null == page_number
          ? _self.page_number
          : page_number // ignore: cast_nullable_to_non_nullable
              as int,
      includeCancelled: freezed == includeCancelled
          ? _self.includeCancelled
          : includeCancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOrder: freezed == isOrder
          ? _self.isOrder
          : isOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      categoryStatusFilter: freezed == categoryStatusFilter
          ? _self.categoryStatusFilter
          : categoryStatusFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      clientId: freezed == clientId
          ? _self.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CategorysRequest implements CategorysRequest {
  _CategorysRequest(
      {this.type = "INVOICE",
      this.dateSort = "asc",
      this.page_size = 20,
      this.page_number = 0,
      this.includeCancelled,
      this.isOrder,
      this.categoryStatusFilter,
      this.clientId,
      this.startDate,
      this.endDate});
  factory _CategorysRequest.fromJson(Map<String, dynamic> json) =>
      _$CategorysRequestFromJson(json);

  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey()
  final String dateSort;
  @override
  @JsonKey()
  final int page_size;
  @override
  @JsonKey()
  final int page_number;
  @override
  final bool? includeCancelled;
  @override
  final bool? isOrder;
  @override
  final String? categoryStatusFilter;
  @override
  final String? clientId;
  @override
  final String? startDate;
  @override
  final String? endDate;

  /// Create a copy of CategorysRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategorysRequestCopyWith<_CategorysRequest> get copyWith =>
      __$CategorysRequestCopyWithImpl<_CategorysRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CategorysRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategorysRequest &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.dateSort, dateSort) ||
                other.dateSort == dateSort) &&
            (identical(other.page_size, page_size) ||
                other.page_size == page_size) &&
            (identical(other.page_number, page_number) ||
                other.page_number == page_number) &&
            (identical(other.includeCancelled, includeCancelled) ||
                other.includeCancelled == includeCancelled) &&
            (identical(other.isOrder, isOrder) || other.isOrder == isOrder) &&
            (identical(other.categoryStatusFilter, categoryStatusFilter) ||
                other.categoryStatusFilter == categoryStatusFilter) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      dateSort,
      page_size,
      page_number,
      includeCancelled,
      isOrder,
      categoryStatusFilter,
      clientId,
      startDate,
      endDate);

  @override
  String toString() {
    return 'CategorysRequest(type: $type, dateSort: $dateSort, page_size: $page_size, page_number: $page_number, includeCancelled: $includeCancelled, isOrder: $isOrder, categoryStatusFilter: $categoryStatusFilter, clientId: $clientId, startDate: $startDate, endDate: $endDate)';
  }
}

/// @nodoc
abstract mixin class _$CategorysRequestCopyWith<$Res>
    implements $CategorysRequestCopyWith<$Res> {
  factory _$CategorysRequestCopyWith(
          _CategorysRequest value, $Res Function(_CategorysRequest) _then) =
      __$CategorysRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String type,
      String dateSort,
      int page_size,
      int page_number,
      bool? includeCancelled,
      bool? isOrder,
      String? categoryStatusFilter,
      String? clientId,
      String? startDate,
      String? endDate});
}

/// @nodoc
class __$CategorysRequestCopyWithImpl<$Res>
    implements _$CategorysRequestCopyWith<$Res> {
  __$CategorysRequestCopyWithImpl(this._self, this._then);

  final _CategorysRequest _self;
  final $Res Function(_CategorysRequest) _then;

  /// Create a copy of CategorysRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? type = null,
    Object? dateSort = null,
    Object? page_size = null,
    Object? page_number = null,
    Object? includeCancelled = freezed,
    Object? isOrder = freezed,
    Object? categoryStatusFilter = freezed,
    Object? clientId = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_CategorysRequest(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      dateSort: null == dateSort
          ? _self.dateSort
          : dateSort // ignore: cast_nullable_to_non_nullable
              as String,
      page_size: null == page_size
          ? _self.page_size
          : page_size // ignore: cast_nullable_to_non_nullable
              as int,
      page_number: null == page_number
          ? _self.page_number
          : page_number // ignore: cast_nullable_to_non_nullable
              as int,
      includeCancelled: freezed == includeCancelled
          ? _self.includeCancelled
          : includeCancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOrder: freezed == isOrder
          ? _self.isOrder
          : isOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      categoryStatusFilter: freezed == categoryStatusFilter
          ? _self.categoryStatusFilter
          : categoryStatusFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      clientId: freezed == clientId
          ? _self.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
