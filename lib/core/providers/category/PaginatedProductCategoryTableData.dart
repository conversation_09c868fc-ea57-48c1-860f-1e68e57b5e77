import 'package:invoicer/core/db/drift/database.dart';

class PaginatedProductCategoryTableData {
  final List<ProductCategoryTableData> content;
  final int totalItems;
  final int offset;
  final int itemCount;
  final int page_number;

  PaginatedProductCategoryTableData({
    required this.content,
    required this.totalItems,
    required this.offset,
    required this.itemCount,
    required this.page_number,
  });
}
