import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:invoicer/core/db/drift/database.dart';

// import '../../models/Category.dart';


part 'CategorysState.freezed.dart';

// extension method for easy comparison
extension CategoryGetters on CategorysState {
  bool get isLoading => this is _CategorysStateLoading;
  bool get isError=> this is _CategorysStateError;
}

@freezed
class CategorysState with _$CategorysState {
  /// initial
  factory CategorysState.initial() = _CategorysStateInitial;

  /// loading
  factory CategorysState.loading() = _CategorysStateLoading;

  /// data
  factory CategorysState.data({required List<ProductCategoryTableData> categorys}) =
      _CategorysStateData;

  /// other data different from categorys
  factory CategorysState.loaded([@Default(0) dynamic data]) = _CategorysStateLoaded;

  /// Error
  factory CategorysState.error([String? error]) = _CategorysStateError;
}
