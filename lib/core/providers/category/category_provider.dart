import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';
import 'PaginatedProductCategoryTableData.dart';
import 'CategorysRequest.dart';

// Use the Drift-based repository for categories
final categoryProvider = FutureProvider.autoDispose.family<ProductCategoryTableData?, int>((ref, id) {
  return ref.read(productCategoryRepositoryProvider).getById(id);
});

// Provider for creating a new category
final newCategoryProvider = FutureProvider.autoDispose<ProductCategoryTableData>((ref) async {
  // Create a new category with default values
  return ProductCategoryTableData(
    id:0,
    name: '',
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1,
  );
});

// Provider for paginated categories
final categoriesProvider = FutureProvider.autoDispose.family<PaginatedProductCategoryTableData, CategorysRequest>((ref, req) async {
  // Get active business ID
  var prefs = await SharedPreferences.getInstance();
  var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

  if (activeBusiness == null) {
    throw Exception("Go to settings and create or select active business.");
  }

  if (kIsWeb || strictWeb) {
    // Web implementation would be handled here
    throw UnimplementedError("Web implementation not available in this version");
  } else {
    // Local database implementation using Drift
    // Get all categories
    final categories = await ref.read(productCategoryRepositoryProvider).getAll();

    // Filter categories for the active business
    var filteredCategories = categories.where((category) =>
      category.company_id == null || category.company_id == activeBusiness
    ).toList();

    // Sort categories by name
    filteredCategories.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    // Pagination
    int totalItems = filteredCategories.length;
    int startIndex = req.page_number * req.page_size;
    int endIndex = (startIndex + req.page_size) > filteredCategories.length
        ? filteredCategories.length
        : (startIndex + req.page_size);

    List<ProductCategoryTableData> pagedCategories = [];
    if (startIndex < filteredCategories.length) {
      pagedCategories = filteredCategories.sublist(startIndex, endIndex);
    }

    // Create paginated result
    return PaginatedProductCategoryTableData(
      content: pagedCategories,
      totalItems: totalItems,
      offset: req.page_number * req.page_size,
      itemCount: pagedCategories.length,
      page_number: req.page_number,
    );
  }
});

// Provider for searching categories
final searchCategoriesProvider = FutureProvider.autoDispose.family<List<ProductCategoryTableData>, String>((ref, query) {
  return ref.read(productCategoryRepositoryProvider).search(query);
});
