import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/UserPreference.dart';

/// Service for migrating existing tax data to the new tax system
class TaxMigrationService {
  final AppDatabase _database;

  TaxMigrationService(this._database);

  /// Check if migration is needed
  Future<bool> isMigrationNeeded() async {
    final prefs = await SharedPreferences.getInstance();
    final migrationCompleted = prefs.getBool('tax_migration_completed') ?? false;
    
    if (migrationCompleted) return false;

    // Check if there are any existing invoices with old tax_rate field
    final invoices = await _database.accountMoveDao.getAllInvoices();
    return invoices.any((invoice) => 
      invoice.tax_rate != null && 
      invoice.tax_rate!.isNotEmpty && 
      invoice.tax_rate != '0'
    );
  }

  /// Perform the migration
  Future<void> performMigration() async {
    print('Starting tax migration...');

    try {
      // Step 1: Create default taxes for each company
      await _createDefaultTaxes();

      // Step 2: Migrate invoice tax rates to tax entities
      await _migrateInvoiceTaxRates();

      // Step 3: Update product tax relationships
      await _updateProductTaxRelationships();

      // Step 4: Mark migration as completed
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('tax_migration_completed', true);

      print('Tax migration completed successfully');
    } catch (e) {
      print('Error during tax migration: $e');
      rethrow;
    }
  }

  /// Create default taxes for each company
  Future<void> _createDefaultTaxes() async {
    final companies = await _database.resCompanyDao.getAllCompanies();

    for (final company in companies) {
      // Create a default sales tax (e.g., 10% VAT)
      final saleTaxId = await _database.accountTaxDao.createTax(
        AccountTaxTableCompanion.insert(
          name: 'Sales Tax (${company.name})',
          amount_type: 'percent',
          amount: 10.0, // Default 10% - can be adjusted
          type_tax_use: 'sale',
          price_include: false,
          active: true,
          company_id: Value(company.id),
          sequence: 1,
        ),
      );

      // Create a default purchase tax
      final purchaseTaxId = await _database.accountTaxDao.createTax(
        AccountTaxTableCompanion.insert(
          name: 'Purchase Tax (${company.name})',
          amount_type: 'percent',
          amount: 10.0, // Default 10% - can be adjusted
          type_tax_use: 'purchase',
          price_include: false,
          active: true,
          company_id: Value(company.id),
          sequence: 1,
        ),
      );

      // Update company with default taxes
      await _database.resCompanyDao.updateCompany(
        company.id,
        ResCompanyTableCompanion(
          account_sale_tax_id: Value(saleTaxId),
          account_purchase_tax_id: Value(purchaseTaxId),
        ),
      );

      print('Created default taxes for company: ${company.name}');
    }
  }

  /// Migrate invoice tax rates to proper tax entities
  Future<void> _migrateInvoiceTaxRates() async {
    final invoices = await _database.accountMoveDao.getAllInvoices();

    for (final invoice in invoices) {
      if (invoice.tax_rate == null || invoice.tax_rate!.isEmpty || invoice.tax_rate == '0') {
        continue;
      }

      try {
        final taxRate = double.parse(invoice.tax_rate!);
        if (taxRate == 0) continue;

        // Find or create a tax with this rate for the company
        final taxId = await _findOrCreateTaxForRate(
          taxRate,
          invoice.company_id ?? 1,
          'sale', // Assume sales tax for invoices
        );

        // Update invoice lines to use this tax
        await _updateInvoiceLineTaxes(invoice.id, taxId);

        print('Migrated invoice ${invoice.id} with tax rate ${taxRate}%');
      } catch (e) {
        print('Error migrating invoice ${invoice.id}: $e');
      }
    }
  }

  /// Find or create a tax with the specified rate
  Future<int> _findOrCreateTaxForRate(
    double rate,
    int companyId,
    String taxType,
  ) async {
    // First, try to find an existing tax with this rate
    final existingTaxes = await _database.accountTaxDao.getTaxesForCompany(companyId);
    final matchingTax = existingTaxes.where((tax) =>
      tax.amount_type == 'percent' &&
      tax.amount == rate &&
      tax.type_tax_use == taxType
    ).firstOrNull;

    if (matchingTax != null) {
      return matchingTax.id;
    }

    // Create a new tax
    final company = await _database.resCompanyDao.getCompanyById(companyId);
    final companyName = company?.name ?? 'Unknown';

    return await _database.accountTaxDao.createTax(
      AccountTaxTableCompanion.insert(
        name: '${taxType.toUpperCase()} Tax ${rate.toStringAsFixed(1)}% (${companyName})',
        amount_type: 'percent',
        amount: rate,
        type_tax_use: taxType,
        price_include: false,
        active: true,
        company_id: Value(companyId),
        sequence: 10, // Higher sequence for migrated taxes
      ),
    );
  }

  /// Update invoice lines to use the specified tax
  Future<void> _updateInvoiceLineTaxes(int invoiceId, int taxId) async {
    final lines = await _database.accountMoveLineDao.getLinesForInvoice(invoiceId);

    for (final line in lines) {
      if (line.is_deleted || line.isTaxLine()) continue;

      // Set the tax ID for this line
      final taxIds = [taxId];
      await _database.accountMoveLineDao.updateLine(
        line.id,
        AccountMoveLineTableCompanion(
          tax_ids: Value(json.encode(taxIds)),
        ),
      );
    }
  }

  /// Update product tax relationships based on company defaults
  Future<void> _updateProductTaxRelationships() async {
    final products = await _database.productProductDao.getAllProducts();
    final companies = await _database.resCompanyDao.getAllCompanies();

    for (final product in products) {
      final companyId = product.company_id ?? 1;
      final company = companies.where((c) => c.id == companyId).firstOrNull;

      if (company == null) continue;

      List<int> customerTaxIds = [];
      List<int> supplierTaxIds = [];

      // Use company default taxes if available
      if (company.account_sale_tax_id != null) {
        customerTaxIds.add(company.account_sale_tax_id!);
      }

      if (company.account_purchase_tax_id != null) {
        supplierTaxIds.add(company.account_purchase_tax_id!);
      }

      // Update product with tax relationships
      await _database.productProductDao.updateProduct(
        product.id,
        ProductProductTableCompanion(
          taxes_id: Value(json.encode(customerTaxIds)),
          supplier_taxes_id: Value(json.encode(supplierTaxIds)),
        ),
      );

      print('Updated tax relationships for product: ${product.name}');
    }
  }

  /// Rollback migration (for testing purposes)
  Future<void> rollbackMigration() async {
    print('Rolling back tax migration...');

    try {
      // Delete all taxes
      final taxes = await _database.accountTaxDao.getAllActiveTaxes();
      for (final tax in taxes) {
        await _database.accountTaxDao.deleteTax(tax.id);
      }

      // Clear company default taxes
      final companies = await _database.resCompanyDao.getAllCompanies();
      for (final company in companies) {
        await _database.resCompanyDao.updateCompany(
          company.id,
          const ResCompanyTableCompanion(
            account_sale_tax_id: Value(null),
            account_purchase_tax_id: Value(null),
          ),
        );
      }

      // Clear product tax relationships
      final products = await _database.productProductDao.getAllProducts();
      for (final product in products) {
        await _database.productProductDao.updateProduct(
          product.id,
          const ProductProductTableCompanion(
            taxes_id: Value(null),
            supplier_taxes_id: Value(null),
          ),
        );
      }

      // Clear invoice line tax relationships
      final lines = await _database.accountMoveLineDao.getAllLines();
      for (final line in lines) {
        await _database.accountMoveLineDao.updateLine(
          line.id,
          const AccountMoveLineTableCompanion(
            tax_ids: Value(null),
          ),
        );
      }

      // Mark migration as not completed
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('tax_migration_completed', false);

      print('Tax migration rollback completed');
    } catch (e) {
      print('Error during tax migration rollback: $e');
      rethrow;
    }
  }
}

/// Extension to add getAllLines method to AccountMoveLineDao
extension AccountMoveLineDaoExtension on AccountMoveLineDao {
  Future<List<AccountMoveLineTableData>> getAllLines() {
    return select(accountMoveLineTable).get();
  }
}
