import 'package:invoicer/core/db/drift/database_service.dart';
import 'package:invoicer/core/services/tax_migration_service.dart';

/// Service for running database migrations
class MigrationRunner {
  static bool _migrationInProgress = false;

  /// Run all pending migrations
  static Future<void> runMigrations() async {
    if (_migrationInProgress) {
      print('Migration already in progress, skipping...');
      return;
    }

    _migrationInProgress = true;

    try {
      print('Checking for pending migrations...');
      
      final database = DatabaseService().database;
      
      // Run tax migration
      final taxMigrationService = TaxMigrationService(database);
      if (await taxMigrationService.isMigrationNeeded()) {
        print('Tax migration needed, starting...');
        await taxMigrationService.performMigration();
        print('Tax migration completed');
      } else {
        print('Tax migration not needed');
      }

      // Add other migrations here as needed
      // e.g., await _runOtherMigration();

      print('All migrations completed');
    } catch (e) {
      print('Error during migration: $e');
      rethrow;
    } finally {
      _migrationInProgress = false;
    }
  }

  /// Check if any migrations are pending
  static Future<bool> hasPendingMigrations() async {
    try {
      final database = DatabaseService().database;
      
      // Check tax migration
      final taxMigrationService = TaxMigrationService(database);
      if (await taxMigrationService.isMigrationNeeded()) {
        return true;
      }

      // Add other migration checks here
      
      return false;
    } catch (e) {
      print('Error checking for pending migrations: $e');
      return false;
    }
  }

  /// Force run tax migration (for testing/debugging)
  static Future<void> forceTaxMigration() async {
    final database = DatabaseService().database;
    final taxMigrationService = TaxMigrationService(database);
    await taxMigrationService.performMigration();
  }

  /// Rollback tax migration (for testing/debugging)
  static Future<void> rollbackTaxMigration() async {
    final database = DatabaseService().database;
    final taxMigrationService = TaxMigrationService(database);
    await taxMigrationService.rollbackMigration();
  }
}
