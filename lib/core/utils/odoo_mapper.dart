import '../db/drift/database.dart';
import '../models/drift/DriftSyncWrappers.dart';
import '../models/interfaces/DriftSyncClass.dart';
import '../models/drift/ResPartnerExtensions.dart';

/// Utility class to map between app models and Odoo models
class OdooMapper {
  // Map of app model types to Odoo model names
  static const Map<String, String> modelMapping = {
    'businesses': 'res.company',
    'clients': 'res.partner',
    'categories': 'product.category',
    'products': 'product.product',
    'invoices': 'account.move',
    'currencies': 'res.currency',
  };

  // Convert app model to Odoo model
  static Future<Map<String, dynamic>> toOdooModel(String type, DriftSyncClass entity) async {
    // Get the JSON representation of the entity
    Map<String, dynamic> json = await entity.toSyncJson();

    // Process the JSON for Odoo compatibility
    _processJsonForOdoo(json);

    return json;
  }

  // Process JSON data for Odoo compatibility
  static void _processJsonForOdoo(Map<String, dynamic> json) {
    // According to Odoo documentation, Many2one fields should be sent as integers, not arrays
    // The [id, name] format is only used when READING from Odoo, not when WRITING
    // Reference: https://www.odoo.com/documentation/18.0/developer/reference/backend/orm.html
    // "For Many2one, the value should be the database identifier of the record to set"

    // Many2one fields should remain as integers when sending to Odoo
    // No conversion to array format needed

    // Remove fields that shouldn't be sent to Odoo
    final fieldsToRemove = [
      // Common fields to remove
      'id', 'universal_id', 'is_synced', 'synced', 'version',
      'is_deleted', 'is_confirmed', 'confirmed', 'origin_id',
      'currency', 'status', // App-specific fields not in Odoo models

      // Invalid fields on res.company model
      'favicon', 'prefix', 'last_used_id', 'payment_info', 'logo_web',

      // Other app-specific fields
      'currency_symbol', 'quick_edit_mode', 'tax_rate', 'is_order'
    ];

    for (var field in fieldsToRemove) {
      json.remove(field);
    }

    // Remove null values
    json.removeWhere((key, value) => value == null);
  }

  // Convert Odoo model to app model
  static DriftSyncClass fromOdooModel(String type, Map<String, dynamic> odooData) {
    // Process the Odoo data for app compatibility
    Map<String, dynamic> processedData = _processOdooData(odooData);

    // Create the appropriate model instance based on type
    switch (type) {
      case 'businesses':
        // Create a ResCompanyTableData instance and wrap it in a ResCompanySync
        return ResCompanySync(ResCompanyTableData.fromJson(processedData));

      case 'clients':
        return ResPartnerSync(ResPartnerTableData.fromJson(processedData));

      case 'categories':
        return ProductCategorySync(ProductCategoryTableData.fromJson(processedData));

      case 'products':
        return ProductProductSync(ProductProductTableData.fromJson(processedData));

      case 'invoices':
        return AccountMoveSync(AccountMoveTableData.fromJson(processedData));

      case 'currencies':
        return ResCurrencySync(ResCurrencyTableData.fromJson(processedData));

      default:
        throw Exception('Unknown entity type: $type');
    }
  }

  // Process Odoo data for app compatibility
  static Map<String, dynamic> _processOdooData(Map<String, dynamic> odooData) {
    // Create a copy of the data to avoid modifying the original
    Map<String, dynamic> processedData = Map.from(odooData);

    // Map Odoo ID to universal_id
    if (processedData.containsKey('id')) {
      processedData['universal_id'] = processedData['id'];
    }

    // Set sync fields with explicit boolean values (not null)
    processedData['is_synced'] = true;
    processedData['is_confirmed'] = true;
    processedData['is_deleted'] = false;
    processedData['is_order'] = processedData['is_order'] ?? false; // Default to false if not present
    processedData['version'] = 1;

    // Handle special fields
    if (processedData.containsKey('write_date')) {
      processedData['last_modified'] = processedData['write_date'];
    }

    // Fields that should actually be boolean values in the app's database
    final booleanFields = [
      // App-specific boolean fields
      'is_synced', 'is_confirmed', 'is_deleted', 'is_order',

      // Odoo boolean fields that are stored as boolean in our database
      'purchase_ok',

      // Odoo boolean fields that are stored as integers in our database
      // These should NOT be included here as they need to be converted to null
      // 'active', 'is_company', 'sale_ok',
      // 'reconcile', 'auto_post', 'tax_exigible', 'always_set_currency_id',
      // 'include_initial_balance', 'force_delete'
    ];

    // Create a new map with processed values
    Map<String, dynamic> updatedData = {};

    // Process each field in the Odoo data
    processedData.forEach((key, value) {
      if (value is List && value.length == 2 && value[0] is int) {
        // For array fields like [id, name], just use the ID (first element)
        // For fields that are stored as text in the database but come as [id, name] arrays from Odoo,
        // convert the ID to a string
        if (key == 'invoice_payment_term_id' || key == 'invoice_user_id') {
          updatedData[key] = value[0].toString();
        } else {
          updatedData[key] = value[0];
        }
      } else if (value == false) {
        // Handle false values based on the field type
        if (booleanFields.contains(key)) {
          // For actual boolean fields, preserve as false
          updatedData[key] = false;
        } else {
          // For all other fields (integers, strings, etc.), convert false to null
          updatedData[key] = null;
        }
      } else {
        // Keep other values as they are
        updatedData[key] = value;
      }
    });

    return updatedData;
  }
}
