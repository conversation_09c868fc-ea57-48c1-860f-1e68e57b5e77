import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'UserPreference.dart';

final sharedPreferencesServiceProvider =
    Provider<SharedPreferencesService>((ref) => throw UnimplementedError());


class SharedPreferencesService {
  SharedPreferencesService(this.sharedPreferences);
  final SharedPreferences sharedPreferences;

  static const onboardingCompleteKey = 'onboardingComplete';
  static const kUserPwdKey = 'kUserPwdKey';

  Future<void> setOnboardingComplete() async {
    await sharedPreferences.setBool(onboardingCompleteKey, true);
  }

  Future<void> cacheUserCredentials(Map<String, dynamic> auth) async {
    await sharedPreferences.setString(kUserPwdKey, json.encode(auth));
  }

  Future<void> resetUserCredentials() async {
    await sharedPreferences.remove(kUserPwdKey);
    await sharedPreferences.remove(UserPreference.userId);
    await sharedPreferences.remove(UserPreference.firstName);
    await sharedPreferences.remove(UserPreference.lastName);
    await sharedPreferences.remove(UserPreference.lastSyncDate);
    await sharedPreferences.remove(UserPreference.email);
    await sharedPreferences.remove(UserPreference.refreshToken);
    await sharedPreferences.remove(UserPreference.accessToken);
    await sharedPreferences.remove(UserPreference.activeClient);
    await sharedPreferences.remove(UserPreference.activeBusiness);
    await sharedPreferences.remove(UserPreference.activeBusinessName);
    await sharedPreferences.remove(UserPreference.logos);

  }

  Map<String, dynamic>? getCachedUserCredentials() {
    final res = sharedPreferences.getString(kUserPwdKey);

    if (res != null) {
      return json.decode(res) as Map<String, dynamic>;
    }

    return null;
  }

  Map<String, dynamic>? getSkipSignIn() {
    final res = sharedPreferences.getString(kUserPwdKey);

    if (res != null) {
      return json.decode(res) as Map<String, dynamic>;
    }

    return null;

  }

  bool skipSignIn() =>    sharedPreferences.getBool("skip") ?? false;

  bool isOnboardingComplete() =>    sharedPreferences.getBool(onboardingCompleteKey) ?? false;
}
