import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/constants.dart';
import '../exceptions/exception_handler.dart';
import '../utils/UserPreference.dart';

/// Create a singleton class to contain all Dio methods and helper functions
class DioClient {
  DioClient._();

  static final instance = DioClient._();



  final Dio _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
        responseType: ResponseType.json,
      )
  );

  final Options options = Options(
      headers: {
        'user-agent': 'dio',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
  );


  ///Get Method
  Future <dynamic> get(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    options.headers!.addAll({'Authorization': 'Bearer $accessToken'});

    try{
      final Response response = await _dio.get(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e) {
      throw exceptionHandler(e, 'profile request');
    }
    // } catch(e){
    //   rethrow;
    // }
  }

  ///Post Method
  Future<dynamic> post(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        bool? ignoreAuthToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    if(accessToken!=null && ignoreAuthToken!=true) {
      options.headers!.addAll({'Authorization': 'Bearer $accessToken'});
    }

    try{
      final Response response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e, st) {
      print('$e \n$st');
      throw exceptionHandler(e, 'request');
    }
  }///Post Method
  Future<dynamic> postRaw(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        bool? ignoreAuthToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{

    try{
      final Response response = await _dio.post(
        path,
        data: data,
      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e,st) {
      print('$e \n$st');
      throw exceptionHandler(e, 'request');
      ;
    }
  }

  ///Put Method
  Future<dynamic> put(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    options.headers!.addAll({'Authorization': 'Bearer $accessToken'});

    try{
      final Response response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        // options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      if(response.statusCode == 200){
        return response.data;
      }
      if (response.data != null) {
        return response.data;
      }
    }catch(e,st) {
      print('$e \n$st');
      throw exceptionHandler(e, 'profile request');
      ;
    }
  }

  ///Delete Method
  Future<dynamic> delete(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    options.headers!.addAll({'Authorization': 'Bearer $accessToken'});

    try{

      final Response response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,

      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e,st) {
      print('$e \n$st');
      throw exceptionHandler(e, 'profile request');
      ;
    }
  }

  ///Post to Odoo JSON-RPC endpoint for authentication
  Future<dynamic> postOdooJsonRpc(
      String odooBaseUrl,
      String database,
      String username,
      String password,
      [Map<String, dynamic>? context]) async {
    try {
      final Map<String, dynamic> data = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "common",
          "method": "authenticate",
          "args": [
            database,
            username,
            password,
            context ?? {}
          ]
        }
      };

      final Dio odooClient = Dio(
        BaseOptions(
          baseUrl: odooBaseUrl,
          connectTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
          responseType: ResponseType.json,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        )
      );

      final Response response = await odooClient.post(
        odooJsonRpcEndpoint,
        data: data,
      );

      if (response.data != null) {
        return response.data;
      }
    } catch(e,st) {
      print('$e \n$st');
      throw exceptionHandler(e, 'Odoo authentication request');
    }
    return null;
  }

  ///Get user profile from Odoo
  Future<dynamic> getOdooUserProfile(
      String odooBaseUrl,
      String database,
      int userId,
      String username,
      String password,
      [Map<String, dynamic>? context]) async {
    try {
      // First authenticate to get the session info
      final authResult = await postOdooJsonRpc(
        odooBaseUrl,
        database,
        username,
        password,
        context
      );

      if (authResult == null || authResult['result'] == false) {
        throw Exception('Authentication failed: Invalid credentials');
      }

      // Now make a call to get user information
      final Map<String, dynamic> data = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            userId,
            password,
            "res.users",
            "read",
            [[userId]],
            {
              "fields": [
                "id", "name", "login", "email", "phone",
                "partner_id", "company_id", "company_ids"
              ]
            }
          ]
        }
      };

      final Dio odooClient = Dio(
        BaseOptions(
          baseUrl: odooBaseUrl,
          connectTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
          responseType: ResponseType.json,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        )
      );

      final Response response = await odooClient.post(
        odooJsonRpcEndpoint,
        data: data,
      );

      if (response.data != null) {
        // Log the response for debugging
        print('Odoo API response: ${response.data}');
        return response.data;
      }
    } catch(e, st) {
      print('Error in getOdooUserProfile: $e \n $st');
      throw exceptionHandler(e, 'Odoo user profile request');
    }
    return null;
  }

  ///Register a new user in Odoo
  Future<dynamic> postOdooRegistration(
      String odooBaseUrl,
      String database,
      String email,
      String password,
      String firstName,
      String lastName,
      [Map<String, dynamic>? context]) async {
    try {
      // For Odoo registration, we need to create a partner and then a user
      // This is a simplified implementation - in a real app, you might need to
      // adjust this based on your Odoo configuration and permissions

      // First authenticate as admin or a user with create rights
      // Note: In a real app, you would use a service account or API key
      final authResult = await postOdooJsonRpc(
        odooBaseUrl,
        database,
        "admin", // This should be a user with create rights
        "admin", // This should be the password for that user
        context
      );

      if (authResult == null || authResult['result'] == false) {
        throw Exception('Authentication failed: Admin credentials invalid');
      }

      final adminUserId = authResult['result'];

      // Create a new user
      final Map<String, dynamic> data = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            adminUserId,
            "admin", // Admin password
            "res.users",
            "create",
            [{
              "login": email,
              "password": password,
              "name": "$firstName $lastName",
              "email": email,
            }]
          ]
        }
      };

      final Dio odooClient = Dio(
        BaseOptions(
          baseUrl: odooBaseUrl,
          connectTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
          responseType: ResponseType.json,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        )
      );

      final Response response = await odooClient.post(
        odooJsonRpcEndpoint,
        data: data,
      );

      if (response.data != null) {
        // Log the response for debugging
        print('Odoo registration response: ${response.data}');

        // Check for errors in the response
        if (response.data['error'] != null) {
          throw Exception('Registration failed: ${response.data['error']['message']}');
        }

        return response.data;
      }
    } catch(e, st) {
      print('Error in postOdooRegistration: $e \n $st');
      throw exceptionHandler(e, 'Odoo registration request');
    }
    return null;
  }
}