// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/core/constants/color_constants.dart';
// import 'package:invoicer/core/providers/client/ClientsRequest.dart';
// import 'package:invoicer/core/utils/colorful_tag.dart';
// import 'package:flutter/material.dart';
// import 'package:go_router/go_router.dart';
// import 'package:invoicer/screens/receipt/edit/receipt_home_screen.dart';
// import '../../../core/models/Client.dart';
// import '../../../core/models/Invoice.dart';
// import '../../../core/providers/client/client_repository.dart';
// import '../../../core/providers/invoice/InvoicesRequest.dart';
// import '../../../core/providers/invoice/invoice_repository.dart';
// import '../../../core/utils/responsive.dart';
//
// class ReceiptList extends ConsumerStatefulWidget {
//   const ReceiptList({
//     Key? key,
//   }) : super(key: key);
//
//   @override
//   _ReceiptListState createState() => _ReceiptListState();
// }
//
//
// class _ReceiptListState extends ConsumerState<ReceiptList> {
//
//
//   List<Invoice> invoices = [Invoice()];
//   List<Client> clients = [Client()];
//
//   String filter = 'ALL';
//   String filter2 = 'CLIENTS';
//   String dateSort = 'asc';
//
//   int page_size = 25;
//
//   int page_number = 0;
//   InvoicesRequest req =  new InvoicesRequest();
//
//   int totalItems = 0;
//
//   Future<void> _initInvoices() async {
//     var res = (await ref.read(invoicesRepositoryProvider).getInvoices(req));
//     invoices =  res.content;
//     clients = (await ref.read(clientsRepositoryProvider).getClients(ClientsRequest())).content;
//     setState(() {});
//   }
//
//   @override
//   void initState() {
//     super.initState();
//
//     _initInvoices();
//   }
//
//
//   @override
//   Widget build(BuildContext context) {
//
//     return Container(
//       padding: EdgeInsets.all(defaultPadding),
//       decoration: BoxDecoration(
//         // color:  Theme.of(context).colorScheme.surface,
//         borderRadius: const BorderRadius.all(Radius.circular(10)),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//
//             children: [
//               Text(
//                 "Receipt List",
//                 style: Theme.of(context).textTheme.subtitle1,
//               ),
//
//               Row(children: [
//                 Container(
//                   decoration: BoxDecoration(
//                     color: getRoleColor("R").withOpacity(.2),
//                     borderRadius: const BorderRadius.all(Radius.circular(10)),
//                     // border: Border.all(color: Colors.white),
//                   ),
//                   child: TextButton(
//                     child: SizedBox(child: Text(filter2, style: Theme.of(context).textTheme.titleMedium,),),
//                     onPressed: () {
//                       showDialog(
//                           context: context,
//                           builder: (_) {
//                             return AlertDialog(
//                                 content: Container(
//                                   // color:  Theme.of(context).colorScheme.surface,
//                                   // height: 450,
//                                   child: SingleChildScrollView(
//                                     child: Column(
//                                       children: [
//                                         Container(
//                                           // margin: EdgeInsets.only(left: defaultPadding),
//                                           // padding: EdgeInsets.symmetric(
//                                           //   horizontal: defaultPadding,
//                                           //   vertical: defaultPadding / 2,
//                                           // ),
//                                           decoration: BoxDecoration(
//                                             // color:  Theme.of(context).colorScheme.surface,
//                                             borderRadius: const BorderRadius.all(Radius.circular(10)),
//                                             border: Border.all(color: Theme.of(context).colorScheme.outline),
//                                           ),
//                                           child: TextButton(
//                                             child: Text("All CLIENTS", ),
//                                             onPressed: () {
//                                               filter2 = 'CLIENTS';
//                                               _initInvoices();
//                                               context.pop();
//                                             },
//                                             // Delete
//                                           ),
//
//                                         ),
//                                         SizedBox(height: 7,),
//
//
//                                         Column(
//                                           children:
//                                           List.generate(
//                                               clients.length,
//                                                   (index) =>
//
//
//                                                   Container(
//                                                     margin: EdgeInsets.only(bottom: 7),
//                                                     // padding: EdgeInsets.symmetric(
//                                                     //   horizontal: defaultPadding,
//                                                     //   vertical: defaultPadding / 2,
//
//                                                     // ),
//                                                     decoration: BoxDecoration(
//                                                       // color:  Theme.of(context).colorScheme.surface,
//                                                       borderRadius: const BorderRadius.all(Radius.circular(10)),
//                                                       border: Border.all(color: Theme.of(context).colorScheme.outline),
//                                                     ),
//                                                     child: TextButton(
//                                                       child: Text(clients[index].name!, ),
//                                                       onPressed: () {
//                                                         filter2 = clients[index]!.id.toString()!;
//                                                         _initInvoices();
//                                                         setState(() {
//                                                         });
//                                                         context.pop();
//                                                       },
//                                                       // Delete
//                                                     ),
//
//                                                   )
//                                           ),
//
//
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ));
//                           });
//                     },
//                     // Delete
//                   ),
//
//                 ),
//               ],)
//
//
//             ],
//           ),
//           SizedBox(
//             width: double.infinity,
//             child: DataTable(
//               horizontalMargin: 0,
//               columnSpacing: defaultPadding,
//               columns: [
//                 DataColumn(
//                   label: Text("Id"),
//                 ),DataColumn(
//                   label: Text("Client"),
//                 ),
//                 DataColumn(
//                     label: GestureDetector(
//                       child: Container(
//                         // margin: EdgeInsets.only(left: defaultPadding/4),
//                           padding: EdgeInsets.all(defaultPadding/3 ),
//                           decoration: BoxDecoration(
//                             // color:  Theme.of(context).colorScheme.surface,
//                             borderRadius: const BorderRadius.all(Radius.circular(10)),
//                             border: Border.all(color: Theme.of(context).colorScheme.outline),
//                           ),
//                           child:Text("Date")),
//                       onTap: (){
//                         if(dateSort=='desc') {
//                           dateSort = 'asc';
//                         }else if (dateSort=='asc') {
//                           dateSort = 'desc';
//                         }
//                         _initInvoices();
//                         setState(() { });
//                       },
//                     )
//                 ),
//                 DataColumn(
//                   label: Text("Amount"),
//                 ),
//                 DataColumn(
//                   label: Text("Actions"),
//                 ),
//               ],
//               rows: List.generate(
//                 invoices.length,
//                     (index) => recentUserDataRow(invoices[index]),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   DataRow recentUserDataRow(Invoice userInfo) {
//     return DataRow(
//       cells: [
//         DataCell(Text(userInfo.id.toString()!)),
//         DataCell(Container(
//             padding: EdgeInsets.all(5),
//             decoration: BoxDecoration(
//               color: getRoleColor(userInfo.client.toString()).withOpacity(.2),
//               // border: Border.all(color: Colors.lightBlueAccent),
//               borderRadius: BorderRadius.all(Radius.circular(5.0) //
//               ),
//             ),
//             child: Text(userInfo.client != null ? userInfo.client!.name??"" : ""))),
//         DataCell(Text(userInfo.invoice_date.toString().split(" ")[0])),
//         DataCell(Text(userInfo.totalAmount.toString())),
//         DataCell(Row(
//           children: [
//             Responsive.isDesktop(context) ? ElevatedButton.icon(
//               style: ElevatedButton.styleFrom(
//                 primary: mainColor,
//               ),
//               icon: Icon(
//                 Icons.remove_red_eye,
//                 size: 14,
//               ),
//               onPressed: () {
//                 Navigator.of(context).push(new MaterialPageRoute<Null>(
//                     builder: (BuildContext context) {
//                       return new ReceiptHomeScreen(title: "View Receipt: ${userInfo.id}", code: "edit", invoiceId: userInfo.id );
//                     },
//                     fullscreenDialog: true));
//               },
//               // Edit
//               label: Text("View"),
//             ) :
//
//             GestureDetector(
//               onTap:(){
//                 Navigator.of(context).push(new MaterialPageRoute<Null>(
//                     builder: (BuildContext context) {
//                       return new ReceiptHomeScreen(title: "Edit Receipt: ${userInfo.id}", code: "edit", invoiceId: userInfo.id );
//                     },
//                     fullscreenDialog: true));
//               },
//               child:Icon(Icons.edit, color:mainColor),
//             ),
//             SizedBox(
//               width: 6,
//             ),
//             // SizedBox(
//             //   width: 6,
//             // ),
//             // Responsive.isDesktop(context)
//             //     ? ElevatedButton.icon(
//             //   style: ElevatedButton.styleFrom(
//             //     primary: Colors.red.withOpacity(0.5),
//             //   ),
//             //   icon: Icon(Icons.delete),
//             //   onPressed: () {
//             //     showDialog(
//             //         context: context,
//             //         builder: (_) {
//             //           return AlertDialog(
//             //               title: Center(
//             //                 child: Text("Confirm Deletion"),
//             //               ),
//             //               content: Container(
//             //                 color:  Theme.of(context).colorScheme.surface,
//             //                 height: 70,
//             //                 child: Column(
//             //                   children: [
//             //                     Text(
//             //                         "Are you sure want to delete invoice: ${userInfo.id}'?"),
//             //                     SizedBox(
//             //                       height: 16,
//             //                     ),
//             //                     Row(
//             //                       mainAxisAlignment: MainAxisAlignment.center,
//             //                       children: [
//             //                         ElevatedButton.icon(
//             //                             icon: Icon(
//             //                               Icons.close,
//             //                               size: 14,
//             //                             ),
//             //                             style: ElevatedButton.styleFrom(
//             //                                 primary: Colors.grey),
//             //                             onPressed: () {
//             //                               context.pop();
//             //                             },
//             //                             label: Text("Cancel")),
//             //                         SizedBox(
//             //                           width: 20,
//             //                         ),
//             //                         ElevatedButton.icon(
//             //                             icon: Icon(
//             //                               Icons.delete,
//             //                               size: 14,
//             //                             ),
//             //                             style: ElevatedButton.styleFrom(
//             //                                 primary: Colors.red),
//             //                             onPressed: () {
//             //                               try{
//             //                                 userInfo.delete();
//             //                                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//             //                                   content: Text("Invoice deleted successfully"),
//             //                                 ));
//             //                               }catch(e){
//             //                                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//             //                                   content: Text("An error occured while deleting"),
//             //                                 ));
//             //                               }
//             //                               context.pop();
//             //                               Navigator.push(
//             //                                 context,
//             //                                 MaterialPageRoute(builder: (context) => RegisterHomeScreen()),
//             //                               );
//             //                             },
//             //                             label: Text("Delete"))
//             //                       ],
//             //                     )
//             //                   ],
//             //                 ),
//             //               ));
//             //         });
//             //   },
//             //   // Delete
//             //   label: Text("Delete"),
//             // )
//             //     : GestureDetector(
//             //     onTap: (){
//             //       showDialog(
//             //           context: context,
//             //           builder: (_) {
//             //             return AlertDialog(
//             //                 title: Center(
//             //                   child: Text("Confirm Deletion"),
//             //                 ),
//             //                 content: Container(
//             //                   color:  Theme.of(context).colorScheme.surface,
//             //                   height: 70,
//             //                   child: Column(
//             //                     children: [
//             //                       Text(
//             //                           "Are you sure want to delete invoice: '${userInfo.id}'?"),
//             //                       SizedBox(
//             //                         height: 16,
//             //                       ),
//             //                       Row(
//             //                         mainAxisAlignment: MainAxisAlignment.center,
//             //                         children: [
//             //                           ElevatedButton.icon(
//             //                               icon: Icon(
//             //                                 Icons.close,
//             //                                 size: 14,
//             //                               ),
//             //                               style: ElevatedButton.styleFrom(
//             //                                   primary: Colors.grey),
//             //                               onPressed: () {
//             //                                 context.pop();
//             //                               },
//             //                               label: Text("Cancel")),
//             //                           SizedBox(
//             //                             width: 20,
//             //                           ),
//             //                           ElevatedButton.icon(
//             //                               icon: Icon(
//             //                                 Icons.delete,
//             //                                 size: 14,
//             //                               ),
//             //                               style: ElevatedButton.styleFrom(
//             //                                   primary: Colors.red),
//             //                               onPressed: () {
//             //                                 try{
//             //                                   userInfo.delete();
//             //                                   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//             //                                     content: Text("Invoice deleted successfully"),
//             //                                   ));
//             //                                 }catch(e){
//             //                                   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//             //                                     content: Text("An error occured while deleting"),
//             //                                   ));
//             //                                 }
//             //                                 context.pop();
//             //                                 Navigator.push(
//             //                                   context,
//             //                                   MaterialPageRoute(builder: (context) => RegisterHomeScreen()),
//             //                                 );
//             //                               },
//             //                               label: Text("Delete"))
//             //                         ],
//             //                       )
//             //                     ],
//             //                   ),
//             //                 ));
//             //           });
//             //     } ,
//             //     child: Icon( Icons.delete, color: Colors.red.withOpacity(0.5),)
//             // ),
//           ],
//         ),),
//       ],
//     );
//   }
//
// }
//
