// import 'package:invoicer/core/constants/color_constants.dart';
//
// import 'package:invoicer/core/utils/responsive.dart';
//  import 'package:flutter/material.dart';
//
// import '../receipts_home_screen.dart';
//
//
// class ReceiptHeader extends StatelessWidget {
//
//
//   const ReceiptHeader({
//     Key? key,required this.title
//   }) : super(key: key);
//   final String title;
//
//   @override
//   Widget build(BuildContext context) {
//     final Size _size = MediaQuery.of(context).size;
//     return Column(
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             SizedBox(
//               width: 10,
//             ),
//             Text(title, style: TextStyle(fontSize: 20) ),
//             ElevatedButton.icon(
//               style: TextButton.styleFrom(
//                 backgroundColor: Colors.red,
//                 padding: EdgeInsets.symmetric(
//                   horizontal: defaultPadding * 1.5,
//                   vertical:
//                   defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
//                 ),
//               ),
//               onPressed: () {
//                 Navigator.push(
//                   context,
//                   MaterialPageRoute(builder: (context) => ReceiptsHomeScreen()),
//                 );
//
//
//               },
//               icon: Icon(Icons.cancel),
//               label: Text(
//                 "Close",
//               ),
//             ),
//           ],
//         ),
//         SizedBox(height: defaultPadding),
//         // Responsive(
//         //   mobile: InformationCard(
//         //     crossAxisCount: _size.width < 650 ? 2 : 4,
//         //     childAspectRatio: _size.width < 650 ? 1.2 : 1,
//         //   ),
//         //   tablet: InformationCard(),
//         //   desktop: InformationCard(
//         //     childAspectRatio: _size.width < 1400 ? 1.2 : 1.4,
//         //   ),
//         // ),
//       ],
//     );
//   }
// }
