import 'package:flutter/material.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';

import '../../components/tile.dart';
// import '../components/tile.dart';

class ProductGrid extends StatelessWidget {
  final List<ProductCategoryTableData> categories;
  final List<ProductProductTableData> products;
  final int crossAxisCount;
  final double childAspectRatio;
  final Function(int) onCategorySelected;
  final Function(ProductProductTableData) onProductSelected;

  const ProductGrid({
    Key? key,
    required this.categories,
    required this.products,
    required this.crossAxisCount,
    required this.childAspectRatio,
    required this.onCategorySelected,
    required this.onProductSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        children: [
          GridView.builder(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: categories.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: defaultPadding,
              mainAxisSpacing: defaultPadding,
              childAspectRatio: childAspectRatio,
            ),
            itemBuilder: (context, index) => GestureDetector(
              child: TileWidget(
                title: categories[index].name ?? "",
                color: Colors.lightBlueAccent,
              ),
              onTap: () {
                onCategorySelected(categories[index].id);
              },
            ),
          ),
          SizedBox(height: defaultPadding),
          GridView.builder(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: products.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: defaultPadding,
              mainAxisSpacing: defaultPadding,
              childAspectRatio: childAspectRatio,
            ),
            itemBuilder: (context, index) => GestureDetector(
              child: TileWidget(
                title: products[index].name ?? "",
                color: Colors.lightGreen,
              ),
              onTap: () {
                onProductSelected(products[index]);
              },
            ),
          ),
        ],
      ),
    );
  }
}
