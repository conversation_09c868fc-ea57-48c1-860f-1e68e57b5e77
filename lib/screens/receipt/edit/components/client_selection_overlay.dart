import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/utils/responsive.dart';

class ClientSelectionOverlay extends ConsumerWidget {
  final List<ResPartnerTableData> clients;
  final Function() onCancel;
  final Function() onNewClient;
  final Function(ResPartnerTableData) onClientSelected;

  const ClientSelectionOverlay({
    Key? key,
    required this.clients,
    required this.onCancel,
    required this.onNewClient,
    required this.onClientSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        SizedBox(height: 15),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton.icon(
              style: TextButton.styleFrom(
                backgroundColor: Colors.red,
                padding: EdgeInsets.symmetric(
                  horizontal: defaultPadding * 1.5,
                  vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                ),
              ),
              onPressed: onCancel,
              icon: Icon(Icons.cancel),
              label: Text("Cancel"),
            ),
            ElevatedButton.icon(
              style: TextButton.styleFrom(
                backgroundColor: mainColor,
                padding: EdgeInsets.symmetric(
                  horizontal: defaultPadding * 1.5,
                  vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                ),
              ),
              onPressed: onNewClient,
              icon: Icon(Icons.add),
              label: Text("New Client"),
            ),
          ],
        ),
        SingleChildScrollView(
          child: clients.isEmpty
              ? Text("No client with these details was found.")
              : Column(
                  children: List.generate(
                    clients.length,
                    (index) => GestureDetector(
                      child: ListTile(
                        title: Text(clients[index].name!),
                      ),
                      onTap: () {
                        onClientSelected(clients[index]);
                      },
                    )
                  ),
                ),
        ),
      ],
    );
  }
}
