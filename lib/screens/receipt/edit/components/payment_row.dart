import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_provider.dart'; 
import 'package:invoicer/core/repositories/drift/account_payment_repository_drift.dart';

DataRow paymentDataRow(
  AccountPaymentTableData payment,
  int index,
  BuildContext context,
  Function(int) onDelete
) {
  return DataRow(
    cells: [
      DataCell(
        Container(
          padding: EdgeInsets.all(5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(5.0)),
          ),
          child: Text(payment.id.toString())
        )
      ),
      DataCell(Text(payment.date?.toString().split(" ")[0] ?? "")),
      DataCell(Text(payment.amount?.toString() ?? "")),
      DataCell(
        Row(
          children: [
            GestureDetector(
              child: payment.amount != null ? Icon(Icons.delete, color: Colors.redAccent) : SizedBox(),
              onTap: () async {
                // Call the onDelete callback to update the UI
                onDelete(index);

                // Get the database instance
                final db = ProviderScope.containerOf(context).read(databaseProvider);

                // Create the repository
                final paymentRepository = AccountPaymentRepositoryDrift(db);

                // Delete the payment using the repository
                await paymentRepository.delete(payment.id);
              },
            ),
          ],
        ),
      )
    ],
  );
}
