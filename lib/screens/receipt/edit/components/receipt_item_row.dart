import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:drift/drift.dart' hide Column, Table;

DataRow receiptItemDataRow(
  AccountMoveLineTableData item,
  int index,
  BuildContext context,
  List<List<TextEditingController>> myController,
  Function(AccountMoveLineTableData) onDelete,
  Function(double) onValueChanged
) {
  myController[index][0].text = item.quantity?.toString() ?? "";
  myController[index][1].text = item.name?.toString() ?? "";
  myController[index][2].text = item.price_unit?.toString() ?? "";
  myController[index][3].text = item.price_total?.toString() ?? "";

  return DataRow(
    cells: [
      DataCell(
        Padding(
          padding: EdgeInsets.all(3),
          child: TextFormField(
            style: TextStyle(fontSize: 18),
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly
            ],
            initialValue: item.quantity?.toString() ?? "1",
            keyboardType: TextInputType.number,
            onChanged: (String value) {
              double quantity = 1.0;
              if (value.isNotEmpty) {
                quantity = double.parse(value);
              }

              double price_unit = item.price_unit ?? 0;
              double price_total = quantity * price_unit;

              // Update the UI
              myController[index][3].text = price_total.toString();

              // Create a new item with updated values
              item = item.copyWith(
                quantity: Value(quantity),
                price_total: Value(price_total)
              );

              onValueChanged(price_total);
            },
          ),
        )
      ),
      DataCell(
        Padding(
          padding: EdgeInsets.all(3),
          child: Text(
            myController[index][1].text,
            style: TextStyle(fontSize: 18),
          )
        )
      ),
      DataCell(
        Padding(
          padding: EdgeInsets.all(3),
          child: Text(
            myController[index][2].text,
            style: TextStyle(fontSize: 18),
          ),
        )
      ),
      DataCell(
        Padding(
          padding: EdgeInsets.all(3),
          child: Text(
            myController[index][3].text,
            style: TextStyle(fontSize: 18),
          ),
        )
      ),
      DataCell(
        Row(
          children: [
            item.move_id != null ?
            GestureDetector(
              child: Icon(Icons.delete, color: Colors.redAccent, size: 30),
              onTap: () {
                onDelete(item);
              },
            ) : SizedBox(width: 0),
          ],
        ),
      ),
    ],
  );
}
