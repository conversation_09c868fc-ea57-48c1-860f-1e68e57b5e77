import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart'; 
import 'package:invoicer/core/utils/responsive.dart';

class PaymentDialog extends ConsumerWidget {
  final AccountMoveTableData invoice;
  final double paymentAmount;
  final DateTime payDate;
  final Function(double) onPaymentAmountChanged;
  final Function(AccountMoveTableData, bool) onSave;
  final Function(AccountMoveTableData) onPrint;

  const PaymentDialog({
    Key? key,
    required this.invoice,
    required this.paymentAmount,
    required this.payDate,
    required this.onPaymentAmountChanged,
    required this.onSave,
    required this.onPrint,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AlertDialog(
      title: Center(
        child: Text("Amount Paid"),
      ),
      content: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "Total Paid:          ",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(
                          width: 80,
                          child: TextFormField(
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.allow(RegExp(r"[0-9.]"))
                            ],
                            keyboardType: TextInputType.number,
                            onChanged: (String value) {
                              if (value.isNotEmpty) {
                                onPaymentAmountChanged(double.parse(value));
                              }
                            },
                            initialValue: invoice.amount_total?.toString(),
                          ),
                        )
                      ]
                    ),
                  ),
                ],
              ),
              SizedBox(height: 15),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: TextButton.styleFrom(
                        backgroundColor: mainColor,
                        padding: EdgeInsets.symmetric(
                          horizontal: defaultPadding * 1.5,
                          vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                        ),
                      ),
                      onPressed: () {
                        onSave(invoice, false);
                      },
                      child: Text("Save"),
                    ),
                  ),
                  SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      style: TextButton.styleFrom(
                        backgroundColor: mainColor,
                        padding: EdgeInsets.symmetric(
                          horizontal: defaultPadding * 1.5,
                          vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                        ),
                      ),
                      onPressed: () {
                        onSave(invoice, true);
                      },
                      child: Text("Quick Print"),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 15),
            ],
          ),
        )
      )
    );
  }
}
