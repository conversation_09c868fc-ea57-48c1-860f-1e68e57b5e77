// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:invoicer/core/constants/color_constants.dart';
// import 'package:invoicer/core/db/drift/database.dart';
// import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
// import 'package:invoicer/core/utils/responsive.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:invoicer/core/utils/UserPreference.dart';
// import 'package:drift/drift.dart' as drift;
//
// class ReceiptForm extends ConsumerStatefulWidget {
//   final AccountMoveTableData? invoice;
//   final List<ResCurrencyTableData> currencies;
//   final List<List<TextEditingController>> myController;
//   final Function(AccountMoveTableData) onInvoiceChanged;
//   final LayerLink clientLayerLink;
//   final LayerLink productLayerLink;
//   final FocusNode clientFocusNode;
//   final FocusNode productFocusNode;
//
//   const ReceiptForm({
//     Key? key,
//     required this.invoice,
//     required this.currencies,
//     required this.myController,
//     required this.onInvoiceChanged,
//     required this.clientLayerLink,
//     required this.productLayerLink,
//     required this.clientFocusNode,
//     required this.productFocusNode,
//   }) : super(key: key);
//
//   @override
//   _ReceiptFormState createState() => _ReceiptFormState();
// }
//
// class _ReceiptFormState extends ConsumerState<ReceiptForm> {
//   String query = '';
//
//   @override
//   Widget build(BuildContext context) {
//     return Form(
//       child: Column(
//         children: [
//           CompositedTransformTarget(
//             link: widget.clientLayerLink,
//             child: TextFormField(
//               focusNode: widget.clientFocusNode,
//               keyboardType: TextInputType.text,
//               textCapitalization: TextCapitalization.words,
//               textInputAction: TextInputAction.next,
//               onChanged: (v) async {
//                 query = v;
//                 // This will be handled by the parent
//               },
//             )
//           ),
//           SizedBox(height: 10,),
//           Row(
//             mainAxisSize: MainAxisSize.min,
//             mainAxisAlignment: MainAxisAlignment.start,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Padding(padding: EdgeInsets.only(top: 5),
//                 child: Icon(Icons.person),
//               ),
//               SizedBox(width: 10,),
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text("Customer", style: TextStyle(fontSize: 20)),
//                   Text("Regular Customer", style: TextStyle(fontSize: 16)),
//                 ],
//               ),
//             ],
//           ),
//           SizedBox(height: 16.0),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//             children: [
//               Expanded(
//                 child: Row(
//                   children: [
//                     Text("Currency:      ", style: TextStyle(fontWeight: FontWeight.bold)),
//                     (widget.invoice != null && widget.invoice!.currencySymbol != null) ? Container(
//                       margin: EdgeInsets.only(left: 0),
//                       padding: EdgeInsets.symmetric(
//                         horizontal: defaultPadding / 2,
//                         vertical: defaultPadding / 3,
//                       ),
//                       decoration: BoxDecoration(
//                         borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
//                         border: Border.all(color: Theme.of(context).colorScheme.outline)
//                       ),
//                       child: GestureDetector(
//                         child: Text(widget.invoice!.currencySymbol!, style: TextStyle(fontSize: 18, color: primaryColor)),
//                         onTap: () {
//                           if (widget.invoice != null && widget.invoice!.id > 0) {
//                             ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                               content: Text("Cannot change currency of saved invoice"),
//                             ));
//                           } else {
//                             _showCurrencyDialog();
//                           }
//                         },
//                       ),
//                     ) : ElevatedButton.icon(
//                       icon: Icon(
//                         Icons.flag,
//                         size: 14,
//                       ),
//                       style: ElevatedButton.styleFrom(padding: EdgeInsets.all(10), backgroundColor: mainColor),
//                       label: Text(widget.invoice?.currencySymbol ?? "Select Currency"),
//                       onPressed: () {
//                         _showCurrencyDialog();
//                       },
//                     )
//                   ]
//                 ),
//               ),
//             ],
//           ),
//           SizedBox(height: 16.0),
//           CompositedTransformTarget(
//             link: widget.productLayerLink,
//             child: TextFormField(
//               focusNode: widget.productFocusNode,
//               keyboardType: TextInputType.text,
//               textCapitalization: TextCapitalization.words,
//               textInputAction: TextInputAction.next,
//               onChanged: (v) {
//                 // This will be handled by the parent
//               },
//             )
//           ),
//           SizedBox(height: 20),
//           Container(
//             padding: EdgeInsets.all(defaultPadding),
//             decoration: BoxDecoration(
//               color: Theme.of(context).colorScheme.surface,
//               borderRadius: const BorderRadius.all(Radius.circular(10)),
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 SingleChildScrollView(
//                   child: SizedBox(
//                     width: double.infinity,
//                     child: DataTable(
//                       horizontalMargin: 0,
//                       columnSpacing: defaultPadding,
//                       columns: [
//                         DataColumn(
//                           label: Text("Unit", style: TextStyle(fontSize: 18)),
//                         ),
//                         DataColumn(
//                           label: Text("Description", style: TextStyle(fontSize: 18)),
//                         ),
//                         DataColumn(
//                           label: Text("U Price", style: TextStyle(fontSize: 18)),
//                         ),
//                         DataColumn(
//                           label: Text("Amount", style: TextStyle(fontSize: 18)),
//                         ),
//                         DataColumn(
//                           label: Text(""),
//                         ),
//                       ],
//                       rows: [],
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           SizedBox(height: 20.0),
//           _buildTotalsSection(),
//           SizedBox(height: 10.0),
//           SizedBox(height: 10.0),
//           _buildActionButtons(),
//           SizedBox(height: 15.0),
//           SizedBox(height: 20.0),
//           SizedBox(height: 24.0),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildTotalsSection() {
//     return Column(
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.end,
//           children: [
//             Expanded(child: SizedBox(width: 600.0)),
//             Text("Subtotal", style: TextStyle(fontSize: 18)),
//             SizedBox(width: 60.0),
//             Container(
//               decoration: BoxDecoration(
//                 borderRadius: const BorderRadius.all(Radius.circular(10)),
//                 border: Border.all(color: Theme.of(context).colorScheme.surface),
//               ),
//               child: TextButton(
//                 child: Text((widget.invoice?.currencySymbol ?? '') + " " + (widget.invoice?.amountUntaxed ?? 0.0).toStringAsFixed(2), style: TextStyle(fontSize: 18)),
//                 onPressed: () {},
//               ),
//             ),
//             SizedBox(width: 16.0),
//           ],
//         ),
//         SizedBox(height: 5),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.end,
//           children: [
//             Expanded(child: SizedBox(width: 400.0)),
//             Text("Tax % ", style: TextStyle(fontSize: 18)),
//             SizedBox(width: 10.0),
//             SizedBox(
//               width: 50,
//               child: Padding(
//                 padding: EdgeInsets.all(3),
//                 child: TextFormField(
//                   style: TextStyle(fontSize: 18),
//                   inputFormatters: <TextInputFormatter>[
//                     FilteringTextInputFormatter.allow(RegExp(r"[0-9.]"))
//                   ],
//                   keyboardType: TextInputType.number,
//                   initialValue: '0.0',
//                   onChanged: (String? value) {
//                     try {
//                       // widget.invoice.tax_rate = double.parse(value ?? '0');
//                     } catch (e) {
//                       // widget.invoice.tax_rate = 0;
//                     }
//                     _updateInvoice();
//                   },
//                 ),
//               ),
//             ),
//             SizedBox(width: 60.0),
//             Container(
//               decoration: BoxDecoration(
//                 borderRadius: const BorderRadius.all(Radius.circular(10)),
//                 border: Border.all(color: Theme.of(context).colorScheme.surface),
//               ),
//               child: TextButton(
//                 child: Text('',
//                   // (widget.invoice.currency_symbol ?? '') + " " + ((widget.invoice.amount_untaxed ?? 0) * ((widget.invoice.tax_rate ?? 0) / 100)).toStringAsFixed(2),
//                   style: TextStyle(fontSize: 18),
//                 ),
//                 onPressed: () {},
//               ),
//             ),
//             SizedBox(width: 16.0),
//           ],
//         ),
//         SizedBox(height: 5),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.end,
//           children: [
//             Expanded(child: SizedBox(width: 600.0)),
//             Text("Total Due", style: TextStyle(fontSize: 18)),
//             SizedBox(width: 60.0, height: 15),
//             Container(
//               decoration: BoxDecoration(
//                 borderRadius: const BorderRadius.all(Radius.circular(10)),
//                 border: Border.all(color: Theme.of(context).colorScheme.surface),
//               ),
//               child: TextButton(
//                 child: Text(
//                   (widget.invoice?.currencySymbol ?? '') + " " + (widget.invoice?.amount_total ?? 0.0).toStringAsFixed(2),
//                   style: TextStyle(fontSize: 18),
//                 ),
//                 onPressed: () {},
//               ),
//             ),
//             SizedBox(width: 16.0),
//           ],
//         ),
//       ],
//     );
//   }
//
//   Widget _buildActionButtons() {
//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         ElevatedButton.icon(
//           style: TextButton.styleFrom(
//             backgroundColor: Colors.blue,
//             padding: EdgeInsets.symmetric(
//               horizontal: defaultPadding * 1.5,
//               vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
//             ),
//           ),
//           onPressed: () async {
//             // Create a new invoice using AccountMoveTableCompanion
//             final newInvoice = AccountMoveTableCompanion(
//               id: const drift.Value(0),
//               move_type: const drift.Value("RECEIPT"),
//               invoice_date: drift.Value(DateTime.now().toString().substring(0, 10)),
//               invoice_dateDue: drift.Value(DateTime.now().add(const Duration(days: 7)).toString().substring(0, 10)),
//               amountUntaxed: const drift.Value(0),
//               amountTax: const drift.Value(0),
//               amount_total: const drift.Value(0)
//             );
//
//             // Get active business ID
//             var prefs = await SharedPreferences.getInstance();
//             int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);
//
//             if (activeBusiness != null) {
//               // Create a new invoice with company ID
//               final companion = newInvoice.copyWith(
//                 company_id: drift.Value(activeBusiness)
//               );
//
//               // Save the new invoice
//               // final newId = await ref.read(accountMoveRepositoryProvider).save(companion);
//
//               // Get the newly created invoice
//               // final createdInvoice = await ref.read(accountMoveRepositoryProvider).getById(newId);
//               if (createdInvoice != null) {
//                 // widget.onInvoiceChanged(createdInvoice);
//               }
//             }
//           },
//           icon: Icon(Icons.delete),
//           label: Text("Reset", style: TextStyle(fontSize: 18)),
//         ),
//         SizedBox(width: 15),
//         if (widget.invoice == null || widget.invoice!.id <= 0)
//           ElevatedButton.icon(
//             style: TextButton.styleFrom(
//               backgroundColor: successColor,
//               padding: EdgeInsets.symmetric(
//                 horizontal: defaultPadding * 1.5,
//                 vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
//               ),
//             ),
//             onPressed: () async {
//               // Check if there are any invoice lines
//               if (widget.invoice == null) return;
//               final lines = await ref.read(accountMoveLineRepositoryProvider).getForInvoice(widget.invoice!.id);
//               if (lines.isEmpty) {
//                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                   content: Text("Please add billable items"),
//                 ));
//                 return null;
//               }
//               // Show payment dialog - this will be handled by parent
//             },
//             icon: Icon(Icons.save),
//             label: Text("Done", style: TextStyle(fontSize: 18)),
//           ),
//       ],
//     );
//   }
//
//   void _showCurrencyDialog() {
//     showDialog(
//       context: context,
//       builder: (_) {
//         return AlertDialog(
//           content: SingleChildScrollView(
//             child: Column(
//               children: List.generate(
//                 widget.currencies.length,
//                 (index) => Container(
//                   margin: EdgeInsets.only(bottom: defaultPadding),
//                   decoration: BoxDecoration(
//                     color: Theme.of(context).colorScheme.surface,
//                     borderRadius: const BorderRadius.all(Radius.circular(10)),
//                     border: Border.all(),
//                   ),
//                   child: TextButton(
//                     child: Text(widget.currencies[index].name),
//                     onPressed: () async {
//                       if (widget.invoice == null) return;
//
//                       // Use the AccountMoveTableCompanion to update the invoice
//                       final companion = AccountMoveTableCompanion(
//                         id: drift.Value(widget.invoice!.id),
//                         currencySymbol: drift.Value(widget.currencies[index].symbol),
//                         currencyId: drift.Value(widget.currencies[index].id),
//                       );
//
//                       // Save the updated invoice
//                       // await ref.read(accountMoveRepositoryProvider).save(companion);
//
//                       // Get the updated invoice
//                       final updatedInvoice = await ref.read(accountMoveRepositoryProvider).getById(widget.invoice!.id);
//                       if (updatedInvoice != null) {
//                         // widget.onInvoiceChanged(updatedInvoice);
//                       }
//
//                       _updateInvoice();
//                       context.pop();
//                     },
//                   ),
//                 )
//               ),
//             ),
//           )
//         );
//       }
//     );
//   }
//
//
//
//   void _updateInvoice() {
//     // Create a copy of the invoice with updated values
//     if (widget.invoice != null) {
//       widget.onInvoiceChanged(widget.invoice!);
//     }
//   }
// }
