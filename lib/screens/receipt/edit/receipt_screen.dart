// import 'dart:io';
//
// import 'package:flutter/services.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:invoicer/core/db/drift/database.dart';
// import 'package:drift/drift.dart' as drift;
// import '../../../core/utils/UserPreference.dart';
// import '../../client/edit/client_screen.dart';
// import '../components/receipt_header.dart';
// import '../../../core/utils/responsive.dart';
// import 'package:intl/intl.dart';
// import '../../../core/constants/color_constants.dart';
// import 'package:flutter/material.dart';
// import 'components/receipt_form.dart';
// import 'components/client_selection_overlay.dart';
// import 'components/product_selection_overlay.dart';
// import 'components/product_grid.dart';
//
//
// String? logoPath;
// String? invoicePath;
// String? invoiceName;
//
// class ReceiptScreen extends ConsumerStatefulWidget {
//   ReceiptScreen({required this.title, required this.code, this.invoiceId});
//   final String title;
//   final int? invoiceId;
//   final String code;
//
//   @override
//   _ReceiptScreenState createState() => _ReceiptScreenState(invoiceId);
// }
//
// class _ReceiptScreenState extends ConsumerState<ReceiptScreen> with TickerProviderStateMixin {
//   DateTime payDate = DateTime.now();
//   double paymentAmount = 0;
//   bool addPayment = false;
//   _ReceiptScreenState(int? this.invoiceId);
//   int? invoiceId;
//   DateFormat dateTimeFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
//   List<List<TextEditingController>> myController = [[TextEditingController(),TextEditingController(),TextEditingController(),TextEditingController()]];
//   int crossAxisCount = 2;
//   double childAspectRatio = 2;
//   List<ResCompanyTableData> companies = [];
//   List<ResPartnerTableData> clients = [];
//   List<ResCurrencyTableData> currencies = [];
//   AccountMoveTableData? invoice;
//   DateFormat dateFormat = DateFormat("yyyy-MM-dd");
//   List<ProductCategoryTableData> categories = [];
//   List<ProductProductTableData> products = [];
//   List<ProductProductTableData> productsSearch = [];
//
//   final FocusNode _focusNode = FocusNode();
//   final FocusNode _focusNode2 = FocusNode();
//   OverlayEntry? _overlayEntry;
//   final LayerLink _layerLink = LayerLink();
//   final LayerLink _layerLink2 = LayerLink();
//   OverlayState? overlayState;
//   Uint8List? logoBytes;
//
//   Future<void> _initInvoice() async {
//     // Get active business ID
//     var prefs = await SharedPreferences.getInstance();
//     int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);
//
//     if (activeBusiness == null) {
//       throw Exception("Go to settings and create or select active business.");
//     }
//
//     // Get logo
//     try {
//       String? path = await getApplicationDocumentsDirectory().then((dir) =>
//         "${dir.path}/logos/$activeBusiness.png");
//       if (path != null) {
//         File file = File(path);
//         if (await file.exists()) {
//           logoBytes = await file.readAsBytes();
//         }
//       }
//     } catch (e) {
//       print("Error loading logo: $e");
//     }
//
//     // Get categories and products using the updated repository providers
//     categories = await ref.read(productCategoryRepositoryProvider).getAll();
//     products = await ref.read(productProductRepositoryProvider).getForCompany(activeBusiness);
//
//     // Get currencies
//     currencies.clear();
//     currencies = await ref.read(resCurrencyRepositoryProvider).getAll();
//
//     // Get or create invoice
//     if (invoiceId != null) {
//       invoice = (await ref.read(accountMoveRepositoryProvider).getById(invoiceId!))?.invoice;
//     } else {
//       // Create a new invoice using AccountMoveTableCompanion
//       final newInvoice = AccountMoveTableCompanion(
//         id: const drift.Value(0),
//         move_type: const drift.Value("RECEIPT"),
//         invoice_date: drift.Value(dateFormat.format(DateTime.now())),
//         invoice_dateDue: drift.Value(dateFormat.format(DateTime.now().add(const Duration(days: 7)))),
//         amountUntaxed: const drift.Value(0),
//         amountTax: const drift.Value(0),
//         amount_total: const drift.Value(0),
//         company_id: drift.Value(activeBusiness)
//       );
//
//       // Save the new invoice to get an ID
//       // final newId = await ref.read(accountMoveRepositoryProvider).save(newInvoice);
//
//       // Fetch the newly created invoice
//       // invoice = await ref.read(accountMoveRepositoryProvider).getById(newId);
//     }
//
//     // Get companies and clients
//     companies = await ref.read(resCompanyRepositoryProvider).getAll();
//
//     // Get clients for the company
//     var allPartners = await ref.read(resPartnerRepositoryProvider).getForCompany(activeBusiness);
//     // Filter to only include customers (customerRank > 0)
//     clients = allPartners.where((client) => client.customerRank != null && client.customerRank! > 0).toList();
//
//     setState(() {});
//   }
//
//   void _updateInvoice(AccountMoveTableData updatedInvoice) {
//     setState(() {
//       invoice = updatedInvoice;
//     });
//   }
//
//   void _addProductToInvoice(ProductProductTableData product) async {
//     if (invoice == null) return;
//
//     setState(() {
//       // Create a new invoice line using AccountMoveLineTableCompanion
//       final line = AccountMoveLineTableCompanion(
//         id: const drift.Value(0),
//         move_id: drift.Value(invoice!.id),
//         product_id: drift.Value(product.id),
//         name: drift.Value(product.name ?? ""),
//         quantity: const drift.Value(1),
//         price_unit: drift.Value(product.list_price ?? 0),
//         price_total: drift.Value(product.list_price ?? 0)
//       );
//
//       // Save the line to the database
//       ref.read(accountMoveLineRepositoryProvider).save(line).then((_) {
//         // Add a new row of controllers for the UI
//         myController.add([
//           TextEditingController(),
//           TextEditingController(),
//           TextEditingController(),
//           TextEditingController()
//         ]);
//
//         // Update the invoice with new totals
//         _updateInvoiceTotals();
//       });
//     });
//   }
//
//   // Update invoice totals based on lines in the database
//   void _updateInvoiceTotals() async {
//     if (invoice == null) return;
//
//     // Get all lines for this invoice
//     final lines = await ref.read(accountMoveLineRepositoryProvider).getForInvoice(invoice!.id);
//
//     // Calculate totals
//     double total = 0;
//     for (var line in lines) {
//       total += line.price_total ?? 0;
//     }
//
//     // Update the invoice with new totals
//     final updatedInvoice = AccountMoveTableCompanion(
//       id: drift.Value(invoice!.id),
//       amountUntaxed: drift.Value(total),
//       amount_total: drift.Value(total)
//     );
//
//     // Save the updated invoice
//     // await ref.read(accountMoveRepositoryProvider).save(updatedInvoice);
//
//     // Refresh the invoice from the database
//     final refreshedInvoice = await ref.read(accountMoveRepositoryProvider).getById(invoice!.id);
//     if (refreshedInvoice != null) {
//       setState(() {
//         invoice = (refreshedInvoice).invoice;
//       });
//     }
//   }
//
//
//
//
//
//   void _showClientOverlay() {
//     if (_overlayEntry != null) {
//       _overlayEntry!.remove();
//       _overlayEntry = null;
//     }
//
//     _overlayEntry = OverlayEntry(
//       builder: (context) => Positioned(
//         width: Responsive.isMobile(context)
//           ? ((MediaQuery.of(context).size.width) - 26)
//           : ((MediaQuery.of(context).size.width / 2) - 26),
//         child: CompositedTransformFollower(
//           link: _layerLink,
//           showWhenUnlinked: false,
//           offset: Offset(0.0, 55.0),
//           child: Material(
//             elevation: 5.0,
//             child: ClientSelectionOverlay(
//               clients: clients,
//               onCancel: () {
//                 _overlayEntry?.remove();
//                 _overlayEntry = null;
//               },
//               onNewClient: () async {
//                 _overlayEntry?.remove();
//                 _overlayEntry = null;
//
//                 bool shouldUpdate = await showDialog(
//                   context: context,
//                   builder: (BuildContext context) {
//                     return AlertDialog(
//                       titlePadding: const EdgeInsets.all(0),
//                       contentPadding: const EdgeInsets.all(0),
//                       content: SingleChildScrollView(
//                         child: ClientScreen(title: 'New Client', code: 'quick'),
//                       ),
//                     );
//                   },
//                 );
//
//                 if (shouldUpdate) {
//                   // Get active business ID
//                   var prefs = await SharedPreferences.getInstance();
//                   int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);
//
//                   if (activeBusiness != null) {
//                     // Get clients for the company
//                     var allPartners = await ref.read(resPartnerRepositoryProvider).getForCompany(activeBusiness);
//                     // Filter to only include customers (customerRank > 0)
//                     clients = allPartners.where((client) => client.customerRank != null && client.customerRank! > 0).toList();
//                     setState(() {});
//                   }
//                 }
//               },
//               onClientSelected: (client) async {
//                 if (invoice == null) return;
//
//                 // Update the invoice with the selected partner
//                 final updatedInvoice = AccountMoveTableCompanion(
//                   id: drift.Value(invoice!.id),
//                   partner_id: drift.Value(client.id)
//                 );
//
//                 // Save the updated invoice
//                 // await ref.read(accountMoveRepositoryProvider).save(updatedInvoice);
//
//                 // Refresh the invoice from the database
//                 final refreshedInvoice = await ref.read(accountMoveRepositoryProvider).getById(invoice!.id);
//                 if (refreshedInvoice != null) {
//                   setState(() {
//                     invoice = (refreshedInvoice).invoice;
//                   });
//                 }
//                 _overlayEntry?.remove();
//                 _overlayEntry = null;
//               },
//             ),
//           ),
//         ),
//       ),
//     );
//
//     overlayState!.insert(_overlayEntry!);
//   }
//
//   void _showProductOverlay() {
//     if (_overlayEntry != null) {
//       _overlayEntry!.remove();
//       _overlayEntry = null;
//     }
//
//     _overlayEntry = OverlayEntry(
//       builder: (context) => Positioned(
//         width: Responsive.isMobile(context)
//           ? ((MediaQuery.of(context).size.width) - 26)
//           : ((MediaQuery.of(context).size.width / 2) - 26),
//         child: CompositedTransformFollower(
//           link: _layerLink2,
//           showWhenUnlinked: false,
//           offset: Offset(0.0, 55.0),
//           child: Material(
//             elevation: 5.0,
//             child: ProductSelectionOverlay(
//               products: productsSearch,
//               onCancel: () {
//                 _overlayEntry?.remove();
//                 _overlayEntry = null;
//               },
//               onProductSelected: (product) {
//                 _addProductToInvoice(product);
//                 _overlayEntry?.remove();
//                 _overlayEntry = null;
//               },
//             ),
//           ),
//         ),
//       ),
//     );
//
//     overlayState!.insert(_overlayEntry!);
//   }
//
//
//
//
//
//
//
//   @override
//   void dispose() {
//     myController.forEach((e) {
//       e.forEach((a) {
//         a.dispose();
//       });
//     });
//     _focusNode.dispose();
//     _focusNode2.dispose();
//     _overlayEntry?.remove();
//     super.dispose();
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     _initInvoice();
//
//     _focusNode.addListener(() {
//       if (_focusNode.hasFocus) {
//         _showClientOverlay();
//       } else {
//         _overlayEntry?.remove();
//         _overlayEntry = null;
//       }
//     });
//
//     _focusNode2.addListener(() {
//       if (_focusNode2.hasFocus) {
//         _showProductOverlay();
//       } else {
//         _overlayEntry?.remove();
//         _overlayEntry = null;
//       }
//     });
//
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       overlayState = Overlay.of(context);
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final Size size = MediaQuery.of(context).size;
//     crossAxisCount = size.width < 650 ? 2 : 4;
//     childAspectRatio = size.width < 650 ? 2 : 2;
//
//     return SafeArea(
//       child: SingleChildScrollView(
//         child: Container(
//           padding: EdgeInsets.all(defaultPadding),
//           child: Column(
//             children: [
//               SizedBox(height: defaultPadding),
//               ReceiptHeader(title: widget.title + (invoice != null ? ": " + (invoice!.company_id != null ? companies.firstWhere((c) => c.id == invoice!.company_id, orElse: () => ResCompanyTableData(
//                 id:0,
//                 name: "",
//                 is_synced: false,
//                 is_confirmed: true,
//                 is_deleted: false,
//                 version: 1
//               )).name : "") : "")),
//               SizedBox(height: defaultPadding),
//               Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Expanded(
//                     flex: 5,
//                     child: Column(
//                       children: [
//                         _buildMainContent(context),
//                         SizedBox(height: defaultPadding),
//                         if (Responsive.isMobile(context))
//                           SizedBox(height: defaultPadding),
//                       ],
//                     ),
//                   ),
//                   if (!Responsive.isMobile(context))
//                     SizedBox(width: defaultPadding),
//                 ],
//               )
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildMainContent(BuildContext context) {
//     return Container(
//       width: double.infinity,
//       constraints: BoxConstraints(
//         minHeight: MediaQuery.of(context).size.height - 0.0,
//       ),
//       child: Responsive(
//         mobile: Column(
//           children: [
//             invoice != null ? ReceiptForm(
//               invoice: invoice,
//               currencies: currencies,
//               myController: myController,
//               onInvoiceChanged: _updateInvoice,
//               clientLayerLink: _layerLink,
//               productLayerLink: _layerLink2,
//               clientFocusNode: _focusNode,
//               productFocusNode: _focusNode2,
//             ) : Center(child: CircularProgressIndicator()),
//             _buildProductGrid(),
//           ],
//         ),
//         tablet: Row(
//           mainAxisSize: MainAxisSize.min,
//           mainAxisAlignment: MainAxisAlignment.start,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             SizedBox(
//               width: (MediaQuery.of(context).size.width / 2) - 26,
//               child: invoice != null ? ReceiptForm(
//                 invoice: invoice,
//                 currencies: currencies,
//                 myController: myController,
//                 onInvoiceChanged: _updateInvoice,
//                 clientLayerLink: _layerLink,
//                 productLayerLink: _layerLink2,
//                 clientFocusNode: _focusNode,
//                 productFocusNode: _focusNode2,
//               ) : Center(child: CircularProgressIndicator()),
//             ),
//             SizedBox(width: 4),
//             SizedBox(
//               width: (MediaQuery.of(context).size.width / 2) - 26,
//               child: _buildProductGrid(),
//             )
//           ],
//         ),
//         desktop: Row(
//           mainAxisAlignment: MainAxisAlignment.start,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             SizedBox(
//               width: (MediaQuery.of(context).size.width / 2) - 26,
//               child: invoice != null ? ReceiptForm(
//                 invoice: invoice,
//                 currencies: currencies,
//                 myController: myController,
//                 onInvoiceChanged: _updateInvoice,
//                 clientLayerLink: _layerLink,
//                 productLayerLink: _layerLink2,
//                 clientFocusNode: _focusNode,
//                 productFocusNode: _focusNode2,
//               ) : Center(child: CircularProgressIndicator()),
//             ),
//             SizedBox(width: 4),
//             SizedBox(
//               width: (MediaQuery.of(context).size.width / 2) - 26,
//               child: _buildProductGrid(),
//             )
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildProductGrid() {
//     return ProductGrid(
//       categories: categories,
//       products: products,
//       crossAxisCount: crossAxisCount,
//       childAspectRatio: childAspectRatio,
//       onCategorySelected: (categoryId) async {
//         // Get active business ID
//         var prefs = await SharedPreferences.getInstance();
//         int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);
//
//         if (activeBusiness != null) {
//           // Get products for the selected category
//           products = await ref.read(productProductRepositoryProvider).getForCategory(categoryId);
//           // Filter for the active business
//           products = products.where((product) => product.company_id == activeBusiness).toList();
//           setState(() {});
//         }
//       },
//       onProductSelected: (product) {
//         _addProductToInvoice(product);
//       },
//     );
//   }
// }
