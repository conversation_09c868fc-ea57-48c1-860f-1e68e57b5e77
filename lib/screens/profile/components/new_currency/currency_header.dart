import 'package:invoicer/core/constants/color_constants.dart';

import 'package:invoicer/core/utils/responsive.dart';
import 'package:flutter/material.dart';
 
// import '../new/invoice_home_screen.dart';
// import '../new/invoice_screen.dart';

class CategoryHeader extends StatelessWidget {


  const CategoryHeader({
    Key? key,required this.title
  }) : super(key: key);
  final String title;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Container(
      // color:  Theme.of(context).colorScheme.surface,
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 10,
              ),
              Text(title, style: TextStyle(fontSize: 20) ),
              ElevatedButton.icon(
                style: TextButton.styleFrom(
                  backgroundColor: dangerColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: defaultPadding * 1.5,
                    vertical:
                    defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                  ),
                ),
                onPressed: () {
                  Navigator.pop(context);


                },
                icon: Icon(Icons.cancel),
                label: Text(
                  "Close",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
