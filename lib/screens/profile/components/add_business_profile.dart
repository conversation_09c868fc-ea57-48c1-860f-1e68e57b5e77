import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/providers/business/business_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:flutter/material.dart';

import '../../../core/exceptions/custom_exception.dart';
import '../../../core/repositories/drift/repository_provider_riverpod.dart';
import '../../../core/utils/UserPreference.dart';
import '../../../core/types/Memo.dart';
import '../../../core/db/drift/database.dart';
import '../../../core/models/drift/ResCompanyExtensions.dart';
import '../../dashboard/components/error_page.dart';
import '../edit/profile_home_screen.dart';

class AddBusinessProfile extends StatelessWidget {
  const AddBusinessProfile({
    Key? key,
    // required this.memos,
    // required this.callback

  }) : super(key: key);

  // final Function(String, String) callback;
  // final List<Memo> memos;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [],
        ),
        SizedBox(height: defaultPadding),
        Responsive(
          mobile: InformationCard(
            crossAxisCount: _size.width < 650 ? 1 : 1,
            childAspectRatio: _size.width < 650 ? 5 : 8,
            memos: memos,
            // callback: callback
          ),
          tablet: InformationCard(
            memos: memos,
            // callback: callback
          ),
          desktop: InformationCard(
            childAspectRatio: _size.width < 1400 ? 6 : 6,
            memos: memos,
            // callback: callback
          ),
        ),
      ],
    );
  }
}


class InformationCard extends ConsumerStatefulWidget {
  const InformationCard({
    Key? key,
    this.crossAxisCount = 2,
    this.childAspectRatio = 6,
    required this.memos,
    // required this.callback,

  }) : super(key: key);

  final List<Memo> memos;
  final int crossAxisCount;
  final double childAspectRatio;
  // final Function(String, String) callback;


  @override
  _InformationCardState createState() => _InformationCardState();
}

class _InformationCardState extends ConsumerState<InformationCard> {


  List<ResCompanyTableData> businesses = [ ];

  Future<void> _initBusinesss() async {
    // Get businesses using the resCompanyRepositoryProvider
    businesses = await ref.read(resCompanyRepositoryProvider).getAll();
    // Refresh the businessesProvider to update the UI
    var _ = ref.refresh(businessesProvider.future);
    setState(() {});
  }

  @override
  void initState() {
    _initBusinesss();
    super.initState();
  }


  @override
  Widget build(BuildContext context) {

    return ref.watch(businessesProvider)
        .when(
      data: (data) {
        businesses = data;
        return businesses.isEmpty ? Text("No businesses found") :GridView.builder(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: businesses.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: widget.crossAxisCount,
            crossAxisSpacing: defaultPadding,
            mainAxisSpacing: defaultPadding,
            childAspectRatio: widget.childAspectRatio,
          ),
          itemBuilder: (context, index) =>
              BusinessTileWidget(memo: businesses[index], ),
        );

      },
      loading: () =>
          Padding(
            padding: EdgeInsets.all(10),
            child: Center(child: CircularProgressIndicator()),
          ),
      error: (e, st) => ErrorPage(
        error: e is CustomException ? e.message : e.toString(),
      ),
    );

  }
}

class BusinessTileWidget extends StatefulWidget {
  const BusinessTileWidget({
    Key? key,
    required this.memo,
    // required this.callback
  }) : super(key: key);
  final ResCompanyTableData memo;
  // final Function(String, String) callback;

  @override
  _BusinessTileWidgetState createState() => _BusinessTileWidgetState();
}

class _BusinessTileWidgetState extends State<BusinessTileWidget> {
  bool _visible = false;

  TextEditingController _controller = TextEditingController();

  void _toggle() {
    setState(() {
      _visible = !_visible;
    });
  }

  int charLength = 0;

  bool status = false;
  bool _closeIcon = true;

  SharedPreferences? prefs;
  int? activeBusiness;

  init() async {
    prefs = await SharedPreferences.getInstance();
    activeBusiness = await prefs!.getInt(UserPreference.activeBusiness);
    setState(() {  activeBusiness; });
  }

  @override
  void initState() {
    init();

    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {

    var variaId = widget.memo.id;

    print('This is the active business: $activeBusiness');
    print('ResCompany id is: $variaId');


    return           GestureDetector(
        child:Container(
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: activeBusiness==widget.memo.id?Theme.of(context).colorScheme.surface
                :Colors.black26,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [


              Container(
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(defaultPadding * 0),
                      height: 40,
                      width: 40,
                      decoration: BoxDecoration(
                        color: Colors.lightBlue.withAlpha(26), // 0.1 * 255 = 26
                        borderRadius: const BorderRadius.all(Radius.circular(10)),
                      ),
                      child: Icon(
                        Icons.house,
                        color: mainColor,
                        size: 18,
                      ),
                    ),
                    SizedBox(width: 6,),
                    Text(
                      "${widget.memo.name}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(
                      width: 6,
                    ),
                    Visibility(
                      visible: !_visible,
                      child: activeBusiness==widget.memo.id?Text("Active",   style: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: Colors.blue),):SizedBox(),
                    )
                  ],
                ),
              ),


            ],
          ),
        ),onTap: () {
      // _toggle();
      // if(widget.memo.set=="set"){
      //   widget.callback(widget.memo.id.toString()!, "not");
      //   setState(() {
      //     widget.memo.set="not";
      //   });
      // }
      // else{
      //   widget.callback(widget.memo.id.toString()!, "set");
      //   setState(() {
      //     widget.memo.set="set";
      //   });
      //
      // }

      //
      // Navigator.of(context).push(new MaterialPageRoute<Null>(
      //     builder: (BuildContext context) {
      //       return new NewClientHome(title: "Edit Client", code: "edit", clientId: widget.memo.id );
      //     },
      //     fullscreenDialog: true));


      // print(widget.memo.toJson());
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => ProfileHome(title: 'New Invoice', code: 'invoice', profileId: widget.memo.id)),
      );

    }
    );
  }

}
