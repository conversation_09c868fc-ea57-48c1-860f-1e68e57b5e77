import 'package:flutter/scheduler.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/dashboard/dashboard_screen.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/utils/UserPreference.dart';
import '../profile/components/add_business_profile_home.dart';
import 'components/side_menu.dart';

class HomeScreen extends StatefulWidget {
  final String source;

  const HomeScreen({Key? key, required this.source}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool firstBuild = true;

  @override
  Widget build(BuildContext context) {
    // print("I was called by" + widget.source);

    Future<bool> checkBusiness() async {
      try {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        var id = await prefs.getInt(UserPreference.activeBusiness);

        if (id != null) {
          return true;
        } else {
          return false;
        }
      } catch (e,st) {
        print('$e \n$st');
        return false;
      }
    }


    return Scaffold(
      //key: context.read<MenuController>().scaffoldKey,
      drawer: SideMenu(),
      body: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Use SizedBox.shrink() instead of SizedBox() to ensure it doesn't take up space
            FutureBuilder(
              future: checkBusiness(),
              builder: (context, snapshot) {
                if(snapshot.data == false && firstBuild == true){
                  firstBuild = false;
                  SchedulerBinding.instance.addPostFrameCallback((_) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => AddBusinessProfileHome()),
                    );
                  });
                }
                // Return a widget with zero size to avoid layout issues
                return SizedBox.shrink();
              },
            ),
            // We want this side menu only for large screen
            if (Responsive.isDesktop(context))
              Expanded(
                // default flex = 1
                // and it takes 1/6 part of the screen
                child: SideMenu(),
              ),
            Expanded(
              // It takes 5/6 part of the screen
              flex: 5,
              child: DashboardScreen(),
            ),
          ],
        ),
      ),
    );
  }
}
