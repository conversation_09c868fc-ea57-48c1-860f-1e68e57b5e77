//
// import 'package:flutter/material.dart';
// import 'package:fl_chart/fl_chart.dart';
//
//
// class MarksBarChartWidget extends StatelessWidget {
//   final List<BarChartGroupData> studentMarks = [
//     BarChartGroupData(x: 1, barsSpace: 2, barRods: [
//       BarChartRodData(y: 56),
//     ]),
//     BarChartGroupData(x: 2, barsSpace: 2, barRods: [
//       BarChartRodData(y: 80),
//     ]),
//     BarChartGroupData(x: 3, barsSpace: 2, barRods: [
//       BarChartRodData(y: 56),
//     ]),
//     BarChartGroupData(x: 4, barsSpace: 2, barRods: [
//       BarChartRodData(y: 60),
//     ]),
//     BarChartGroupData(x: 9, barsSpace: 2, barRods: [
//       BarChartRodData(y: 30),
//     ]),
//   ];
//
//
//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       height: 500,
//       child: Column(
//         children: [
//           SizedBox(height: 10,),
//           Container(
//             // padding: const EdgeInsets.symmetric(20),
//             width: double.infinity,
//             height: 400,
//             child: BarChart(
//               BarChartData(
//                 minY: 0,
//                 maxY: 100,
//                 gridData: FlGridData(show: true, drawVerticalLine: true, horizontalInterval: 10),
//                 borderData: FlBorderData(show: true, border: Border.all(color: Colors.grey)),
//                 barGroups: studentMarks,
//                 titlesData: FlTitlesData(
//                   leftTitles: SideTitles(showTitles: true, interval: 10),
//                   bottomTitles: SideTitles(showTitles: true),
//                 ),
//               ),
//             ),
//           ),
//           Center(child: Text("Product Sales March"),),
//           SizedBox(height: 10,),
//           Text("< Swipe left"),
//           Expanded(child: SizedBox()),
//         ],
//       ),
//     );
//   }
// }
