import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:invoicer/screens/profile/profiles_home_screen.dart';

import '../../../core/repositories/drift/repository_provider_riverpod.dart';
import '../../../core/utils/UserPreference.dart';
import '../../shared_components/search_results/clients_home_screen.dart';

class Header extends StatelessWidget {
  Header({
    Key? key
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final canPop = Navigator.of(context).canPop();
    return Row(
      children: [
        if (canPop)
          IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).maybePop();
            },
          ),
        // else if (!Responsive.isDesktop(context))
          IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        if (!Responsive.isMobile(context))
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HelloWidget(),
              SizedBox(
                height: 8,
              ),
              Text(
                "Welcome to your dashboard",
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ],
          ),
        if (!Responsive.isMobile(context))
          Spacer(flex: Responsive.isDesktop(context) ? 2 : 1),
        Expanded(child: SizedBox()),
        ProfileCard()
      ],
    );
  }
}

class ProfileCard extends StatelessWidget {
  const ProfileCard({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap:(){
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => ProfileHomeScreen()),
      );
    } ,
    child:Container(
      margin: EdgeInsets.only(left: defaultPadding),
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding,
        vertical: defaultPadding / 2,
      ),
      decoration: BoxDecoration(
        // color:  Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color:  Theme.of(context).colorScheme.outline),
      ),
      child:  Row(
          children: [
            Icon(Icons.person),
            if (!Responsive.isMobile(context))
              Padding(
                padding:
                const EdgeInsets.symmetric(horizontal: defaultPadding / 2),
                child: Text("User"),
              ),
            // Icon(Icons.keyboard_arrow_down),
          ],
        ),
    ),
    );
  }
}
String query = '';
// class SearchField extends ConsumerWidget {
//   SearchField({
//     Key? key,
//   }) : super(key: key);
//
//
//
//   @override
//   Widget build(BuildContext context,WidgetRef ref) {
//     return TextField(
//       decoration: InputDecoration(
//         hintText: "Search",
//         // fillcolor:  Theme.of(context).colorScheme.surface,
//         filled: true,
//         border: OutlineInputBorder(
//           borderSide: BorderSide.none,
//           borderRadius: const BorderRadius.all(Radius.circular(10)),
//         ),
//         suffixIcon: InkWell(
//           onTap: () async {
//             print(query);
//             List<ResPartnerTableData> clients = [];
//
//             if (query != '') {
//               try {
//                 // Get active business ID
//                 var prefs = await SharedPreferences.getInstance();
//                 int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);
//
//                 if (activeBusiness != null) {
//                   // Search clients
//                   var searchResults = await ref.read(resPartnerRepositoryProvider).search(query);
//
//                   // Filter to only include clients for the active business and customers
//                   clients = searchResults.where((client) =>
//                     client.company_id == activeBusiness &&
//                     client.customer_rank != null &&
//                     client.customer_rank! > 0
//                   ).toList();
//                 }
//               } catch (e) {
//                 print("Error searching clients: $e");
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   SnackBar(
//                     content: Text("Error searching clients: ${e.toString()}"),
//                     backgroundColor: Colors.red,
//                   )
//                 );
//               }
//             }
//
//             // TODO: Update SearchResultsHomeScreen to accept ResPartnerTableData
//             Navigator.push(
//               context,
//               MaterialPageRoute(builder: (context) => SearchResultsHomeScreen(clients: [])),
//             );
//           },
//           child: Container(
//             padding: EdgeInsets.all(defaultPadding * 0.75),
//             margin: EdgeInsets.symmetric(horizontal: defaultPadding / 2),
//             decoration: BoxDecoration(
//               color: mainColor,
//               borderRadius: const BorderRadius.all(Radius.circular(10)),
//             ),
//             child: SvgPicture.asset(
//               "assets/icons/Search.svg",
//             ),
//           ),
//         ),
//       ),
//       onSubmitted: (e) async {
//         print(query);
//         List<ResPartnerTableData> clients = [];
//
//         if (e != '') {
//           try {
//             // Get active business ID
//             var prefs = await SharedPreferences.getInstance();
//             int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);
//
//             if (activeBusiness != null) {
//               // Search clients
//               var searchResults = await ref.read(resPartnerRepositoryProvider).search(e);
//
//               // Filter to only include clients for the active business and customers
//               clients = searchResults.where((client) =>
//                 client.company_id == activeBusiness &&
//                 client.customer_rank != null &&
//                 client.customer_rank! > 0
//               ).toList();
//             }
//           } catch (error) {
//             print("Error searching clients: $error");
//             ScaffoldMessenger.of(context).showSnackBar(
//               SnackBar(
//                 content: Text("Error searching clients: ${error.toString()}"),
//                 backgroundColor: Colors.red,
//               )
//             );
//           }
//         }
//
//         // TODO: Update SearchResultsHomeScreen to accept ResPartnerTableData
//         Navigator.push(
//           context,
//           MaterialPageRoute(builder: (context) => SearchResultsHomeScreen(clients: [])),
//         );
//       },
//       onChanged: (value){
//         query = value;
//         print(query);
//       },
//     );
//   }
// }



class HelloWidget extends StatefulWidget {
  @override
  _HelloWidgetState createState() => _HelloWidgetState();
}

class _HelloWidgetState extends State<HelloWidget> {



  String user = "" ;

  init() async {
    var prefs = await SharedPreferences.getInstance();
    user = (await prefs.getString(UserPreference.firstName))?? "";
    setState(() {

    });
  }

  @override
  void initState() {
    init();

    super.initState();
  }


  @override
  Widget build(BuildContext context) {
    return Text(
      "Hello, "+ user +" 👋",
      style: Theme.of(context).textTheme.titleLarge,
    );
  }
}

