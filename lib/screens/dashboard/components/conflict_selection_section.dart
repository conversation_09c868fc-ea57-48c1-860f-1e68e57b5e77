import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/types/conflict.dart';

class ConflictSelectionSection extends StatelessWidget {
  ConflictSelectionSection({
    Key? key,
    required this.conflict

  }) : super(key: key);

  final Conflict conflict;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [],
        ),
        SizedBox(height: defaultPadding),
        Responsive(
          mobile: InformationCard(
            crossAxisCount: _size.width < 650 ? 1 : 1,
            childAspectRatio: _size.width < 650 ? 3/2 : 1,
            conflict: conflict
          ),
          tablet: InformationCard(
            conflict: conflict
          ),
          desktop: InformationCard(
            childAspectRatio: _size.width < 1400 ? 2 : 3,
            conflict: conflict
          ),
        ),
        SizedBox(
          height: 16,
        ),
        ElevatedButton.icon(
            icon: Icon(
              Icons.close,
              size: 14,
            ),
            style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey),
            onPressed: () {
              context.pop();
            },
            label: Text("Accept Incoming")),
        SizedBox(
          height: 20,
        ),
        ElevatedButton.icon(
            icon: Icon(
              Icons.delete,
              size: 14,
            ),
            style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red),
            onPressed: () {},
            label: Text("Use Local"))
      ],
    );
  }
}



class InformationCard extends StatefulWidget {
  const InformationCard({
    Key? key,
    this.crossAxisCount = 2,
    this.childAspectRatio = 6,
    required this.conflict,

  }) : super(key: key);

  final Conflict conflict;
  final int crossAxisCount;
  final double childAspectRatio;


  @override
  _InformationCardState createState() => _InformationCardState();
}

class _InformationCardState extends State<InformationCard> {


  List<ResPartnerTableData> clients = [
    ResPartnerTableData(
      id:0,
      name: null,
      street: null,
      street2: null,
      city: null,
      zip: null,
      country_id: null,
      state_id: null,
      phone: null,
      mobile: null,
      email: null,
      website: null,
      function: null,
      title: null,
      vat: null,
      ref: null,
      lang: null,
      comment: null,
      active: true,
      customer_rank: null,
      supplier_rank: null,
      user_id: null,
      company_id: null,
      parent_id: null,
      is_company: null,
      type: null,
      industry_id: null,
      color: null,
      image_1920: null,
      image_1024: null,
      image_512: null,
      image_256: null,
      image_128: null,
      barcode: null,
      currency: null,
      status: null,
      universal_id: null,
      is_synced: false,
      origin_id: null,
      version: 1,
      is_confirmed: false,
      is_deleted: false,
    )
  ];

  Future<void> _initClients() async {
    // clients = await getClients();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();

    _initClients();
  }


  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: clients.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        crossAxisSpacing: defaultPadding,
        mainAxisSpacing: defaultPadding,
        childAspectRatio: widget.childAspectRatio,
      ),
      itemBuilder: (context, index) =>
          MiniInformationWidget(memo: clients[index]),
    );
  }
}

class MiniInformationWidget extends StatefulWidget {
  const MiniInformationWidget({
    Key? key,
    required this.memo
  }) : super(key: key);
  final ResPartnerTableData memo;

  @override
  _MiniInformationWidgetState createState() => _MiniInformationWidgetState();
}

class _MiniInformationWidgetState extends State<MiniInformationWidget> {
  int selectedClient = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [


            GestureDetector(
                child: Container(
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(width: 6,),
                      Text(
                        "Due Date",
                        style:
                        const TextStyle(color: Colors.redAccent, fontWeight: FontWeight.bold, fontSize: 14),
                        // "${widget.memo.name?? 'Name'}",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(
                        width: 6,
                      ),
                    ],
                  ),
                ),
            ),

          ],
        ),
        SizedBox(height: 5,),
        Container(
          padding: EdgeInsets.all(defaultPadding/3),
          decoration: BoxDecoration(
            color: selectedClient==widget.memo.id?darkgreenColor:Colors.black38,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child:
          Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [


                  GestureDetector(
                      child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person,
                              color: Colors.lightBlue,
                              size: 12,
                            ),
                            SizedBox(width: 6,),
                            Text(
                              "Tinashe Makarudze",
                              style:
                              const TextStyle(color: Colors.white, fontStyle: FontStyle.italic, fontSize: 12),
                              // "${widget.memo.name?? 'Name'}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(
                              width: 6,
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        // _toggle();
                        if(selectedClient==widget.memo.id){
                          selectedClient=0;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });
                        }else{
                          selectedClient = widget.memo.id;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });

                        }
                      }
                  ),

                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [


                  GestureDetector(
                      child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(width: 6,),
                            Text(
                              "11/02/23 ",
                              style:
                              const TextStyle(color: Colors.white, fontStyle: FontStyle.italic, fontSize: 12),
                              // "${widget.memo.name?? 'Name'}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Icon(
                              Icons.arrow_forward,
                              color: Colors.redAccent,
                              size: 12,
                            ),
                            Text(
                              "12/03/23 ",
                              style:
                              const TextStyle(color: Colors.greenAccent, fontStyle: FontStyle.italic, fontSize: 12),
                              // "${widget.memo.name?? 'Name'}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        // _toggle();
                        if(selectedClient==widget.memo.id){
                          selectedClient=0;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });
                        }else{
                          selectedClient = widget.memo.id;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });

                        }
                      }
                  ),

                ],
              ),
            ],
          ),



        ),
        SizedBox(height: 5,),
        Container(
          padding: EdgeInsets.all(defaultPadding/3),
          decoration: BoxDecoration(
            color: selectedClient==widget.memo.id?darkgreenColor:Colors.black38,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child:
          Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [


                  GestureDetector(
                      child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person,
                              color: Colors.lightBlue,
                              size: 12,
                            ),
                            SizedBox(width: 6,),
                            Text(
                              "Tinashe Makarudze",
                              style:
                              const TextStyle(color: Colors.white, fontStyle: FontStyle.italic, fontSize: 12),
                              // "${widget.memo.name?? 'Name'}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(
                              width: 6,
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        // _toggle();
                        if(selectedClient==widget.memo.id){
                          selectedClient=0;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });
                        }else{
                          selectedClient = widget.memo.id;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });

                        }
                      }
                  ),

                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [


                  GestureDetector(
                      child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(width: 6,),
                            Text(
                              "11/02/23 ",
                              style:
                              const TextStyle(color: Colors.white, fontStyle: FontStyle.italic, fontSize: 12),
                              // "${widget.memo.name?? 'Name'}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Icon(
                              Icons.arrow_forward,
                              color: Colors.redAccent,
                              size: 12,
                            ),
                            Text(
                              "12/03/23 ",
                              style:
                              const TextStyle(color: Colors.greenAccent, fontStyle: FontStyle.italic, fontSize: 12),
                              // "${widget.memo.name?? 'Name'}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        // _toggle();
                        if(selectedClient==widget.memo.id){
                          selectedClient=0;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });
                        }else{
                          selectedClient = widget.memo.id;
                          print(selectedClient);
                          context.pop();
                          setState(() {
                          });

                        }
                      }
                  ),

                ],
              ),
              SizedBox(height: 5,),
            ],

          ),



        ),
      ],
    );


  }

}
