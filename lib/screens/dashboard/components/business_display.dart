import 'package:flutter/material.dart';

import '../../../core/constants/color_constants.dart';

Widget BusinessName(String? business, context){
  return Container(
    // margin: EdgeInsets.only(left: 0),
    padding: EdgeInsets.all(4),
    decoration: BoxDecoration(
      // color:  Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
        border: Border.all(color: Theme.of(context).colorScheme.outline)
    ),
    child: SizedBox(
      child: Text(business ?? "Select Business" ),
    ),

  );
}