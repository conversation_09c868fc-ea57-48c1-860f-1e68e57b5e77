//
// import 'package:flutter/material.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:invoicer/core/constants/color_constants.dart';
//
//
// class MarksChartWidget extends StatelessWidget {
//   final List<FlSpot> studentMarks = [
//     FlSpot(0, 80), // Test 1
//     FlSpot(1, 75), // Test 2
//     FlSpot(2, 90), // Test 3
//     FlSpot(3, 85), // Test 4
//   ];
//
//   final List<FlSpot> classAverage = [
//     FlSpot(0, 85), // Test 1
//     FlSpot(1, 82), // Test 2
//     FlSpot(2, 88), // Test 3
//     FlSpot(3, 15), // Test 4
//   ];
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Container(
//           // padding: const EdgeInsets.symmetric(20),
//           width: double.infinity,
//           height: 400,
//           child: LineChart(
//             LineChartData(
//               minY: 0,
//               maxY: 100,
//               gridData: FlGridData(show: true, drawVerticalLine: true, horizontalInterval: 10),
//               borderData: FlBorderData(show: true, border: Border.all(color: Colors.grey)),
//               lineBarsData: [
//                 // Student marks line
//                 LineChartBarData(
//                   spots: studentMarks,
//                   isCurved: false,
//                   colors: [Colors.blue],
//                   barWidth: 1,
//                 ),
//                 // Class average line
//                 LineChartBarData(
//                   spots: classAverage,
//                   isCurved: false,
//                   colors: [Colors.grey],
//                   barWidth: 1,
//                 ),
//               ],
//               titlesData: FlTitlesData(
//                 leftTitles: SideTitles(showTitles: true, interval: 10),
//                 bottomTitles: SideTitles(showTitles: true),
//               ),
//             ),
//           ),
//         ),
//         Center(child: Text("Test Marks"),),
//         SizedBox(height: 20,),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//           children: [
//             Row(
//               children: [
//                 Icon(Icons.circle, color: primaryColor,),
//                 Text("Camila")
//             ],
//             ),
//             Row(
//               children: [
//                 Icon(Icons.circle, color: Colors.grey,),
//                 Text("Class Average")
//             ],
//             ),
//           ],
//         ),
//         SizedBox(height: 20,),
//       ],
//     );
//   }
// }
