import 'package:invoicer/core/constants/color_constants.dart';

import 'package:flutter/material.dart';


import '../../../core/utils/responsive.dart';

class PaginationWidget extends StatefulWidget {
  @override
  _PaginationWidgetState createState() => _PaginationWidgetState();

  PaginationWidget({required this.res, required this.req, required this.getItems});

  var  res;
  var req;
  dynamic Function(dynamic) getItems;
}

class _PaginationWidgetState extends State<PaginationWidget> {


  @override
  Widget build(BuildContext context) {

      return        SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: ConstrainedBox(
          constraints: new BoxConstraints(
            minWidth: Responsive.isDesktop(context)?((MediaQuery.of(context).size.width/6)*5)-90:(MediaQuery.of(context).size.width-90),
          ),
          child: new DecoratedBox(
            decoration: new BoxDecoration(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [

                SizedBox(),
                <PERSON>zed<PERSON><PERSON>(width: 10,),
                Row(
                  children: [
                    Text("Page Size: ", style: TextStyle(fontSize: tableFooterFontSize),),
                    DropdownButton<int>(
                      value: widget.req.page_size,
                      onChanged: (int? newValue) {
                        widget.req = widget.req.copyWith(page_size:newValue! );
                        widget.getItems(widget.req) ;
                      },
                      items: [10, 20, 100, 250, 500, 1000].map<DropdownMenuItem<int>>((int number) {
                        return DropdownMenuItem<int>(
                          value: number,
                          child: Text(number.toString(),style: TextStyle(fontSize: tableFooterFontSize)),
                        );
                      }).toList(),
                    ),
                    SizedBox(width: 10,),
                    Text("Showing ${widget.res.offset+1} to ${(widget.res.itemCount+widget.res.offset).toString()} of ${widget.res.totalItems}", style: TextStyle(fontSize: tableFooterFontSize),),
                    SizedBox(width: 10,),
                    GestureDetector(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                          border: Border.all(color: Theme.of(context).colorScheme.outline),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: defaultPadding/2),
                        child: Row(
                          children: [
                            Icon(Icons.navigate_before,  size: 12,),
                            Text("Prev", style: TextStyle(fontSize: 12 ),)
                          ],
                        ),
                      ),
                      onTap: (){
                        if(widget.req.page_number>0)widget.req = widget.req.copyWith(page_number: widget.req.page_number-1);
                        widget.getItems(widget.req) ;
                      },
                    ),


                    SizedBox(width: 10,),

                    GestureDetector(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                          border: Border.all(color: Theme.of(context).colorScheme.outline),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: defaultPadding/2),
                        child:   Row(
                          children: [
                            Text("Next", style: TextStyle(fontSize: 12 ),),
                            Icon(Icons.navigate_next,  size: 12,),
                          ],
                        ),
                      ),
                      onTap: (){
                        if(widget.res.offset+widget.res.itemCount<widget.res.totalItems)widget.req = widget.req.copyWith(page_number: widget.req.page_number+1);
                        widget.getItems(widget.req) ;
                      },
                    )


                  ],
                )
              ],
            ),
          ),
        ),
      );


  }
}
