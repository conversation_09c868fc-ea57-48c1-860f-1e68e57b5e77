
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:colorize_text_avatar/colorize_text_avatar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/db/drift/database.dart';
import '../../../core/utils/responsive.dart';
import '../categorys_home_screen.dart';
import '../edit/category_home_screen.dart';



class CategoryList extends ConsumerStatefulWidget {
  @override
  _CategoryListState createState() => _CategoryListState();
}

class _CategoryListState extends ConsumerState<CategoryList> {

  List<ProductCategoryTableData> categorys = [];

  Future<void> _initcategorys() async {
    categorys = await ref.read(productCategoryRepositoryProvider).getAll();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();

    _initcategorys();
  }

  @override
  Widget build(BuildContext context) {


      categorys.removeWhere((element) => element.name == null);
      final scrollController = ScrollController();

      DataRow recentUserDataRow(ProductCategoryTableData category, BuildContext context) {

        deleteDialog(){
          return showDialog(
              context: context,
              builder: (_) {
                return AlertDialog(
                    title: Center(
                      child: Text("Confirm Deletion"),
                    ),
                    content: Container(
                      // color:  Theme.of(context).colorScheme.surface,
                      height: 70,
                      child: Column(
                        children: [
                          Text(
                              "Are you sure want to delete '${category.name}'?"),
                          SizedBox(
                            height: 16,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton.icon(
                                  icon: Icon(
                                    Icons.close,
                                    size: 14,
                                  ),
                                  style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.grey),
                                  onPressed: () {
                                    context.pop();
                                  },
                                  label: Text("Cancel")),
                              SizedBox(
                                width: 20,
                              ),
                              ElevatedButton.icon(
                                  icon: Icon(
                                    Icons.delete,
                                    size: 14,
                                  ),
                                  style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red),
                                  onPressed: () async {
                                    try{
                                      await ref.read(productCategoryRepositoryProvider).delete(category.id);
                                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                        content: Text("Category deleted successfully"),
                                      ));
                                    }catch(e,st) {
                                      print('$e \n$st');
                                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                        content: Text("An error occurred while deleting"),
                                      ));
                                    }
                                    context.pop();
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(builder: (context) => CategorysHomeScreen()),
                                    );
                                  },
                                  label: Text("Delete"))
                            ],
                          )
                        ],
                      ),
                    ));
              });
        }

        return DataRow(
          cells: [

            DataCell(
                Row(
                  children: [
                    TextAvatar(
                      size: 35,
                      backgroundColor: Colors.white,
                      textColor: Colors.white,
                      fontSize: 14,
                      upperCase: true,
                      numberLetters: 1,
                      shape: Shape.Rectangle,
                      text: category.name != null  ?  RegExp(r'^[A-Za-z_.]+$').hasMatch(category.name![0]) ? category.name!: 'a' : "a",
                    ),
                    SizedBox(width: 4,),
                    Container(
                        padding: EdgeInsets.all(5),
                        width: (MediaQuery.of(context).size.width/3)-20,
                        child: Text(category.name != null ? category.name! : "",
                          // overflow: TextOverflow.fade,
                        )
                    )
                  ],)

            ),
            DataCell(

                Text(category.complete_name != null ? category.complete_name! : "")),
            // DataCell(Text(userInfo.city != null ? userInfo.city! : "")),
            DataCell(
              Row(
                children: [
                  Responsive.isDesktop(context) ? ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: mainColor,
                    ),
                    icon: Icon(
                      Icons.edit,
                      size: 14,
                    ),
                    onPressed: () {
                      Navigator.of(context).push(new MaterialPageRoute<Null>(
                          builder: (BuildContext context) {
                            return new CategoryHome(title: "Edit Category", code: "edit", categoryId: category.id );
                          },
                          fullscreenDialog: true));
                    },
                    // Edit
                    label: Text("Edit"),
                  ) :

                  GestureDetector(
                    onTap:(){
                      Navigator.of(context).push(new MaterialPageRoute<Null>(
                          builder: (BuildContext context) {
                            return new CategoryHome(title: "Edit Category", code: "edit", categoryId: category.id );
                          },
                          fullscreenDialog: true));
                    },
                    child:Icon(Icons.edit, color:mainColor),
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  // Responsive.isDesktop(context) ? ElevatedButton.icon(
                  //   style: ElevatedButton.styleFrom(
                  //     primary: Colors.green.withOpacity(0.5),
                  //   ),
                  //   icon: Icon(
                  //     Icons.visibility,
                  //     size: 14,
                  //   ),
                  //   onPressed: () {},
                  //   //View
                  //   label: Text("View"),
                  // ) : Icon(Icons.remove_red_eye, color: Colors.green.withOpacity(0.5)),
                  SizedBox(
                    width: 6,
                  ),
                  Responsive.isDesktop(context)
                      ? ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.withAlpha(128),
                    ),
                    icon: Icon(Icons.delete),
                    onPressed: () {
                      deleteDialog();
                    },
                    // Delete
                    label: Text("Delete"),
                  )
                      : GestureDetector(
                      onTap: (){
                        deleteDialog();
                      } ,
                      child: Icon( Icons.delete, color: Colors.red.withAlpha(128),)
                  ),
                ],
              ),
            ),
          ],
        );
      }

      return Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Category List",
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Scrollbar(
              controller: scrollController,
              thumbVisibility: true, //always show scrollbar
              thickness: 10, //width of scrollbar
              radius: Radius.circular(20), //corner radius of scrollbar
              scrollbarOrientation: ScrollbarOrientation.bottom, //which side to show scrollbar
              child:SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  controller: scrollController,
                  child: ConstrainedBox(
                    constraints: new BoxConstraints(
                      minWidth: Responsive.isDesktop(context)?((MediaQuery.of(context).size.width/6)*5)-50:(MediaQuery.of(context).size.width-60),
                    ),
                    child: new DecoratedBox(
                      decoration: new BoxDecoration(),
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 10),
                        child: DataTable(

                          horizontalMargin: 0,
                          columnSpacing: defaultPadding,
                          columns: [
                            DataColumn(
                              label: Text("Name"),
                            ),
                            DataColumn(
                              label: Text("Description"),
                            ),
                            // DataColumn(
                            //   label: Text("Stage"),
                            // ),
                            DataColumn(
                              label: Text("Operation"),
                            ),
                          ],
                          rows: List.generate(
                            categorys.length,
                                (index) => recentUserDataRow(categorys[index], context),
                          ),
                        ),
                      ),
                    ),
                  )
              )
          )

        ],
      ),
    );
  }


}

