import 'dart:math';
import 'dart:typed_data';

import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:intl/intl.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/screens/bluetoothprinter/printing_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/UserPreference.dart';
import '../../main.dart';
import 'package:image/image.dart' as im;


class BluePrint {
  BluePrint({this.chunkLen = 240});  // Reduced from 512 to 240 (below the 245 max)

  final int chunkLen;
  final _data = List<int>.empty(growable: true);

  void add(List<int> data) {
    _data.addAll(data);
  }

  List<List<int>> getChunks() {
    final chunks = List<List<int>>.empty(growable: true);
    for (var i = 0; i < _data.length; i += chunkLen) {
      chunks.add(_data.sublist(i, min(i + chunkLen, _data.length)));
    }
    return chunks;
  }

  Future<void> printData(BluetoothDevice device) async {
    try {
      print("Preparing data for printing...");
      final data = getChunks();
      print("Getting device characteristics...");
      final characs = await _getCharacteristics(device);

      if (characs.isEmpty) {
        print("No suitable characteristics found for printing");
        return;
      }

      print("Found ${characs.length} characteristics, attempting to print...");
      bool printed = false;
      for (var i = 0; i < characs.length; i++) {
        print("Trying characteristic ${i+1}/${characs.length}");
        if (await _tryPrint(characs[i], data)) {
          print("Successfully printed with characteristic ${i+1}");
          printed = true;
          break;
        }
      }

      if (!printed) {
        print("Failed to print with any characteristic");
      }
    } catch (e,st) {
      print('$e \n$st');
      print("Error during printing: $e");
      rethrow; // Rethrow to allow the calling code to handle the error
    }
  }

  Future<bool> _tryPrint(BluetoothCharacteristic charac, List<List<int>> data) async {
    try {
      // Check if the characteristic supports write
      if (!charac.properties.write && !charac.properties.writeWithoutResponse) {
        print("Characteristic doesn't support writing");
        return false;
      }

      // Set a safe maximum chunk size
      // Most Bluetooth printers support 20 bytes as a minimum
      // The error message indicated a max of 245 bytes for your device
      int maxChunkSize = 200; // Conservative value below the 245 max
      print("Using maximum chunk size of $maxChunkSize bytes");

      // Ensure we don't exceed the maximum chunk size
      maxChunkSize = maxChunkSize > 240 ? 240 : maxChunkSize;

      for (var i = 0; i < data.length; i++) {
        try {
          final chunk = data[i];
          print("Processing chunk ${i+1}/${data.length} (${chunk.length} bytes)");

          // If the chunk is too large, split it into smaller chunks
          if (chunk.length > maxChunkSize) {
            print("Chunk too large, splitting into smaller chunks");
            for (var j = 0; j < chunk.length; j += maxChunkSize) {
              final end = (j + maxChunkSize < chunk.length) ? j + maxChunkSize : chunk.length;
              final subChunk = chunk.sublist(j, end);
              print("Writing sub-chunk ${j ~/ maxChunkSize + 1}/${(chunk.length / maxChunkSize).ceil()} (${subChunk.length} bytes)");

              await charac.write(subChunk, withoutResponse: charac.properties.writeWithoutResponse);
              // Add a small delay between sub-chunks
              await Future.delayed(Duration(milliseconds: 50));
            }
          } else {
            // Chunk is small enough, write it directly
            print("Writing chunk ${i+1}/${data.length} (${chunk.length} bytes)");
            await charac.write(chunk, withoutResponse: charac.properties.writeWithoutResponse);
          }

          // Add a delay between chunks to avoid overwhelming the device
          if (i < data.length - 1) {
            await Future.delayed(Duration(milliseconds: 100));
          }
        } catch (e,st) {
          print('$e \n$st');
          print("Error writing chunk ${i+1}: $e");
          // Try to continue with the next chunk instead of failing completely
          continue;
        }
      }
      return true;
    } catch (e,st) {
      print('$e \n$st');
      print("Error in _tryPrint: $e");
      return false;
    }
  }

  Future<List<BluetoothCharacteristic>> _getCharacteristics(BluetoothDevice device) async {
    try {
      print("Discovering services...");
      final services = await device.discoverServices();
      print("Found ${services.length} services");

      final res = List<BluetoothCharacteristic>.empty(growable: true);
      for (var i = 0; i < services.length; i++) {
        final service = services[i];
        print("Service ${i+1}: ${service.uuid}");

        // Add all characteristics that support write
        final writeCharacteristics = service.characteristics
            .where((c) => c.properties.write || c.properties.writeWithoutResponse)
            .toList();

        print("Service ${i+1} has ${writeCharacteristics.length} writable characteristics");
        res.addAll(writeCharacteristics);
      }

      return res;
    } catch (e,st) {
      print('$e \n$st');
      print("Error getting characteristics: $e");
      return [];
    }
  }

  Future<void> printInvoiceWithDevice(
    AccountMoveTableData invoice,
    ResCompanyTableData? company,
    ResPartnerTableData? partner,
    List<AccountMoveLineTableData> invoiceLines,
    List<AccountPaymentTableData> payments,
    Uint8List? logoBytes,
    BuildContext context
  ) async {
    //  await device.connect();
    final gen = Generator(PaperSize.mm58, await CapabilityProfile.load());
    BluePrint printer = BluePrint();

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String firstName = await prefs.getString(UserPreference.firstName)??'';
    String lastname = await prefs.getString(UserPreference.lastName)??'';


    // final ByteData data = await rootBundle.load('assets/images/guide3.png');
    // final Uint8List bytes = data.buffer.asUint8List();
    if(logoBytes!=null) {
      final Uint8List bytes = logoBytes;
      final im.Image? image = im.decodeImage(bytes);
      if (image != null) printer.add(gen.image(image, align: PosAlign.center));
    }

    printer.add(gen.text(company?.name??"",  styles: const PosStyles(bold: true, underline: false)));
    if(company?.street!=null)printer.add(gen.text((company?.street??'')  ));
    if(company?.city!=null || company?.city!=null)printer.add(gen.text((company?.city??'')+', '+'') );
    if(company?.phone!=null)printer.add(gen.text((company?.phone??'')));
    if(company?.email!=null)printer.add(gen.text((company?.email??'')));
    printer.add(gen.feed(1));

    // printer.add(gen.text('Invoice:43234', styles: const PosStyles(bold: true)));

    var now = DateTime.now();
    var formatterTime = DateFormat('kk:mm');
    var formatterDate = DateFormat('dd/MM/yy');

    if(partner != null && partner.name != null && partner.name!.toLowerCase() != "anonymous")
      printer.add(gen.text("Invoiced To: " + partner.name!));

    printer.add(gen.row([
      PosColumn(
          text: 'Invoice:'+(invoice.name??invoice.id.toString()),
          width: 6,
          styles: const PosStyles( underline: false)
      ),
      PosColumn(
          text: invoice.invoice_date??'',
          width: 6,
          styles: const PosStyles( underline: false)
      ),
    ]));


    printer.add(gen.text('------------------------------',     styles: const PosStyles( underline: false)));


    printer = generateProductRow(gen, printer, 'Product', 'Price', 'Qty', 'Total', addsecod: 1  );
    printer.add(gen.text('-------------------------------'));

    // Add invoice lines to the receipt
    invoiceLines.forEach((i) {
      printer = generateProductRow(gen, printer, breakAt12(i.name??''), i.price_unit?.toStringAsFixed(2)??'', i.quantity?.toStringAsFixed(1)??'', i.price_total?.toString()??'' );
    });

    double totalTopay = (invoice.amount_total! + ((invoice.amount_total??0)*((0)/100)) -(0));

    printer.add(gen.text('-------------------------------'));
    printer = generateProductRow(gen,printer, '', 'Gross', '', totalTopay.toStringAsFixed(2), addsecod: 4 );
    printer = generateProductRow(gen,printer, '', 'Net', '', invoice.amount_total?.toStringAsFixed(2)??'', addsecod: 4 );
    // if(invoice.vatPercent!=null&&invoice.vatPercent!=0)printer = generateProductRow(gen,printer, '', 'Vat'+(invoice.vatPercent?.toString()??'')+"%", '', invoice.vatAmount?.toString()??'', addsecod: 4 );
    // if(invoice.discount!=null && invoice.discount!=0)printer = generateProductRow(gen,printer, '', 'Discount', '', invoice.discount?.toString()??'', addsecod: 4 );

    double amountPaid = 0;

    // Calculate total amount paid
    payments.forEach((e) {
      amountPaid += e.amount??0;
    });

    if(amountPaid!=0)printer = generateProductRow(gen,printer, '', 'Paid', '', amountPaid.toStringAsFixed(2),addsecod: 4 );
    if(amountPaid!=0)printer = generateProductRow(gen,printer, '', 'Change', '', (amountPaid-totalTopay).toStringAsFixed(2),addsecod: 4 );
    printer.add(gen.text('----------------------------',     styles: const PosStyles( underline: false)));

    num totalItems = 0;
    invoiceLines.forEach((i) {
      totalItems+=i.quantity??1;
    });

    printer.add(gen.text('Total Items  '+totalItems.toStringAsFixed(1),     styles: const PosStyles( underline: false)));
    printer.add(gen.feed(1));
    printer.add(gen.text('Invoice By '+firstName+' '+lastname,     styles: const PosStyles( underline: false)));
    printer.add(gen.feed(1));

    printer.add(gen.text('Thank you for your business.'));
    printer.add(gen.text('Please come back soon!'));
    printer.add(gen.text('--------------------------------'));
    // printer.add(gen.text('For all your agricultural products, Dist Feed'
    //     'Dressed birds & Processing Services'));

    printer.add(gen.text('--------------------------------'));
    printer.add(gen.text('Kanjan Pos Systems:'));
    printer.add(gen.text('invoicer.kanjan.co.zw'));
    printer.add(gen.text('Printed '+formatterDate.format(now)+" "+formatterTime.format(now)));

    printer.add(gen.feed(3));

    printer.add(gen.feed(1));



    BluetoothDevice? device = selectedBtDevice;
    if (device != null) {
      try {
        print("Checking connection to device: ${device.platformName}");

        // Check if already connected
        bool isConnected = false;
        try {
          final state = await device.connectionState.first.timeout(Duration(seconds: 2));
          isConnected = state == BluetoothConnectionState.connected;
          print("Device connection state: $state");
        } catch (e,st) {
          print('$e \n$st');
          print("Error checking connection state: $e");
        }

        if (!isConnected) {
          print("Device not connected, showing device selection dialog");
          // Show the device selection dialog
          showDialog(
            context: context,
            builder: (_) {
              return AlertDialog(
                title: Text("Connect to Printer"),
                content: PrintingWidget(printer: printer),
              );
            },
          );
          return; // Exit the method, the PrintingWidget will handle printing
        }

        // If we're here, we're connected to the device
        print("Device is connected, sending print data");

        // Show a progress indicator while printing
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 10),
                Text("Printing in progress..."),
              ],
            ),
            duration: Duration(seconds: 10),
          )
        );

        try {
          await printer.printData(device);
          print("Print data sent successfully");

          // Dismiss the progress indicator and show success message
          scaffoldMessenger.hideCurrentSnackBar();
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text("Print job sent successfully"),
              backgroundColor: Colors.green,
            )
          );
        } catch (e,st) {
          print('$e \n$st');
          // Dismiss the progress indicator and show error message
          scaffoldMessenger.hideCurrentSnackBar();
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text("Error sending print data: $e"),
              backgroundColor: Colors.red,
            )
          );
          rethrow;
        }

      } catch (e,st) {
        print('$e \n$st');
        print("Error during printing: $e");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Error printing: $e"))
        );
      }
    } else {
      print("No device selected, showing device selection dialog");
      showDialog(
        context: context,
        builder: (_) {
          return AlertDialog(
            title: Text("Select a Printer"),
            content: PrintingWidget(printer: printer),
          );
        },
      );
    }
  }


  BluePrint generateProductRow(Generator gen, BluePrint printer, String t1 , String t2, String t3 , String t4, {int? addsecod}) {
    printer.add(gen.row([
      PosColumn(
        text: t1,
        width: 5-(addsecod??0),
        styles: PosStyles( underline: false),
      ),
      PosColumn(
        text: t2,
        width: 2+(addsecod??0),
        styles: PosStyles( underline: false),
      ),
      PosColumn(
        text: t3,
        width: 2,
        styles: PosStyles( underline: false),
      ),
      PosColumn(
        text: t4,
        width: 3,
        styles: PosStyles( underline: false),
      ),
    ]));

    return printer;
  }

  String breakAt12(String input) {
      final int interval = 12;
      final String breaks = '\n';
      StringBuffer result = StringBuffer();

      for (int i = 0; i < input.length; i++) {
        result.write(input[i]);
        if ((i + 1) % interval == 0) {
          result.write(breaks);
        }
      }

      return result.toString();
  }


}
