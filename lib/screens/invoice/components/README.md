# Invoice List Implementation

This directory contains an improved implementation of the invoice list functionality using Riverpod for state management and Odoo-compatible models.

## Key Components

### State Management

- `InvoiceStateNotifier_improved.dart`: A Riverpod state notifier that manages the state of invoices, including loading, filtering, and searching.
- `InvoicesState.dart`: A freezed class that represents the different states of the invoice list (initial, loading, data, error).
- `InvoicesRequest.dart`: A model class that represents the request parameters for fetching invoices.

### UI Components

- `invoice_list_container_improved.dart`: The main container component that orchestrates the invoice list UI.
- `invoice_card.dart`: A card component that displays an invoice.
- `invoice_list_empty.dart`: A component that displays a message when no invoices are found.
- `invoice_list_error.dart`: A component that displays an error message when an error occurs.
- `invoice_list_loading.dart`: A component that displays a loading indicator.
- `invoice_list_pagination.dart`: A component that displays pagination controls.
- `invoice_search_bar_improved.dart`: A search bar component with filtering capabilities.

## Usage

To use this implementation, you need to:

1. Import the necessary components in your screen:

```dart
import 'package:invoicer/screens/invoice/components/invoice_list_container_improved.dart';
```

2. Use the `InvoiceListContainer` component in your screen:

```dart
class InvoiceScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Invoices')),
      body: InvoiceListContainer('invoice'),
    );
  }
}
```

3. The `InvoiceListContainer` takes a parameter `invoiceType` which can be one of:
   - `'invoice'`: Customer invoices
   - `'bill'`: Vendor bills
   - `'credit'`: Customer credit notes
   - `'debit'`: Vendor debit notes

## Features

- **Search**: Users can search for invoices by name, number, or customer.
- **Filtering**: Users can filter invoices by status, date range, and sort order.
- **Pagination**: The list is paginated to improve performance.
- **Pull to Refresh**: Users can pull down to refresh the list.
- **Error Handling**: Errors are displayed in a user-friendly way.
- **Empty State**: A message is displayed when no invoices are found.

## Example

See `invoice_list_screen_example.dart` for a complete example of how to use this implementation in a screen with tabs for different invoice types.

## Implementation Details

- The implementation uses Riverpod for state management.
- It supports both web and local database implementations.
- It uses Odoo-compatible models (AccountMove) instead of the legacy Invoice model.
- It includes proper error handling and loading states.
- It supports filtering, searching, and pagination.
