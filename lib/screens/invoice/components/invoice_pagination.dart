import 'package:flutter/material.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/invoice/InvoicesRequest.dart';
import 'package:invoicer/core/providers/invoice/PaginatedAccountMoveTableData.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/invoice/print/invoices_report.dart';

class InvoicePagination extends StatelessWidget {
  final PaginatedAccountMoveTableData paginatedData;
  final List<AccountMoveTableData> invoices;
  final InvoicesRequest request;
  final Function(InvoicesRequest) onRequestUpdate;
  final Function() onExport;

  const InvoicePagination({
    Key? key,
    required this.paginatedData,
    required this.invoices,
    required this.request,
    required this.onRequestUpdate,
    required this.onExport,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: Responsive.isDesktop(context)
              ? ((MediaQuery.of(context).size.width / 6) * 5) - 90
              : (MediaQuery.of(context).size.width - 90),
        ),
        child: DecoratedBox(
          decoration: BoxDecoration(),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Export button
              Row(
                children: [
                  GestureDetector(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(buttonBorderRadius)),
                        border: Border.all(color: Theme.of(context).colorScheme.outline),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: defaultPadding / 2),
                      child: Row(
                        children: [
                          Icon(Icons.import_export, size: 12),
                          Text("Export Current", style: TextStyle(fontSize: tableFooterFontSize))
                        ],
                      ),
                    ),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => InvoicesReport(invoice: invoices)),
                      );
                    },
                  ),
                ],
              ),

              // Pagination controls
              Row(
                children: [
                  // Page size dropdown
                  Text("Page Size: ", style: TextStyle(fontSize: tableFooterFontSize)),
                  DropdownButton<int>(
                    value: request.page_size,
                    onChanged: (int? newValue) {
                      if (newValue != null) {
                        final updatedRequest = request.copyWith(
                          page_size: newValue,
                        );
                        onRequestUpdate(updatedRequest);
                      }
                    },
                    items: [20, 100, 250, 500, 1000].map<DropdownMenuItem<int>>((int number) {
                      return DropdownMenuItem<int>(
                        value: number,
                        child: Text(number.toString(), style: TextStyle(fontSize: tableFooterFontSize)),
                      );
                    }).toList(),
                  ),

                  // Pagination info
                  SizedBox(width: 10),
                  Text(
                    "Showing ${paginatedData.offset + 1} to ${(paginatedData.itemCount + paginatedData.offset).toString()} of ${paginatedData.totalItems}",
                    style: TextStyle(fontSize: tableFooterFontSize),
                  ),

                  // Previous page button
                  SizedBox(width: 10),
                  GestureDetector(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(buttonBorderRadius)),
                        border: Border.all(color: Theme.of(context).colorScheme.outline),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: defaultPadding / 2),
                      child: Row(
                        children: [
                          Icon(Icons.navigate_before, size: 12),
                          Text("Prev", style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                    onTap: () {
                      if (request.page_number > 0) {
                        final updatedRequest = request.copyWith(
                          page_number: request.page_number - 1,
                        );
                        onRequestUpdate(updatedRequest);
                      }
                    },
                  ),

                  // Next page button
                  SizedBox(width: 10),
                  GestureDetector(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(buttonBorderRadius)),
                        border: Border.all(color: Theme.of(context).colorScheme.outline),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: defaultPadding / 2),
                      child: Row(
                        children: [
                          Text("Next", style: TextStyle(fontSize: 12)),
                          Icon(Icons.navigate_next, size: 12),
                        ],
                      ),
                    ),
                    onTap: () {
                      if (paginatedData.offset + paginatedData.itemCount < paginatedData.totalItems) {
                        final updatedRequest = request.copyWith(
                          page_number: request.page_number + 1,
                        );
                        onRequestUpdate(updatedRequest);
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
