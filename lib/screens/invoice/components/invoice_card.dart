import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:invoicer/core/db/drift/database.dart';

class InvoiceCard extends StatelessWidget {
  final AccountMoveTableData invoice;
  final ResPartnerTableData? partner;
  final VoidCallback onTap;

  const InvoiceCard({
    Key? key,
    required this.invoice,
    this.partner,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(
      symbol: '\$', // Use a default currency symbol
      decimalDigits: 2,
    );

    // Format date
    String formattedDate = 'No date';
    if (invoice.invoice_date != null) {
      try {
        formattedDate = DateFormat('MMM d, yyyy').format(DateTime.parse(invoice.invoice_date!));
      } catch (e) {
        formattedDate = 'Invalid date';
      }
    }

    // Get status color and text
    final (statusColor, statusText) = _getStatusInfo(invoice.state ?? '');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Invoice number and amount
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      invoice.name ?? 'No Invoice Number',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    currencyFormat.format(invoice.amount_total ?? 0),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Partner name
              Text(
                partner?.name ?? 'No Customer',
                style: theme.textTheme.bodyMedium,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Date and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    formattedDate,
                    style: theme.textTheme.bodySmall,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Color.fromRGBO(
                        statusColor.r.toInt(),
                        statusColor.g.toInt(),
                        statusColor.b.toInt(),
                        0.1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: statusColor,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      statusText,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  (Color, String) _getStatusInfo(String state) {
    switch (state.toLowerCase()) {
      case 'draft':
        return (Colors.grey, 'Draft');
      case 'posted':
        return (Colors.green, 'Posted');
      case 'paid':
        return (Colors.blue, 'Paid');
      case 'cancel':
        return (Colors.red, 'Cancelled');
      default:
        return (Colors.grey, state.isNotEmpty ? state.toUpperCase() : 'Unknown');
    }
  }
}
