import 'package:flutter/material.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
 import 'package:invoicer/core/utils/colorful_tag.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/invoice/edit/invoice_home_screen.dart';
import 'package:invoicer/screens/receipt/edit/receipt_home_screen.dart';

class InvoiceTableRow extends StatelessWidget {
  final AccountMoveTableData invoice;
  final String invoiceType;

  const InvoiceTableRow({
    Key? key,
    required this.invoice,
    required this.invoiceType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Return a placeholder widget since we're using buildDataRow directly
    return const SizedBox.shrink();
  }

  DataRow buildDataRow(BuildContext context) {
    return DataRow(
      cells: [
        DataCell(Text(invoice.name ?? invoice.id.toString())),
        DataCell(
          Container(
            padding: EdgeInsets.all(3),
            margin: EdgeInsets.all(2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            child: Text(
              invoice.state != null ? invoice.state ?? "" : "",
              style: TextStyle(
                fontSize: 10,
                color: invoice.state == 'cancel'
                    ? Colors.red
                    : invoice.state == 'posted'
                        ? Colors.green
                        : invoice.state == 'unpaid'
                            ? Colors.orange
                            : invoice.state == 'overdue'
                                ? Colors.red
                                : invoice.state == 'draft'
                                    ? Colors.blue
                                    : Colors.black,
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: EdgeInsets.all(3),
            margin: EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: getRoleColor(invoice.partner_id.toString()).withAlpha(50),
              borderRadius: BorderRadius.all(Radius.circular(buttonBorderRadius)),
            ),
            child: Text("Customer ID: ${invoice.partner_id ?? 'N/A'}"),
          ),
        ),
        DataCell(Text((invoice.currency_symbol ?? "") + invoice.amount_total!.toStringAsFixed(2))),
        DataCell(Text(invoice.invoice_date.toString().split(" ")[0])),
        DataCell(
          Row(
            children: [
              Responsive.isDesktop(context)
                  ? ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: mainColor,
                      ),
                      icon: Icon(
                        Icons.edit,
                        size: 14,
                      ),
                      onPressed: () => _navigateToEditScreen(context),
                      label: Text(invoice.move_type == "out_receipt" ? "View" : "Edit"),
                    )
                  : GestureDetector(
                      onTap: () => _navigateToEditScreen(context),
                      child: Icon(Icons.edit, color: Colors.blue),
                    ),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateToEditScreen(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute<void>(
        builder: (BuildContext context) {
          return invoice.move_type == "out_receipt"
              ? SizedBox()
          // ReceiptHomeScreen(
          //         title: "Receipt: ${invoice.name ?? invoice.id}",
          //         code: "edit",
          //         invoiceId: invoice.id,
          //       )
              : InvoiceHome(
                  title: "Invoice: ${invoice.name ?? invoice.id}",
                  code: "edit",
                  invoiceId: invoice.id,
                );
        },
        fullscreenDialog: true,
      ),
    );
  }
}
