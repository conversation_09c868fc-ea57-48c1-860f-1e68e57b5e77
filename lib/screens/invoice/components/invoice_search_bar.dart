import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';

class InvoiceSearchBarImproved extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final Function(String) onSearch;
  final Function({
    String? status,
    String? clientId,
    String? startDate,
    String? endDate,
    bool? dateSort,
  }) onFilterChange;
  final VoidCallback onRefresh;

  const InvoiceSearchBarImproved({
    Key? key,
    required this.controller,
    required this.onSearch,
    required this.onFilterChange,
    required this.onRefresh,
  }) : super(key: key);

  @override
  ConsumerState<InvoiceSearchBarImproved> createState() => _InvoiceSearchBarImprovedState();
}

class _InvoiceSearchBarImprovedState extends ConsumerState<InvoiceSearchBarImproved> {
  bool _showFilters = false;
  String? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _sortDescending = true;
  String? _selectedClientId;

  final List<String> _statusOptions = [
    'All',
    'Draft',
    'Posted',
    'Paid',
    'Cancelled',
  ];

  // Add client data
  List<Map<String, dynamic>> _clients = [];

  @override
  void initState() {
    super.initState();
    _loadClients();
  }

  Future<void> _loadClients() async {
    final clients = await ref.read(resPartnerRepositoryProvider).getAll();
    setState(() {
      _clients = clients.map((client) => {
        'id': client.id.toString(),
        'name': client.name,
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Search field
              Expanded(
                child: TextField(
                  controller: widget.controller,
                  decoration: InputDecoration(
                    hintText: 'Search invoices...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    suffixIcon: widget.controller.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              widget.controller.clear();
                              widget.onSearch('');
                            },
                          )
                        : null,
                  ),
                  onChanged: widget.onSearch,
                ),
              ),

              // Filter button
              IconButton(
                icon: Icon(
                  Icons.filter_list,
                  color: _showFilters ? Theme.of(context).primaryColor : null,
                ),
                onPressed: () {
                  setState(() {
                    _showFilters = !_showFilters;
                  });
                },
              ),

              // Refresh button
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: widget.onRefresh,
              ),
            ],
          ),
        ),

        // Filters
        if (_showFilters) _buildFilters(),
      ],
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Client filter
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Client:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _selectedClientId,
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                hint: const Text('All Clients'),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedClientId = newValue;
                  });
                  widget.onFilterChange(clientId: newValue);
                },
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('All Clients'),
                  ),
                  ..._clients.map((client) {
                    return DropdownMenuItem<String>(
                      value: client['id'].toString(),
                      child: Text(client['name'].toString()),
                    );
                  }).toList(),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Status filter
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Status:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _statusOptions.map((status) {
                  final isSelected =
                    (status == 'All' && _selectedStatus == null) ||
                    status == _selectedStatus;

                  return ChoiceChip(
                    label: Text(status),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedStatus = selected
                            ? (status == 'All' ? null : status)
                            : null;
                      });
                      widget.onFilterChange(
                        status: _selectedStatus?.toUpperCase(),
                      );
                    },
                  );
                }).toList(),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Date range filter
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Date Range:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.calendar_today, size: 16),
                    label: Text(
                      _startDate == null
                          ? 'Start Date'
                          : DateFormat('MMM d, yyyy').format(_startDate!),
                    ),
                    onPressed: () => _selectDate(true),
                  ),
                  const Text(' - '),
                  TextButton.icon(
                    icon: const Icon(Icons.calendar_today, size: 16),
                    label: Text(
                      _endDate == null
                          ? 'End Date'
                          : DateFormat('MMM d, yyyy').format(_endDate!),
                    ),
                    onPressed: () => _selectDate(false),
                  ),
                  if (_startDate != null || _endDate != null)
                    IconButton(
                      icon: const Icon(Icons.clear, size: 16),
                      onPressed: () {
                        setState(() {
                          _startDate = null;
                          _endDate = null;
                        });
                        widget.onFilterChange(
                          startDate: null,
                          endDate: null,
                        );
                      },
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Sort order
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Sort:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ChoiceChip(
                    label: const Text('Newest First'),
                    selected: _sortDescending,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _sortDescending = true;
                        });
                        widget.onFilterChange(dateSort: true);
                      }
                    },
                  ),
                  ChoiceChip(
                    label: const Text('Oldest First'),
                    selected: !_sortDescending,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _sortDescending = false;
                        });
                        widget.onFilterChange(dateSort: false);
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate
          ? _startDate ?? DateTime.now().subtract(const Duration(days: 30))
          : _endDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // If end date is before start date, update end date
          if (_endDate != null && _endDate!.isBefore(_startDate!)) {
            _endDate = _startDate!.add(const Duration(days: 1));
          }
        } else {
          _endDate = picked;
          // If start date is after end date, update start date
          if (_startDate != null && _startDate!.isAfter(_endDate!)) {
            _startDate = _endDate!.subtract(const Duration(days: 1));
          }
        }
      });

      // Only apply filter if both dates are selected
      if (_startDate != null && _endDate != null) {
        widget.onFilterChange(
          startDate: DateFormat('yyyy-MM-dd').format(_startDate!),
          endDate: DateFormat('yyyy-MM-dd').format(_endDate!),
        );
      }
    }
  }
}
