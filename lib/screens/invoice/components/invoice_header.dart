import 'package:invoicer/core/constants/color_constants.dart';

 import 'package:flutter/material.dart'; 

import '../invoices_home_screen.dart';

// import '../new/invoice_home_screen.dart';
// import '../new/invoice_screen.dart';

class InvoiceHeader extends StatelessWidget {


  const InvoiceHeader({
    Key? key,required this.title, required this.code
  }) : super(key: key);
  final String title;
  final String code;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Container(
      color:  Theme.of(context).colorScheme.surface,
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 10,
              ),
              Text(title, style: TextStyle(fontSize: 20) ),
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: dangerColor
                ),
                onPressed: () {
                  
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => RegisterHomeScreen()),
                    );
                  
                },
                icon: Icon(Icons.cancel),
                label: Text(
                  "Close",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
