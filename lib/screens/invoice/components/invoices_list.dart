import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/screens/invoice/components/invoice_list_container.dart';

class InvoicesList extends ConsumerWidget {
  const InvoicesList(this.invoiceType, {
    Key? key,
  }) : super(key: key);

  final String invoiceType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Wrap in a SizedBox with a defined height to avoid layout issues
    return InvoiceListContainer(invoiceType);
  }
}
