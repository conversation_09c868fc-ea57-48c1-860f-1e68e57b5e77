import 'clients_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/client/ClientsRequest.dart';
import 'package:invoicer/core/providers/client/client_provider.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/client/edit/client_screen.dart';
import 'package:invoicer/screens/dashboard/components/error_page.dart';

// Original ClientSelectorList widget
class ClientSelectorList extends StatefulWidget {
  @override
  _ClientSelectorListState createState() => _ClientSelectorListState();

  const ClientSelectorList({
    Key? key,
    required this.callback

  }) : super(key: key);

  final Function(String, String) callback;
}

class _ClientSelectorListState extends State<ClientSelectorList> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: new AppBar(),
      body: SingleChildScrollView(
        child: Card(
          // color: bgColor,
          elevation: 5,
          margin: EdgeInsets.all(10),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Container(
                padding: const EdgeInsets.symmetric(
                    vertical: 16.0, horizontal: 16.0),
                child: Column(
                  children: [

                    Center(
                      child: Text("Select a client."),
                    ),
                    ClientsSelector (callback: widget.callback),
                  ],
                )),
          ),
        ),
      ),
    );
  }
}

// Global variable to track selected client
int? selectedClientId;

// New SelectClient widget for invoice form
class SelectClient extends ConsumerStatefulWidget {
  final Function(String, String) callback;

  const SelectClient({
    Key? key,
    required this.callback,
  }) : super(key: key);

  @override
  _SelectClientState createState() => _SelectClientState();
}

class _SelectClientState extends ConsumerState<SelectClient> {
  ClientsRequest req = ClientsRequest();
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Reset the request when the widget initializes
    req = ClientsRequest();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: MediaQuery.of(context).size.height * 0.7,
      child: Column(
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Text(
                  "Select Client",
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              IconButton(
                icon: Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          SizedBox(height: defaultPadding),

          // Search and Add Client Row
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: "Search clients",
                    filled: true,
                    border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                    ),
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    setState(() {
                      req = req.copyWith(query: value);
                      ref.refresh(clientsProvider(req));

                    });
                  },
                ),
              ),
              SizedBox(width: 8),
              ElevatedButton.icon(
                style: TextButton.styleFrom(
                  backgroundColor: mainColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: defaultPadding,
                    vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                  ),
                ),
                onPressed: () async {
                  bool? shouldUpdate = await showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        titlePadding: const EdgeInsets.all(0),
                        contentPadding: const EdgeInsets.all(0),
                        content: SingleChildScrollView(
                          child: Padding(
                            padding: EdgeInsets.all(5),
                            child: ClientScreen(title: 'New Client', code: 'quick', ref: ref),
                          ),
                        ),
                      );
                    },
                  );

                  if (shouldUpdate == true) {
                    // Refresh the clients list
                    var _ = ref.refresh(clientsProvider(ClientsRequest()));
                  }
                },
                icon: Icon(Icons.add),
                label: Text("New Client"),
              ),
            ],
          ),
          SizedBox(height: defaultPadding),

          // Client List
          Expanded(
            child: ref.watch(clientsProvider(req)).when(
              data: (clientsData) {
                List<ResPartnerTableData> clients = clientsData.content;

                if (clients.isEmpty) {
                  return Center(
                    child: Text(
                      "No clients found",
                      style: TextStyle(color: Colors.grey),
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: clients.length,
                  itemBuilder: (context, index) {
                    ResPartnerTableData client = clients[index];
                    bool isSelected = selectedClientId == client.id;

                    return Card(
                      elevation: 2,
                      margin: EdgeInsets.only(bottom: 8),
                      color: isSelected ? Color.fromRGBO(
                        mainColor.r.toInt(),
                        mainColor.g.toInt(),
                        mainColor.b.toInt(),
                        0.1
                      ) : null,
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Color.fromRGBO(
                            mainColor.r.toInt(),
                            mainColor.g.toInt(),
                            mainColor.b.toInt(),
                            0.2
                          ),
                          child: Icon(
                            Icons.person,
                            color: mainColor,
                          ),
                        ),
                        title: Text(
                          client.name ?? "Unknown",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (client.street != null) Text(client.street!),
                            if (client.phone != null) Text("Phone: ${client.phone}"),
                          ],
                        ),
                        trailing: isSelected
                            ? Icon(Icons.check_circle, color: mainColor)
                            : null,
                        onTap: () {
                          setState(() {
                            selectedClientId = client.id;
                          });
                          widget.callback(client.id.toString(), "set");
                          Navigator.of(context).pop();
                        },
                      ),
                    );
                  },
                );
              },
              loading: () => Center(child: CircularProgressIndicator()),
              error: (e, st) => ErrorPage(
                error: e.toString(),
                onTryAgain: () => ref.refresh(clientsProvider(req)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
