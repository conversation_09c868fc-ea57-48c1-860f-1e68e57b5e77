import 'dart:math' as math;
import 'package:flutter/material.dart';

class InvoiceListPagination extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final int page_size;
  final int totalItems;
  final bool isDescending;
  final String? selectedClientId;
  final List<Map<String, dynamic>> clients;
  final Function(int) onPageChanged;
  final Function(int) onPageSizeChanged;
  final Function(bool) onSortDirectionChanged;
  final Function(String?) onClientFilterChanged;

  const InvoiceListPagination({
    Key? key,
    required this.currentPage,
    required this.totalPages,
    required this.page_size,
    required this.totalItems,
    required this.isDescending,
    required this.clients,
    this.selectedClientId,
    required this.onPageChanged,
    required this.onPageSizeChanged,
    required this.onSortDirectionChanged,
    required this.onClientFilterChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate which page numbers to show
    List<int> page_numbers = _calculatePageNumbers();

    // Available page sizes
    final List<int> availablePageSizes = [10, 20, 50, 100];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            // Info and controls - responsive layout
            LayoutBuilder(
              builder: (context, constraints) {
                // Check if we're on a small screen
                final isSmallScreen = constraints.maxWidth < 600;

                if (true) {
                  // For larger screens, use the original row layout
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Client filter dropdown
                      // Row(
                      //   children: [
                      //     Text(isSmallScreen ? '' : 'Client: ',
                      //       style: TextStyle(color: Colors.grey[700])),
                      //     DropdownButton<String>(
                      //       value: selectedClientId,
                      //       hint: const Text('All Clients'),
                      //       onChanged: onClientFilterChanged,
                      //       items: [
                      //         const DropdownMenuItem<String>(
                      //           value: null,
                      //           child: Text('All Clients'),
                      //         ),
                      //         ...clients.map((client) {
                      //           return DropdownMenuItem<String>(
                      //             value: client['id'].toString(),
                      //             child: Text(client['name'].toString()),
                      //           );
                      //         }).toList(),
                      //       ],
                      //     ),
                      //   ],
                      // ),

                      // Page size selector
                      Row(
                        children: [
                          Text(isSmallScreen?'':'Rows per page: ', style: TextStyle(color: Colors.grey[700])),
                          DropdownButton<int>(
                            value: page_size,
                            onChanged: (value) {
                              if (value != null) {
                                onPageSizeChanged(value);
                              }
                            },
                            items: availablePageSizes.map((size) {
                              return DropdownMenuItem<int>(
                                value: size,
                                child: Text('$size'),
                              );
                            }).toList(),
                          ),
                        ],
                      ),

                      // Sort direction toggle
                      Row(
                        children: [
                          Text('Sort: ', style: TextStyle(color: Colors.grey[700])),
                          InkWell(
                            onTap: () => onSortDirectionChanged(!isDescending),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                children: [
                                  Text(
                                    isDescending ? 'Newest first' : 'Oldest first',
                                    style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Icon(
                                    isDescending ? Icons.arrow_downward : Icons.arrow_upward,
                                    size: 16,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Items count
                      Text(
                        'Showing ${currentPage * page_size + 1}-${math.min((currentPage + 1) * page_size, totalItems)} of $totalItems',
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                    ],
                  );
                }
              },
            ),

            const SizedBox(height: 16),

            // Pagination row - wrapped in SingleChildScrollView to prevent overflow
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Previous button
                  IconButton(
                    icon: const Icon(Icons.chevron_left),
                    onPressed: currentPage > 0
                        ? () => onPageChanged(currentPage - 1)
                        : null,
                    color: Theme.of(context).primaryColor,
                    disabledColor: Colors.grey[400],
                  ),

                  // Page numbers
                  ...page_numbers.map((page_number) {
                    final isCurrentPage = page_number == currentPage;

                    if (page_number == -1) {
                      // Ellipsis
                      return const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: Text('...'),
                      );
                    }

                    return InkWell(
                      onTap: isCurrentPage ? null : () => onPageChanged(page_number),
                      borderRadius: BorderRadius.circular(4),
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: isCurrentPage
                              ? Theme.of(context).primaryColor
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: isCurrentPage
                                ? Theme.of(context).primaryColor
                                : Colors.grey[300]!,
                          ),
                        ),
                        child: Text(
                          '${page_number + 1}',
                          style: TextStyle(
                            color: isCurrentPage ? Colors.white : Colors.black87,
                            fontWeight: isCurrentPage ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }),

                  // Next button
                  IconButton(
                    icon: const Icon(Icons.chevron_right),
                    onPressed: currentPage < totalPages - 1
                        ? () => onPageChanged(currentPage + 1)
                        : null,
                    color: Theme.of(context).primaryColor,
                    disabledColor: Colors.grey[400],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<int> _calculatePageNumbers() {
    List<int> page_numbers = [];

    // Always show first page
    page_numbers.add(0);

    // For small number of pages, show all
    if (totalPages <= 7) {
      page_numbers = List.generate(totalPages, (index) => index);
      return page_numbers;
    }

    // For current page near start
    if (currentPage < 3) {
      page_numbers.addAll([1, 2, 3, 4]);
      page_numbers.add(-1); // Ellipsis
      page_numbers.add(totalPages - 1);
      return page_numbers;
    }

    // For current page near end
    if (currentPage > totalPages - 4) {
      page_numbers.add(-1); // Ellipsis
      page_numbers.addAll([
        totalPages - 5,
        totalPages - 4,
        totalPages - 3,
        totalPages - 2,
        totalPages - 1,
      ]);
      return page_numbers;
    }

    // For current page in middle
    page_numbers.add(-1); // Ellipsis
    page_numbers.addAll([
      currentPage - 1,
      currentPage,
      currentPage + 1,
    ]);
    page_numbers.add(-1); // Ellipsis
    page_numbers.add(totalPages - 1);

    return page_numbers;
  }
}
