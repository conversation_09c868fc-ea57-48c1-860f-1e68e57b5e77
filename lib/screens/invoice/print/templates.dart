import 'dart:async';
import 'dart:typed_data';

import 'package:invoicer/core/db/drift/database.dart';
import 'package:pdf/pdf.dart';
import 'package:invoicer/screens/invoice/print/templates/invoice5.dart';

 
const examples = <Example>[
  Example('Plain', 'invoice5.dart', generateInvoice5),
  // Example('Wave', 'invoice.dart', generateInvoice),
  // Example('Circles', 'invoice4.dart', generateInvoice4),
  // Example('Block', 'invoice2.dart', generateInvoice2),
];

typedef LayoutCallbackWithData = Future<Uint8List> Function(
    PdfPageFormat pageFormat, AccountMoveTableData data);

class Example {
  const Example(this.name, this.file, this.builder, [this.needsData = false]);

  final String name;

  final String file;

  final LayoutCallbackWithData builder;

  final bool needsData;
}
