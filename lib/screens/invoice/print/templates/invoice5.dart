/*
 * Copyright (C) 2017, <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:intl/intl.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_service.dart';
import 'package:invoicer/screens/profile/edit/profile_screen.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';

// Future<String?> getLogo() async{
//   var prefs = await SharedPreferences.getInstance();
//   var file =  (await prefs!.getString(UserPreference.activeBusinessLogo));
//   return file;
// }


// Helper function to get payments for an invoice
Future<List<AccountPaymentTableData>> getPaymentsForInvoice(int invoiceId) async {
  final db = DatabaseService().database;
  try {
    print("Fetching payments for invoice ID: $invoiceId using dedicated function");
    final payments = await db.accountPaymentDao.getPaymentsForInvoice(invoiceId);
    print("Found ${payments.length} payments in dedicated function");
    return payments;
  } catch (e,st) {
    print('$e \n$st');
    print("Error fetching payments: $e");
    return [];
  }
  // Note: We don't close the database here anymore since it's managed by the singleton
}

Future<Uint8List> generateInvoice5(
    PdfPageFormat pageFormat, AccountMoveTableData data) async {
  // Initialize default colors
  PdfColor mainColor = PdfColors.blueGrey900;
  PdfColor accent = PdfColors.blueGrey900;
  PdfColor lightAccent = PdfColors.blueGrey900;
  PdfColor complimentAccent = PdfColors.green;

  // Get invoice line items and payments from the database
  List<AccountMoveLineTableData> invoiceLines = [];
  List<AccountPaymentTableData> invoicePayments = [];

  // Create a database instance to access the DAOs
  final db = DatabaseService().database;
  try {
    // Get invoice line items if the invoice has an ID
    if (data.id > 0) {
      print("Fetching data for invoice ID: ${data.id}");

      // Get invoice line items
      invoiceLines = await db.accountMoveLineDao.getLinesForInvoice(data.id);
      print("Found ${invoiceLines.length} line items for invoice ${data.id}");

      // Get invoice payments using the DAO
      invoicePayments = await db.accountPaymentDao.getPaymentsForInvoice(data.id);
      print("Found ${invoicePayments.length} payments for invoice ${data.id} using DAO");

      // If no payments were found using the DAO, try the dedicated function
      if (invoicePayments.isEmpty) {
        print("No payments found using DAO, trying dedicated function");
        invoicePayments = await getPaymentsForInvoice(data.id);
      }

      // Debug payment information
      if (invoicePayments.isEmpty) {
        print("No payments found for invoice ${data.id}. Checking payment state: ${data.payment_state}");

        // If the invoice is marked as paid or partially paid but no payments are found,
        // this might indicate a data inconsistency
        if (data.payment_state == 'paid' || data.payment_state == 'partial') {
          print("Warning: Invoice is marked as ${data.payment_state} but no payments were found");
        }
      } else {
        // Print details of each payment
        for (var payment in invoicePayments) {
          print("Payment details - ID: ${payment.id}, Name: ${payment.name}, Amount: ${payment.amount}, Date: ${payment.payment_date}");
        }
      }
    }
  } catch (e,st) {
    print('$e \n$st');
    print("Error fetching invoice data: $e");
  }
  // Note: We don't close the database here anymore since it's managed by the singleton

  // Initialize default values for customer and company information
  String customer_name = "Customer ID: ${data.partner_id ?? 'N/A'}";
  String customer_address = "";
  String company_info = "";
  String company_name = ""; // This will hold just the company name for the header

  // Debug information
  print("Invoice ID: ${data.id}");
  print("Partner ID: ${data.partner_id}");
  print("Company ID: ${data.company_id}");

  try {
    // Get customer information if available
    if (data.partner_id != null) {
      final partnerDao = db.resPartnerDao;
      final partner = await partnerDao.getPartnerById(data.partner_id!);
      if (partner != null) {
        customer_name = partner.name ?? customer_name;

        // Build address from available fields
        List<String> addressParts = [];
        if (partner.street?.isNotEmpty == true)
          addressParts.add(partner.street!);
        if (partner.city?.isNotEmpty == true)
          addressParts.add(partner.city!);
        if (partner.zip?.isNotEmpty == true)
          addressParts.add(partner.zip!);

        customer_address = addressParts.join(", ");
      }
    }

    // Get company information if available
    if (data.company_id != null) {
      final companyDao = db.resCompanyDao;
      final company = await companyDao.getCompanyById(data.company_id!);
      if (company != null) {
        // Build company info from available fields
        List<String> companyParts = [];

        // Add company name (with fallback to ID if name is empty)
        String name = company.name;
        if (name.isNotEmpty) {
          companyParts.add(name);
          company_name = name; // Store company name separately for header
          print("Company name: $name");
        } else {
          companyParts.add("Company ID: ${company.id}");
          company_name = "Company ID: ${company.id}"; // Store company ID as fallback
          print("Company name not found, using ID: ${company.id}");
        }

        // Add address parts
        if (company.street != null && company.street!.isNotEmpty) {
          companyParts.add(company.street!);
        }

        // Add city and zip together if available
        List<String> cityZip = [];
        if (company.city != null && company.city!.isNotEmpty) {
          cityZip.add(company.city!);
        }

        if (company.zip != null && company.zip!.isNotEmpty) {
          cityZip.add(company.zip!);
        }

        if (cityZip.isNotEmpty) {
          companyParts.add(cityZip.join(" "));
        }

        // Add contact info
        if (company.phone != null && company.phone!.isNotEmpty) {
          companyParts.add("Tel: ${company.phone}");
        }

        if (company.email != null && company.email!.isNotEmpty) {
          companyParts.add("Email: ${company.email}");
        }

        company_info = companyParts.join(" ,");

        // Apply company color if available
        if (company.color != null) {
          print("Using company color: ${company.color}");
          // Convert integer color to RGB components
          int colorValue = company.color!;
          int red = (colorValue >> 16) & 0xFF;
          int green = (colorValue >> 8) & 0xFF;
          int blue = colorValue & 0xFF;

          // Create PDF colors from RGB values (PDF colors use 0-1 range)
          mainColor = PdfColor(red / 255.0, green / 255.0, blue / 255.0);

          // Create accent colors based on the main color
          accent = PdfColor(
            (red * 0.8) / 255.0,
            (green * 0.8) / 255.0,
            (blue * 0.8) / 255.0
          );

          lightAccent = PdfColor(
            (red * 1.2 > 255 ? 255 : red * 1.2) / 255.0,
            (green * 1.2 > 255 ? 255 : green * 1.2) / 255.0,
            (blue * 1.2 > 255 ? 255 : blue * 1.2) / 255.0
          );

          print("Applied company colors - Main: RGB($red, $green, $blue)");
        } else {
          print("No company color found, using default colors");
        }
      }
    }
  } catch (e,st) {
    print('$e \n$st');
    print("Error fetching customer or company info: $e");
  }

  // Create the invoice with the retrieved products list, payments, and customer/company info
  final invoice = LocalInvoice(
    invoice_number: data.name != null ? data.name.toString() : data.id.toString(),
    products: invoiceLines, // Use the retrieved invoice lines
    payments: invoicePayments, // Use the retrieved payments
    logo: !kIsWeb ? ((await getLogoPath(data.company_id ?? 0)) ?? '') : '',
    customer_name: customer_name,
    customer_address: customer_address,
    payment_info: company_info,
    company_name: company_name, // Pass the company name separately
    tax: (data.amount_tax != null && data.amount_untaxed != null && data.amount_untaxed! > 0) ?
      (data.amount_tax! / data.amount_untaxed!) : 0,
    baseColor: mainColor,
    accentColor: accent,
    lightAccent: lightAccent,
    complimentAccent: complimentAccent,
    invoice: data,
  );

  // Debug the invoice creation
  print("Created LocalInvoice with ${invoicePayments.length} payments");

  return await invoice.buildPdf(pageFormat);
}

class LocalInvoice {
  LocalInvoice({
    required this.products,
    required this.logo,
    required this.customer_name,
    required this.customer_address,
    required this.invoice_number,
    required this.tax,
    required this.invoice,
    required this.payment_info,
    required this.baseColor,
    required this.accentColor,
    required this.lightAccent,
    required this.complimentAccent,
    this.company_name = '', // Add company name parameter with default value
    this.payments = const [], // Add payments parameter with default empty list
  });

  final List<AccountMoveLineTableData> products;
  final List<AccountPaymentTableData> payments; // Add payments list
  final String logo;
  final AccountMoveTableData invoice;
  final String customer_name;
  final String customer_address;
  final String invoice_number;
  final double tax;
  final String payment_info;
  final String company_name; // Add company name field
  final PdfColor baseColor;
  final PdfColor accentColor;
  final PdfColor lightAccent;
  final PdfColor complimentAccent;

  static const _darkColor = PdfColors.black;
  static const _lightColor = PdfColors.white;

  PdfColor get _baseTextColor => baseColor.isLight ? _lightColor : _darkColor;

  double get _total {
    if (products.isEmpty) {
      // If products list is empty, use the invoice amount from the invoice object
      return invoice.amount_untaxed ?? 0.0;
    }

    // Calculate total from non-deleted products
    double total = 0.0;
    for (var product in products) {
      if (product.is_deleted != true) {
        // Calculate line total
        double lineTotal = (product.price_unit ?? 0) * (product.quantity ?? 0);
        total += lineTotal;
      }
    }

    return total;
  }

  double get _grandTotal {
    // If invoice has a total amount, use it directly
    if (invoice.amount_total != null) {
      return invoice.amount_total!;
    }
    // Otherwise calculate from _total and tax
    return (_total * (1 + tax));
  }

  // Convert invoice state to human-readable text
  String _getReadableState(String state, String? payment_state) {
    if (state == 'draft') return 'DRAFT';
    if (state == 'posted') {
      if (payment_state == 'paid') return 'PAID';
      if (payment_state == 'partial') return 'PARTIALLY PAID';
      if (payment_state == 'not_paid') return 'UNPAID';
      return 'POSTED';
    }
    if (state == 'cancel') return 'CANCELLED';
    return state.toUpperCase();
  }

  // Get color based on invoice state
  PdfColor _getStateColor(String state, String? payment_state) {
    if (state == 'draft') return PdfColors.grey700;
    if (state == 'posted') {
      if (payment_state == 'paid') return PdfColors.green700;
      if (payment_state == 'partial') return PdfColors.orange700;
      if (payment_state == 'not_paid') return PdfColors.red700;
      return PdfColors.blue700;
    }
    if (state == 'cancel') return PdfColors.red700;
    return PdfColors.grey700;
  }

  ImageProvider? _logo;

  // SVG shapes for background (not used in this template but kept for future use)
  // String? _bgShape;
  // String? _bgShape2;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = Document();

    // Debug payments list
    print("Building PDF with ${payments.length} payments");
    for (var payment in payments) {
      print("Payment in buildPdf: ID=${payment.id}, Name=${payment.name}, Amount=${payment.amount}");
    }

    // Get download path (used for logo loading)
    await getDownloadPath2();

    if(logo!=''&&!kIsWeb)_logo = MemoryImage(File(logo).readAsBytesSync());

    // Company information is now accessed through a separate repository
    // This would need to be updated to use the proper company repository
    // Load SVG shapes (not used in this template but kept for future use)
    // await rootBundle.loadString('assets/logo/invoice4.svg');
    // await rootBundle.loadString('assets/logo/invoice5.svg');

    var robotoRegularFont = await rootBundle.load("assets/fonts/Roboto/Roboto-Regular.ttf");
    var robotoBoldFont = await rootBundle.load("assets/fonts/Roboto/Roboto-Bold.ttf");
    var robotoItalicFont = await rootBundle.load("assets/fonts/Roboto/Roboto-Italic.ttf");
    var robotoRegular = Font.ttf(robotoRegularFont);
    var robotoBold = Font.ttf(robotoBoldFont);
    var robotoItalic = Font.ttf(robotoItalicFont);


    // Add page to the PDF
    doc.addPage(
      MultiPage(
        pageTheme: _buildTheme(
          pageFormat,
          await robotoRegular,
          await robotoBold,
          await robotoItalic,
        ).copyWith(
          margin:  const EdgeInsets.all(50),
        ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _contentHeader(context),
          _contentTable(context),
          SizedBox(height: 20),
          _contentFooter(context),
          SizedBox(height: 20),
          // Payment information section
          _paymentTable(context),
          SizedBox(height: 20),
          _termsAndConditions(context),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  Widget _buildHeader(Context context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(bottom: 18, top: 10, right: 10),
                    height: 90,
                    child: _logo != null ?
                      Image(_logo!) :
                      Text(
                        _getDocumentType(invoice.move_type),
                        style: TextStyle(
                          color: PdfColors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 40,
                        ),
                      ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  Container(
                    // height: 50,
                    padding: const EdgeInsets.only(left: 20, top: 18),
                    alignment: Alignment.centerRight,
                    child: Text(
                      _getReadableState(invoice.state!, invoice.payment_state),
                      style: TextStyle(
                        color: _getStateColor(invoice.state!, invoice.payment_state),
                        fontWeight: FontWeight.bold,
                        fontSize: 40,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        if (context.pageNumber > 1) SizedBox(height: 20)
      ],
    );
  }

  Widget _buildFooter(Context context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Container(
        //   height: 20,
        //   width: 100,
        //   child: BarcodeWidget(
        //     barcode: Barcode.pdf417(),
        //     data: 'Invoice $invoice_number',
        //     drawText: false,
        //   ),
        // ),
        Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const TextStyle(
            fontSize: 12,
            color: PdfColors.white,
          ),
        ),
      ],
    );
  }

  PageTheme _buildTheme(
      PdfPageFormat pageFormat, Font base, Font bold, Font italic) {
    return PageTheme(
      pageFormat: pageFormat,
      theme: ThemeData.withFont(
        base: base,
        bold: bold,
        italic: italic,
      ),
    );
  }

  Widget _contentHeader(Context context) {
    // Get readable document type
    String documentType = _getDocumentType(invoice.move_type);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            // margin: const EdgeInsets.symmetric(horizontal: 20),
            height: 130,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "$documentType: $invoice_number",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: PdfColors.black,
                  ),
                ),
                // Use the company name directly
                Text(
                  "$documentType From: $company_name",
                  style: TextStyle(
                    color: PdfColors.black,
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                Text(
                  "$documentType Date: " + (invoice.invoice_date != null ?
                    _formatDate(DateTime.parse(invoice.invoice_date!)) : 'N/A'),
                  style: TextStyle(
                    color: PdfColors.black,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                if (invoice.move_type == 'out_invoice' || invoice.move_type == 'in_invoice')
                  Text(
                    'Due Date: ' + (invoice.invoice_date_due != null ?
                      _formatDate(DateTime.parse(invoice.invoice_date_due!)) : 'N/A'),
                    style: TextStyle(
                      color: PdfColors.black,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                SizedBox(height: 10),
                Text(
                  "$documentType To:",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: PdfColors.black,
                  ),
                ),
                Text(
                  customer_name,
                  style: TextStyle(
                    color: PdfColors.black,
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                Text(
                  customer_address,
                  style: TextStyle(
                    color: PdfColors.black,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ]
            ),
          ),
        ),
      ],
    );
  }

  // Get readable document type based on move_type
  String _getDocumentType(String? move_type) {
    if (move_type == 'out_invoice') return 'Invoice';
    if (move_type == 'in_invoice') return 'Vendor Bill';
    if (move_type == 'out_refund') return 'Credit Note';
    if (move_type == 'in_refund') return 'Vendor Credit Note';
    if (move_type == 'entry') return 'Journal Entry';
    if (move_type == 'out_receipt') return 'Receipt';
    if (move_type == 'in_receipt') return 'Vendor Receipt';
    return move_type?.toUpperCase() ?? 'INVOICE';
  }

  Widget _contentFooter(Context context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: SizedBox(),
        ),
        Expanded(
          flex: 1,
          child: DefaultTextStyle(
            style: const TextStyle(
              fontSize: 10,
              color: _darkColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Sub Total:'),
                    Text((invoice.currency_symbol ?? "\$") + _formatCurrency(invoice.amount_untaxed ?? _total)),
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Tax ('+(tax*100).toStringAsFixed(1)+'%):'),
                    Text((invoice.currency_symbol ?? "\$") + _formatCurrency(invoice.amount_tax ?? (tax * _total))),
                  ],
                ),
                SizedBox(height: 5),
                // Check if there's a discount by comparing total with untaxed + tax
                if (invoice.amount_total != null && invoice.amount_untaxed != null && invoice.amount_tax != null)
                  if ((invoice.amount_untaxed! + invoice.amount_tax!) > invoice.amount_total!)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Discount:'),
                        Text((invoice.currency_symbol ?? "\$") + _formatCurrency((invoice.amount_untaxed! + invoice.amount_tax!) - invoice.amount_total!)),
                      ],
                    ),
                Divider(color: accentColor),
                DefaultTextStyle(
                  style: TextStyle(
                    color: baseColor,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Total:'),
                      Text((invoice.currency_symbol ?? "\$")+_formatCurrency(_grandTotal)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _termsAndConditions(Context context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if(invoice.narration!=null)Text(
            'Notes',
            style: TextStyle(
              color: _darkColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          if(invoice.narration!=null) Text(
            invoice.narration!,
            style: TextStyle(
              color: _darkColor,
              fontSize: 11
            ),
          ),
          if(invoice.narration!=null) SizedBox(height: 20),
          Text(
            'Thank you for your business',
            style: TextStyle(
              color: _darkColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 20, bottom: 8),
            child: Text(
              'Payment Info:',
              style: TextStyle(
                color: baseColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Text(
            payment_info,
            style: const TextStyle(
              fontSize: 8,
              lineSpacing: 5,
              color: _darkColor,
            ),
          ),
        ],
      ),
        SizedBox(height: 10),

        if(invoice.move_type == 'out_invoice' || invoice.move_type == 'in_invoice')Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: accentColor)),
            ),
            padding: const EdgeInsets.only(top: 10, bottom: 4),
            child: Text(
              'Terms & Conditions',
              style: TextStyle(
                fontSize: 12,
                color: baseColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Text(
            'Please make payment before the due date',
            textAlign: TextAlign.justify,
            style: const TextStyle(
              fontSize: 6,
              lineSpacing: 2,
              color: _darkColor,
            ),
          ),
        ],
      ),


      ],
    );
  }

  Widget _contentTable(Context context) {
    const tableHeaders = [
      'Item Description',
      'Unit Price',
      'Quantity',
      'Total'
    ];

    // If products list is empty, show a placeholder row with the invoice total
    if (products.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TableHelper.fromTextArray(
            border: null,
            cellAlignment: Alignment.centerLeft,
            headerDecoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(2)),
              color: baseColor,
            ),
            headerHeight: 25,
            cellHeight: 40,
            cellAlignments: {
              0: Alignment.centerLeft,
              1: Alignment.centerRight,
              2: Alignment.centerRight,
              3: Alignment.centerRight,
            },
            headerStyle: TextStyle(
              color: _baseTextColor,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
            cellStyle: const TextStyle(
              color: PdfColors.black,
              fontSize: 10,
            ),
            rowDecoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: accentColor,
                  width: .5,
                ),
              ),
            ),
            headers: List<String>.generate(
              tableHeaders.length,
              (col) => tableHeaders[col],
            ),
            data: [
              [
                'Invoice Items',
                invoice.amount_untaxed != null ? (invoice.currency_symbol ?? "\$") + _formatCurrency(invoice.amount_untaxed!) : '',
                '1',
                invoice.amount_untaxed != null ? (invoice.currency_symbol ?? "\$") + _formatCurrency(invoice.amount_untaxed!) : '',
              ]
            ],
          ),
          SizedBox(height: 10),
          Text(
            'Note: Detailed invoice items are not available in this view.',
            style: TextStyle(
              color: PdfColors.grey700,
              fontStyle: FontStyle.italic,
              fontSize: 9,
            ),
          ),
        ],
      );
    }

    // Debug information
    print("Found ${products.length} invoice line items");

    // Prepare data for the table
    final tableData = <List<String>>[];

    // Add each product to the table data
    for (var product in products) {
      // Skip deleted items
      if (product.is_deleted == true) continue;

      // Calculate total
      final total = (product.price_unit ?? 0) * (product.quantity ?? 0);

      // Add row data
      tableData.add([
        product.name ?? 'No description',
        (invoice.currency_symbol ?? "\$") + _formatCurrency(product.price_unit ?? 0),
        (product.quantity ?? 0).toString(),
        (invoice.currency_symbol ?? "\$") + _formatCurrency(total),
      ]);
    }

    // If we have no valid products after filtering, show placeholder
    if (tableData.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TableHelper.fromTextArray(
            border: null,
            cellAlignment: Alignment.centerLeft,
            headerDecoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(2)),
              color: baseColor,
            ),
            headerHeight: 25,
            cellHeight: 40,
            cellAlignments: {
              0: Alignment.centerLeft,
              1: Alignment.centerRight,
              2: Alignment.centerRight,
              3: Alignment.centerRight,
            },
            headerStyle: TextStyle(
              color: _baseTextColor,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
            cellStyle: const TextStyle(
              color: PdfColors.black,
              fontSize: 10,
            ),
            rowDecoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: accentColor,
                  width: .5,
                ),
              ),
            ),
            headers: List<String>.generate(
              tableHeaders.length,
              (col) => tableHeaders[col],
            ),
            data: [
              [
                'Invoice Items',
                invoice.amount_untaxed != null ? (invoice.currency_symbol ?? "\$") + _formatCurrency(invoice.amount_untaxed!) : '',
                '1',
                invoice.amount_untaxed != null ? (invoice.currency_symbol ?? "\$") + _formatCurrency(invoice.amount_untaxed!) : '',
              ]
            ],
          ),
          SizedBox(height: 10),
          Text(
            'Note: No valid invoice items found.',
            style: TextStyle(
              color: PdfColors.grey700,
              fontStyle: FontStyle.italic,
              fontSize: 9,
            ),
          ),
        ],
      );
    }

    // If products list is not empty, show the regular table
    return TableHelper.fromTextArray(
      border: null,
      cellAlignment: Alignment.centerLeft,
      headerDecoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(2)),
        color: baseColor,
      ),
      headerHeight: 25,
      cellHeight: 40,
      cellAlignments: {
        0: Alignment.centerLeft,
        1: Alignment.centerRight,
        2: Alignment.centerRight,
        3: Alignment.centerRight,
      },
      headerStyle: TextStyle(
        color: _baseTextColor,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
      cellStyle: const TextStyle(
        color: PdfColors.black,
        fontSize: 10,
      ),
      rowDecoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: accentColor,
            width: .5,
          ),
        ),
      ),
      headers: tableHeaders,
      data: tableData,
    );
  }
  // Payment information table
  Widget _paymentTable(Context context) {
    // Calculate payment information
    double totalAmount = invoice.amount_total ?? 0.0;
    double amountPaid = totalAmount - (invoice.amount_residual ?? 0.0);
    double amountDue = invoice.amount_residual ?? 0.0;

    // Debug payment information
    print("Total Amount: $totalAmount");
    print("Amount Residual: ${invoice.amount_residual}");
    print("Amount Paid: $amountPaid");
    print("Amount Due: $amountDue");
    print("Payment State: ${invoice.payment_state}");
    print("Number of payments: ${payments.length}");

    // Get payment status text and color
    String paymentStatusText = _getPaymentStatusText(invoice.payment_state);
    PdfColor paymentStatusColor = _getPaymentStatusColor(invoice.payment_state);

    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [

          _paymentsTable(context),
        ]
    );
  }

  // Table to display payment history
  Widget _paymentsTable(Context context) {
    const tableHeaders = [
      'Reference',
      'Date',
      'Amount'
    ];

    // Debug payment information
    print("Building payments table with ${payments.length} payments");
    for (var payment in payments) {
      print("Payment: ID=${payment.id}, Name=${payment.name}, Amount=${payment.amount}, Date=${payment.payment_date}");
    }

    // Prepare data for the table
    final tableData = <List<String>>[];

    // Add each payment to the table data
    for (var payment in payments) {
      // Skip deleted payments
      if (payment.is_deleted == true) {
        print("Skipping deleted payment: ${payment.id}");
        continue;
      }

      // Format the payment date if available
      String formattedDate = '';
      if (payment.payment_date != null && payment.payment_date!.isNotEmpty) {
        try {
          formattedDate = _formatDate(DateTime.parse(payment.payment_date!));
        } catch (e) {
          // If date parsing fails, use the raw string
          formattedDate = payment.payment_date!;
        }
      } else if (payment.date != null && payment.date!.isNotEmpty) {
        try {
          formattedDate = _formatDate(DateTime.parse(payment.date!));
        } catch (e) {
          // If date parsing fails, use the raw string
          formattedDate = payment.date!;
        }
      }

      // Add row data
      tableData.add([
        payment.name ?? 'Payment #${payment.id}',
        formattedDate,
        (invoice.currency_symbol ?? "\$") + _formatCurrency(payment.amount ?? 0),
      ]);
    }

    // If no payment data, show a placeholder row
    if (tableData.isEmpty) {
      print("No valid payments to display");
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TableHelper.fromTextArray(
            border: null,
            cellAlignment: Alignment.centerLeft,
            headerDecoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(2)),
              color: baseColor,
            ),
            headerHeight: 25,
            cellHeight: 30,
            cellAlignments: {
              0: Alignment.centerLeft,
              1: Alignment.center,
              2: Alignment.centerRight,
            },
            headerStyle: TextStyle(
              color: _baseTextColor,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
            cellStyle: const TextStyle(
              color: PdfColors.black,
              fontSize: 10,
            ),
            rowDecoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: accentColor,
                  width: .5,
                ),
              ),
            ),
            headers: tableHeaders,
            data: [
              [
                'No payments found',
                '',
                '',
              ]
            ],
          ),
        ],
      );
    }

    return TableHelper.fromTextArray(
      border: null,
      cellAlignment: Alignment.centerLeft,
      headerDecoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(2)),
        color: baseColor,
      ),
      headerHeight: 25,
      cellHeight: 30,
      cellAlignments: {
        0: Alignment.centerLeft,
        1: Alignment.center,
        2: Alignment.centerRight,
      },
      headerStyle: TextStyle(
        color: _baseTextColor,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
      cellStyle: const TextStyle(
        color: PdfColors.black,
        fontSize: 10,
      ),
      rowDecoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: accentColor,
            width: .5,
          ),
        ),
      ),
      headers: tableHeaders,
      data: tableData,
    );
  }

  // Get payment status text
  String _getPaymentStatusText(String? payment_state) {
    if (payment_state == 'paid') return 'PAID';
    if (payment_state == 'partial') return 'PARTIALLY PAID';
    if (payment_state == 'not_paid') return 'UNPAID';
    return payment_state?.toUpperCase() ?? 'UNKNOWN';
  }

  // Get payment status color
  PdfColor _getPaymentStatusColor(String? payment_state) {
    if (payment_state == 'paid') return PdfColors.green700;
    if (payment_state == 'partial') return PdfColors.orange700;
    if (payment_state == 'not_paid') return PdfColors.red700;
    return PdfColors.grey700;
  }
}

String _formatCurrency(double amount) {
  return '${amount.toStringAsFixed(2)}';
}

String _formatDate(DateTime date) {
  final format = DateFormat.yMMMd('en_US');
  return format.format(date);
}

// class Product {
//   const Product(
//     this.sku,
//     this.product_name,
//     this.price,
//     this.quantity,
//   );
//
//   final String sku;
//   final String product_name;
//   final double price;
//   final int quantity;
//   double get total => price * quantity;
//
//   String getIndex(int index) {
//     switch (index) {
//       case 0:
//         return sku;
//       case 1:
//         return product_name;
//       case 2:
//         return _formatCurrency(price);
//       case 3:
//         return quantity.toString();
//       case 4:
//         return _formatCurrency(total);
//     }
//     return '';
//   }
// }

Future<String?> getDownloadPath2() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);

    } else {
      // directory = Directory('/storage/emulated/0/Download/Invoices/');
      // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
      // ignore: avoid_slow_async_io

      directory = await getExternalStorageDirectory();
    }
  } catch (err) {
    print("Cannot get download folder path");
  }
  return directory?.path;
}

// This function is no longer used as payments are now handled through a separate repository

extension AccountMoveLineTableDataExtension on AccountMoveLineTableData {
  double? get total => price_unit != null && quantity != null ? price_unit! * quantity! : null;

  String? getIndex(int index, String currency_symbol) {
    switch (index) {
      case 0: // Item Description
        return name ?? '';
      case 1: // Unit Price
        return price_unit != null ? currency_symbol + _formatCurrency(price_unit!) : '';
      case 2: // Quantity
        return quantity != null ? quantity.toString() : '';
      case 3: // Total
        return total != null ? currency_symbol + _formatCurrency(total!) : '';
      default:
        return '';
    }
  }
}