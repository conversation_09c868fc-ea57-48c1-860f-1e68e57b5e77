
import '../../core/constants/color_constants.dart';
import '../../core/enums/InvoiceType.dart';
import '../../core/utils/responsive.dart';

import '../dashboard/components/header.dart';
import './components/invoices_header.dart';

import './components/invoices_list.dart';
  import 'package:flutter/material.dart';


class RegisterScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              Header(),
              <PERSON><PERSON><PERSON><PERSON>(height: defaultPadding),
              InvoicesHeader(),
              <PERSON><PERSON><PERSON><PERSON>(height: defaultPadding),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [ 
                        InvoicesList(InvoiceType.INVOICE),

                        // RecentUsers(),
                        SizedBox(height: defaultPadding),
                        if (Responsive.isMobile(context))
                          SizedBox(height: defaultPadding),
                      ],
                    ),
                  ),
                  if (!Responsive.isMobile(context))
                    SizedBox(width: defaultPadding), 
                ],
              )
            ],
          ),
      );
  }
}
