import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../../../../core/models/drift/AccountMoveWithItems.dart';

class InvoicePaymentSection extends ConsumerStatefulWidget {
  final AccountMoveWithItems invoice;
  final Function(AccountMoveWithItems) onInvoiceChanged;
  final Function(int) onDeletePayment;
  final Function(AccountPaymentTableData) onAddPayment;
  final List<AccountPaymentTableData>? payments;

  const InvoicePaymentSection({
    Key? key,
    required this.invoice,
    required this.onInvoiceChanged,
    required this.onDeletePayment,
    required this.onAddPayment,
    this.payments,
  }) : super(key: key);

  @override
  _InvoicePaymentSectionState createState() => _InvoicePaymentSectionState();
}

class _InvoicePaymentSectionState extends ConsumerState<InvoicePaymentSection> {
  bool addPayment = false;
  double paymentAmount = 0;
  DateTime payDate = DateTime.now();
  DateFormat dateTimeFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
  DateFormat dateFormat = DateFormat("yyyy-MM-dd");

  void _onDateSelected(DateRangePickerSelectionChangedArgs args) {
    if (args.value is DateTime) {
      setState(() {
        payDate = args.value as DateTime;
      });
    }
  }

  DataRow paymentsDataRow(AccountPaymentTableData payment, int index, BuildContext context) {
    return DataRow(
      cells: [
        DataCell(Container(
          padding: EdgeInsets.all(5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(5.0)),
          ),
          child: Text(payment.id.toString())
        )),
        DataCell(Text(payment.payment_date?.toString().split(" ")[0] ?? "")),
        DataCell(Text(payment.amount?.toString() ?? "")),
        // DataCell(
        //   Row(
        //     children: [
        //       GestureDetector(
        //         child: payment.amount != null
        //           ? Icon(Icons.delete, color: Colors.redAccent)
        //           : SizedBox(),
        //         onTap: () async {
        //           // Call the onDelete callback to update the UI
        //           widget.onDeletePayment(index);
        //
        //           // Use the repository provider to get the payment repository
        //           final paymentRepository = ref.read(accountPaymentRepositoryProvider);
        //
        //           // Delete the payment using the repository
        //           await paymentRepository.delete(payment.id);
        //         },
        //       ),
        //     ],
        //   ),
        // )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get amount due from the invoice model
    double amountDue = widget.invoice.remainingBalance;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Payments",
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 10),

          // Payment summary
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Text("Total Amount: ${(widget.invoice.amount_total ?? 0).toStringAsFixed(2)}"),
                // SizedBox(width: 16),
                Text("Total Paid: ${widget.invoice.formattedTotalPayments}"),
                SizedBox(width: 16),
                Text(
                  "Amount Due: ${widget.invoice.formattedRemainingBalance}",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: amountDue > 0 ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 15),

          // Add payment form
          if (addPayment)
            Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Add Payment",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 10),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          decoration: InputDecoration(
                            labelText: "Amount",
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          initialValue: paymentAmount == 0
                            ? widget.invoice.remainingBalance.toString()
                            : paymentAmount.toString(),
                          onChanged: (value) {
                            try {
                              setState(() {
                                paymentAmount = double.parse(value);
                              });
                            } catch (e) {
                              print("Invalid amount");
                            }
                          },
                        ),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  content: Container(
                                    height: 300,
                                    width: 300,
                                    child: SfDateRangePicker(
                                      onSelectionChanged: _onDateSelected,
                                      selectionMode: DateRangePickerSelectionMode.single,
                                      initialSelectedDate: payDate,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: "Payment Date",
                              border: OutlineInputBorder(),
                            ),
                            child: Text(dateFormat.format(payDate)),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          setState(() {
                            addPayment = false;
                          });
                        },
                        child: Text("Cancel"),
                      ),
                      SizedBox(width: 10),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Create a new payment with the Drift data class
                          final pay = AccountPaymentTableData(
                            id:0,
                            move_id: widget.invoice.id,
                            amount: paymentAmount != 0 ? paymentAmount : widget.invoice.remainingBalance,
                            payment_date: dateTimeFormat.format(payDate),
                            is_synced: false,
                            is_confirmed: true,
                            is_deleted: false,
                            version: 1,
                          );

                          // Call the callback to add the payment
                          widget.onAddPayment(pay);

                          // Reset the form
                          setState(() {
                            addPayment = false;
                            paymentAmount = 0;
                          });
                        },
                        icon: Icon(Icons.add),
                        label: Text("Add"),
                      ),
                    ],
                  ),
                ],
              ),
            ),

          // Payment list
          SizedBox(height: 15),
          widget.payments != null && widget.payments!.isNotEmpty
            ? SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  horizontalMargin: 0,
                  columnSpacing: defaultPadding,
                  columns: [
                    DataColumn(label: Text("Reference")),
                    DataColumn(label: Text("Date")),
                    DataColumn(label: Text("Amount")),
                    // DataColumn(label: Text("Action")),
                  ],
                  rows: List.generate(
                    widget.payments!.length,
                    (index) => paymentsDataRow(
                      widget.payments![index],
                      index,
                      context
                    ),
                  ),
                ),
              )
            : Text("No payments"),

          SizedBox(height: 15),

          // Add payment button
          if (!addPayment)
            ElevatedButton.icon(
              style: TextButton.styleFrom(
                backgroundColor: mainColor,
                padding: EdgeInsets.symmetric(
                  horizontal: defaultPadding * 1.5,
                  vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                ),
              ),
              onPressed: () {
                setState(() {
                  addPayment = true;
                  paymentAmount = widget.invoice.remainingBalance;
                });
              },
              icon: Icon(Icons.add),
              label: Text("Add Payment"),
            ),
        ],
      ),
    );
  }
}
