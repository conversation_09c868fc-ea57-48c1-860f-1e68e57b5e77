import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveWithItems.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:intl/intl.dart';

import '../../components/select_client.dart';

class InvoiceForm extends ConsumerStatefulWidget {
  final AccountMoveWithItems? invoice;
  final Function(AccountMoveWithItems) onInvoiceChanged;
  final String code;

  const InvoiceForm({
    Key? key,
    required this.invoice,
    required this.onInvoiceChanged,
    required this.code,
  }) : super(key: key);

  @override
  _InvoiceFormState createState() => _InvoiceFormState();
}

class _InvoiceFormState extends ConsumerState<InvoiceForm> {
  TextEditingController invoiceNumCon = TextEditingController();
  TextEditingController taxCon = TextEditingController();
  TextEditingController invoiceNoteCon = TextEditingController();
  TextEditingController invoiceCollectedByCon = TextEditingController();
  String memoItems = '';
  DateFormat dateFormat = DateFormat("yyyy-MM-dd");
  
  late AccountMoveWithItems updatedInvoice; 

  @override
  void initState() {
    super.initState(); 
    _updateControllers();
    updatedInvoice = widget.invoice!;
  }

  @override
  void didUpdateWidget(InvoiceForm oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.invoice != widget.invoice) {
      _updateControllers();
    }
  }

  void _updateControllers() {
    if (widget.invoice == null) return;

    invoiceNumCon.text = widget.invoice!.invoice.name ?? '';
    taxCon.text = widget.invoice!.invoice.amount_tax?.toString() ?? '0';
    invoiceNoteCon.text = widget.invoice!.invoice.narration ?? '';
    // Collected by field is no longer used
  }

  void _onDateSelected(DateTime? selectedDate) {
    if (selectedDate != null && widget.invoice != null) {
      final formattedDate = dateFormat.format(selectedDate);
      setState(() {
        final updatedInvoice = widget.invoice!.copyWith(
          invoice: widget.invoice!.invoice.copyWith(invoice_date: Value(formattedDate)),
        );
        widget.onInvoiceChanged(updatedInvoice);
      });
    }
  }

  void _onDueDateSelected(DateTime? selectedDate) {
    if (selectedDate != null && widget.invoice != null) {
      final formattedDate = dateFormat.format(selectedDate);
      setState(() {
        final updatedInvoice = widget.invoice!.copyWith(
          invoice: widget.invoice!.invoice.copyWith(invoice_date_due: Value(formattedDate)),
        );
        widget.onInvoiceChanged(updatedInvoice);
      });
    }
  }

  callback(mem, action) async {
    if (action == "set" && widget.invoice != null) {
      memoItems = mem;
      print("Selected client with id: " + memoItems);

      // Get client using the resPartnerRepositoryProvider
      final clientId = int.parse(mem);
      final client = await ref.read(resPartnerRepositoryProvider).getById(clientId);

      if (client != null) {


        final updatedInvoice = widget.invoice!.copyWith(
          invoice: widget.invoice!.invoice.copyWith(partner_id: Value(client.id)),
        );

        // Update the invoice
        widget.onInvoiceChanged(updatedInvoice);
        setState(() {});
      }
    } else {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Client Selection
        Container(
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Client",
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        border: Border.all(color: Theme.of(context).colorScheme.outline),
                      ),
                      child: widget.invoice != null && widget.invoice!.invoice.partner_id != null
                          ? FutureBuilder<ResPartnerTableData?>(
                              future: ref.read(resPartnerRepositoryProvider).getById(widget.invoice!.invoice.partner_id!),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState == ConnectionState.waiting) {
                                  return Center(child: CircularProgressIndicator());
                                }

                                if (snapshot.hasData && snapshot.data != null) {
                                  return clientProfile(snapshot.data!);
                                }

                                return Text("Client not found");
                              },
                            )
                          : Text("Select Client"),
                    ),
                  ),
                  SizedBox(width: 10),
                  ElevatedButton.icon(
                    style: TextButton.styleFrom(
                      backgroundColor: mainColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                        vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                      ),
                    ),
                    onPressed: () {
                      // Use UncontrolledProviderScope to properly inherit the parent container
                      final container = ProviderScope.containerOf(context, listen: false);

                      showDialog(
                        context: context,
                        builder: (BuildContext dialogContext) {
                          return UncontrolledProviderScope(
                            container: container,
                            child: AlertDialog(
                              content: Container(
                                width: double.maxFinite,
                                child: SelectClient(callback: callback),
                              ),
                            ),
                          );
                        },
                      );
                    },
                    icon: Icon(Icons.add),
                    label: Text("Select Client"),
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: defaultPadding),

        // Invoice Details
        Container(
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Invoice Details",
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  // Status indicator
                  if (widget.invoice != null)
                    _buildStatusIndicator(context, widget.invoice!),
                ],
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: Focus(
                      onFocusChange: (hasFocus) {
                        if (!hasFocus && widget.invoice != null) {
                          widget.onInvoiceChanged(updatedInvoice);
                        }
                      },
                      child: TextFormField(
                        controller: invoiceNumCon,
                        enabled: !(widget.invoice?.invoice.is_synced ?? false),
                        decoration: InputDecoration(
                          labelText: "Invoice Name",
                          border: OutlineInputBorder(),
                          helperText: widget.invoice?.invoice.is_synced ?? false
                              ? "Invoice number cannot be changed after syncing"
                              : null,
                        ),
                        onEditingComplete: () {
                          if (widget.invoice != null) {
                            widget.onInvoiceChanged(updatedInvoice);
                            FocusScope.of(context).unfocus();
                          }
                        },
                        onFieldSubmitted: (value) {
                          if (widget.invoice != null) {
                            widget.onInvoiceChanged(updatedInvoice);
                          }
                        },
                        onChanged: (value) {
                          if (widget.invoice == null) return;
                          updatedInvoice = widget.invoice!.copyWith(
                            invoice: widget.invoice!.invoice.copyWith(name: Value(value)),
                          );
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: widget.invoice != null && widget.invoice!.invoice.invoice_date != null
                              ? dateFormat.parse(widget.invoice!.invoice.invoice_date!)
                              : DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2101),
                        );
                        _onDateSelected(picked);
                      },
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: "Invoice Date",
                          border: OutlineInputBorder(),
                        ),
                        child: Text(widget.invoice?.invoice.invoice_date ?? dateFormat.format(DateTime.now())),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: widget.invoice != null && widget.invoice!.invoice.invoice_date_due != null
                              ? dateFormat.parse(widget.invoice!.invoice.invoice_date_due!)
                              : DateTime.now().add(Duration(days: 30)),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2101),
                        );
                        _onDueDateSelected(picked);
                      },
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: "Due Date",
                          border: OutlineInputBorder(),
                        ),
                        child: Text(widget.invoice?.invoice.invoice_date_due ?? dateFormat.format(DateTime.now().add(Duration(days: 30)))),
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 10),
              Focus(
                onFocusChange: (hasFocus) {
                  if (!hasFocus && widget.invoice != null) {
                    widget.onInvoiceChanged(updatedInvoice);
                  }
                },
                child: TextFormField(
                  controller: invoiceNoteCon,
                  decoration: InputDecoration(
                    labelText: "Notes",
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  onEditingComplete: () {
                    if (widget.invoice != null) {
                      widget.onInvoiceChanged(updatedInvoice);
                      FocusScope.of(context).unfocus();
                    }
                  },
                  onFieldSubmitted: (value) {
                    if (widget.invoice != null) {
                      widget.onInvoiceChanged(updatedInvoice);
                    }
                  },
                  onChanged: (value) {
                    if (widget.invoice == null) return;
                    updatedInvoice = widget.invoice!.copyWith(
                      invoice: widget.invoice!.invoice.copyWith(narration: Value(value)),
                    );
                  },
                ),
              ),

              SizedBox(height: 10),
              // TextFormField(
              //   controller: invoiceCollectedByCon,
              //   decoration: InputDecoration(
              //     labelText: "Collected By",
              //     border: OutlineInputBorder(),
              //   ),
              //   onChanged: (value) {
              //     // Collected by field is no longer used
              //     // We could store this in a custom field if needed
              //     if (widget.invoice != null) {
              //       widget.onInvoiceChanged(widget.invoice!);
              //     }
              //   },
              // ),
            ],
          ),
        ),
      ],
    );
  }

  Widget clientProfile(ResPartnerTableData client) {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            client.name != null && client.name!.isNotEmpty ? client.name! : "Unknown", // Handle null or empty name
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          if (client.street != null) Text(client.street!),
          if (client.phone != null) Text("Phone: ${client.phone}"),
          if (client.email != null) Text("Email: ${client.email}"),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(BuildContext context, AccountMoveWithItems invoice) {
    // Get status color and text
    final (statusColor, statusText) = _getStatusInfo(invoice.invoice.state ?? '');

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: Color.fromRGBO(
          statusColor.r.toInt(),
          statusColor.g.toInt(),
          statusColor.b.toInt(),
          0.1,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusColor,
          width: 1,
        ),
      ),
      child: Text(
        statusText,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: statusColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  (Color, String) _getStatusInfo(String state) {
    switch (state.toLowerCase()) {
      case 'draft':
        return (Colors.grey, 'Draft');
      case 'posted':
        return (Colors.green, 'Posted');
      case 'paid':
        return (Colors.blue, 'Paid');
      case 'cancel':
        return (Colors.red, 'Cancelled');
      default:
        return (Colors.grey, state.isNotEmpty ? state.toUpperCase() : 'Unknown');
    }
  }
}
