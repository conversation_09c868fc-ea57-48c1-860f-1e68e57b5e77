import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import '../../../../core/utils/UserPreference.dart';
import '../../../../core/models/drift/AccountMoveWithItems.dart';

class InvoiceLineItemsTable extends ConsumerStatefulWidget {
  final AccountMoveWithItems invoice;
  final Function(AccountMoveWithItems) onInvoiceChanged;
  final List<AccountMoveLineTableData>? invoiceLines;

  const InvoiceLineItemsTable({
    Key? key,
    required this.invoice,
    required this.onInvoiceChanged,
    this.invoiceLines,
  }) : super(key: key);

  @override
  _InvoiceLineItemsTableState createState() => _InvoiceLineItemsTableState();
}

class _InvoiceLineItemsTableState extends ConsumerState<InvoiceLineItemsTable> {
  List<FocusNode> _focusNodes = [FocusNode()];
  List<LayerLink> _layerLinks = [LayerLink()];
  OverlayEntry? _overlayEntry;
  OverlayState? _overlayState;
  List<ProductProductTableData> productsSearch = [];
  List<AccountMoveLineTableData> _invoiceLines = [];
  bool descChanged = false;
  bool unitChanged = false;
  bool priceChanged = false;
  bool _productClicked = false;

  @override
  void initState() {
    super.initState();
    _overlayState = Overlay.of(context);

    // Initialize invoice lines from the widget property or from the invoice items
    if (widget.invoiceLines != null && widget.invoiceLines!.isNotEmpty) {
      _invoiceLines = List.from(widget.invoiceLines!);
    } else {
      // Try to get invoice lines from the account move line repository
      _addEmptyInvoiceLine();
    }

    // // Initialize focus nodes and layer links for existing line items
    // if (_invoiceLines.isNotEmpty) {
    //   _focusNodes = List.generate(
    //     _invoiceLines.length,
    //     (index) => FocusNode()
    //   );
    //   _layerLinks = List.generate(
    //     _invoiceLines.length,
    //     (index) => LayerLink()
    //   );
    // } else {
    //   // If no invoice lines, create a default empty one
    //   _addEmptyInvoiceLine();
    // }
  }


  // Add an empty invoice line
  void _addEmptyInvoiceLine() {
    final newLine = AccountMoveLineTableData(
      id: 0,
      move_id: widget.invoice.id,
      name: '',
      quantity: 1,
      price_unit: 0,
      price_subtotal: 0,
      price_total: 0,
      is_synced: false,
      is_confirmed: true,
      is_deleted: false,
      version: 1,
    );

    setState(() {
      _invoiceLines.add(newLine);
      _focusNodes.add(FocusNode());
      _layerLinks.add(LayerLink());
    });
  }

  @override
  void dispose() {
    for (var node in _focusNodes) {
      node.dispose();
    }
    _overlayEntry?.remove();
    super.dispose();
  }

  OverlayEntry _createOverlay(int indexItem) {
    return OverlayEntry(
      builder: (context) => Positioned(
        width: MediaQuery.of(context).size.width / 2.8,
        child: CompositedTransformFollower(
          link: _layerLinks[indexItem],
          showWhenUnlinked: false,
          offset: Offset(0.0, -(5.00 + productsSearch.length * 50)),
          child: Material(
            elevation: 5.0,
            child: Column(
              children: [
                SingleChildScrollView(
                  child: productsSearch.isEmpty
                    ? Padding(
                        padding: EdgeInsets.only(),
                        child: Text("No product found."),
                      )
                    : TapRegion(
                        child: Column(
                          children: List.generate(
                            productsSearch.length,
                            (index) => GestureDetector(
                              child: SizedBox(
                                height: 50,
                                child: ListTile(
                                  title: Text(productsSearch[index].name!),
                                ),
                              ),
                              onDoubleTapDown: (ty) {
                                // Create a new AccountMoveLine with the selected product
                                final invoiceItem = AccountMoveLineTableData(
                                  id: _invoiceLines[indexItem].id,
                                  move_id: widget.invoice.id,
                                  product_id: productsSearch[index].id,
                                  name: productsSearch[index].name ?? "",
                                  quantity: 1,
                                  price_unit: productsSearch[index].list_price ?? 0,
                                  price_subtotal: productsSearch[index].list_price ?? 0,
                                  price_total: productsSearch[index].list_price ?? 0,
                                  is_synced: false,
                                  is_confirmed: true,
                                  is_deleted: false,
                                  version: 1,
                                );

                                // Update the invoice line in our local list
                                _invoiceLines[indexItem] = invoiceItem;
                                _productClicked = true;

                                // Update invoice totals
                                // _updateInvoiceTotals();

                                setState(() {});
                              },
                              onDoubleTap: () {
                                _overlayEntry?.remove();
                                _overlayEntry = null;
                              },
                            )
                          ),
                        ),
                        onTapOutside: (data) {
                          if (descChanged) {
                            descChanged = false;
                            _overlayEntry?.remove();
                            _overlayEntry = null;
                          }
                        },
                      )
                ),
                Text("Double tap to select", style: TextStyle(color: mainColor)),
              ],
            )
          ),
        ),
      ),
    );
  }

  void resetOverlay(List<ProductProductTableData> products, int index) {
    if (_focusNodes[index].hasFocus) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      _overlayEntry = _createOverlay(index);
      _overlayState!.insert(_overlayEntry!);
    } else {
      _overlayEntry?.remove();
      _overlayEntry = null;
    }
  }

  // Calculate totals and update the invoice
  // void _updateInvoiceTotals() {
  //   // Calculate subtotal from invoice lines
  //   double subtotal = 0.0;
  //   for (var line in _invoiceLines) {
  //     subtotal += line.price_total ?? 0.0;
  //   }
  //
  //   // Calculate tax amount
  //   double taxAmount = 0.0;
  //   if (widget.invoice.amountTax != null && widget.invoice.amountTax! > 0) {
  //     taxAmount = subtotal * (widget.invoice.amountTax! / 100);
  //   }
  //
  //   // Calculate total
  //   double total = subtotal + taxAmount;
  //
  //
  //
  //   final updatedInvoice = widget.invoice!.copyWith(
  //     invoice: widget.invoice!.invoice.copyWith(
  //         amountUntaxed: Value(subtotal),
  //         amount_total: Value(total)),
  //   );
  //
  //   // Notify parent component of the change
  //   widget.onInvoiceChanged(updatedInvoice);
  // }

  DataRow _buildLineItemRow(AccountMoveLineTableData item, int index) {
    // Ensure we have enough focus nodes and layer links
    if (index >= _focusNodes.length) {
      _focusNodes.add(FocusNode());
      _layerLinks.add(LayerLink());
    }

    // Initialize default values if needed
    if (_invoiceLines[index].quantity == null) {
      _invoiceLines[index] = _invoiceLines[index].copyWith(quantity: Value(1.0));
    }
    if (_invoiceLines[index].price_unit == null) {
      _invoiceLines[index] = _invoiceLines[index].copyWith(price_unit: Value(0.0));
    }

    return DataRow(
      cells: [
        // Quantity
        DataCell(
          Padding(
            padding: EdgeInsets.all(3),
            child: Focus(
              onFocusChange: (hasFocus) {
                if (!hasFocus) {
                  widget.onInvoiceChanged(widget.invoice!.copyWith(items: _invoiceLines));
                }
              },
              child: TextFormField(
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.allow(RegExp(r"[0-9.]"))
                ],
                keyboardType: TextInputType.number,
                controller: TextEditingController(
                  text: _invoiceLines[index].quantity?.toString() ?? '0'
                ),
                onEditingComplete: () {
                  widget.onInvoiceChanged(widget.invoice!.copyWith(items: _invoiceLines));
                },
                onChanged: (String value) {
                  double quantity;
                  try {
                    quantity = double.parse(value);
                  } catch (e) {
                    quantity = 1;
                  }

                  // Get the current price_unit or default to 0
                  final price_unit = _invoiceLines[index].price_unit ?? 0;

                  // Calculate the total price
                  final price_total = quantity * price_unit;

                  // Create a new AccountMoveLine with the updated values
                  final updatedLine = _invoiceLines[index].copyWith(
                    quantity: Value(quantity),
                    price_unit: Value(price_unit),
                    price_total: Value(price_total)
                  );

                  // Update the invoice line in our local list
                  _invoiceLines[index] = updatedLine;

                  // unitChanged = true;
                  // widget.invoice = ;
                  // widget.onInvoiceChanged(widget.invoice);
                },
              ),
            ),
          ),
        ),

        // Description
        DataCell(
          SizedBox(
            width: MediaQuery.of(context).size.width / 2.8,
            child: Padding(
              padding: EdgeInsets.all(3),
              child: CompositedTransformTarget(
                link: _layerLinks[index],
                child: Focus(
                  onFocusChange: (hasFocus) {
                    if (!hasFocus) {
                      widget.onInvoiceChanged(widget.invoice!.copyWith(items: _invoiceLines));
                    }
                  },
                  child: Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          focusNode: _focusNodes[index],
                          keyboardType: TextInputType.text,
                          textCapitalization: TextCapitalization.sentences,
                          controller: TextEditingController(
                            text: _invoiceLines[index].name ?? ''
                          ),
                          decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            suffixIcon: _invoiceLines[index].product_id != null
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.link, color: Colors.green, size: 16),
                                    IconButton(
                                      icon: Icon(Icons.clear, size: 16),
                                      onPressed: () {
                                        // Clear product selection, keep the name
                                        final updatedLine = _invoiceLines[index].copyWith(
                                          product_id: Value(null)
                                        );
                                        _invoiceLines[index] = updatedLine;
                                        setState(() {});
                                      },
                                      tooltip: "Clear product link",
                                    ),
                                  ],
                                )
                              : Icon(Icons.search, color: Colors.grey, size: 16),
                          ),
                          textInputAction: TextInputAction.next,
                          onEditingComplete: () {
                            widget.onInvoiceChanged(widget.invoice!.copyWith(items: _invoiceLines));
                          },
                    onChanged: (v) async {
                      descChanged = true;

                      // Create a new AccountMoveLine with the updated name
                      final updatedLine = _invoiceLines[index].copyWith(
                        name: Value(v)
                      );

                      // Update the invoice line in our local list
                      _invoiceLines[index] = updatedLine;

                      // Update invoice totals
                      // _updateInvoiceTotals();

                      // Only search for products if no product is currently selected
                      // This allows free editing of product names/labels when a product is already selected
                      if (_invoiceLines[index].product_id == null) {
                        // Search for products using the productProductRepositoryProvider
                        productsSearch = await ref.read(productProductRepositoryProvider).search(v);

                        if (productsSearch.isEmpty) {
                          _overlayEntry?.remove();
                          _overlayEntry = null;
                        } else {
                          resetOverlay(productsSearch, index);
                        }
                      } else {
                        // Product is already selected, just hide the overlay if it's showing
                        _overlayEntry?.remove();
                        _overlayEntry = null;
                      }

                    },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),

        // Price
        DataCell(
          Padding(
            padding: EdgeInsets.all(3),
            child: Focus(
              onFocusChange: (hasFocus) {
                if (!hasFocus) {
                  widget.onInvoiceChanged(widget.invoice!.copyWith(items: _invoiceLines));
                }
              },
              child: TextFormField(
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.allow(RegExp(r"[0-9.]"))
                ],
                keyboardType: TextInputType.number,
                controller: TextEditingController(
                  text: _invoiceLines[index].price_unit != 0
                    ? _invoiceLines[index].price_unit?.toStringAsFixed(2) ?? ''
                    : ''
                ),
                onEditingComplete: () {
                  widget.onInvoiceChanged(widget.invoice!.copyWith(items: _invoiceLines));
                },
                onChanged: (String value) {
                  double price_unit;
                  try {
                    price_unit = double.parse(value);
                  } catch (e) {
                    price_unit = 0;
                  }

                  // Get the current quantity or default to 1
                  final quantity = _invoiceLines[index].quantity ?? 1;

                  // Calculate the total price
                  final price_total = price_unit * quantity;

                  // Create a new AccountMoveLine with the updated values
                  final updatedLine = _invoiceLines[index].copyWith(
                    quantity: Value(quantity),
                    price_unit: Value(price_unit),
                    price_total: Value(price_total)
                  );

                  // Update the invoice line in our local list
                  _invoiceLines[index] = updatedLine;

                  // priceChanged = true;

                  // Update invoice totals
                  // _updateInvoiceTotals();
                },
              ),
            ),
          ),
        ),

        // Total
        DataCell(
          Padding(
            padding: EdgeInsets.all(3),
            child: TextField(
              enabled: false,
              controller: TextEditingController(
                text: _invoiceLines[index].price_total?.toStringAsFixed(2)
              ),
              decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ),
          )
        ),

        // Delete button
        DataCell(
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _invoiceLines.length != 1
                ? GestureDetector(
                    child: Icon(Icons.delete, color: Colors.redAccent),
                    onTap: () {
                      _invoiceLines.removeAt(index);
                      _focusNodes.removeAt(index);
                      _layerLinks.removeAt(index);
                      setState(() {});
                      // Notify parent of the change
                      widget.onInvoiceChanged(widget.invoice!.copyWith(items: _invoiceLines));
                    },
                  )
                : SizedBox(width: 0),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Invoice Items",
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 10),
              Container(
                width: double.infinity,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    width: MediaQuery.of(context).size.width - 40,
                    child: Form(
                      child: DecoratedBox(
                        decoration: BoxDecoration(),
                        child: DataTable(
                          horizontalMargin: 0,
                          columnSpacing: defaultPadding,
                          columns: [
                            DataColumn(label: Text("Unit")),
                            DataColumn(label: Text("Description")),
                            DataColumn(label: Text("Price")),
                            DataColumn(label: Text("Amount")),
                            DataColumn(label: Text("")),
                          ],
                          rows: List.generate(
                            _invoiceLines.length,
                            (index) => _buildLineItemRow(
                              _invoiceLines[index],
                              index
                            ),
                          ),
                        ),
                      )
                    ),
                  ),
                ),
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  TapRegion(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        // Show product picker dialog instead of creating empty line
                        await _showProductPickerDialog();
                      },
                      icon: Icon(Icons.add),
                      label: Text("Add Product")
                    ),
                    onTapOutside: (tap) async {
                      if (priceChanged || _productClicked || unitChanged) {
                        _productClicked = false;
                        unitChanged = false;
                        priceChanged = false;

                        // Update invoice totals
                        // _updateInvoiceTotals();

                        setState(() {});
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),

        // Summary
        SizedBox(height: defaultPadding),
        Container(
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Text(
              //   "Summary",
              //   style: Theme.of(context).textTheme.titleMedium,
              // ),
              SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Text("Subtotal: ", style: TextStyle(fontWeight: FontWeight.bold)),
                          SizedBox(width: 10),
                          Text(
                            (widget.invoice.totalBeforeTaxAndDiscounts ?? 0).toStringAsFixed(2),
                            style: TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                      SizedBox(height: 5),
                      Row(
                        children: [
                          Text("Tax: ", style: TextStyle(fontWeight: FontWeight.bold)),
                          SizedBox(width: 10),
                          Text(
                            ((widget.invoice.amount_tax ?? 0)).toStringAsFixed(2),
                            style: TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                      SizedBox(height: 5),
                      Divider(),
                      Row(
                        children: [
                          Text("Total: ", style: TextStyle(fontWeight: FontWeight.bold)),
                          SizedBox(width: 10),
                          Text(
                            (widget.invoice.totalAfterTaxAndDiscounts ?? 0).toStringAsFixed(2),
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Show product picker dialog
  Future<void> _showProductPickerDialog() async {
    final activeBusiness = await SharedPreferences.getInstance().then((prefs) =>
        prefs.getInt(UserPreference.activeBusiness) ?? 0);

    print("DEBUG: Active business ID: $activeBusiness");

    // Get all products to debug the issue
    final allProducts = await ref.read(productProductRepositoryProvider).getAll();
    print("DEBUG: Total products in database: ${allProducts.length}");

    final products = await ref.read(productProductRepositoryProvider).getForCompany(activeBusiness);
    print("DEBUG: Products for company $activeBusiness: ${products.length}");

    // Debug each product
    for (var product in allProducts) {
      print("DEBUG: Product - ID: ${product.id}, Name: ${product.name}, Company: ${product.company_id}, Universal ID: ${product.universal_id}, Synced: ${product.is_synced}, Deleted: ${product.is_deleted}");
    }

    if (products.isEmpty) {
      // Show more detailed error message
      String errorMessage = "No products available. ";
      if (allProducts.isEmpty) {
        errorMessage += "No products found in database. Please sync products from Odoo first.";
      } else {
        errorMessage += "Found ${allProducts.length} products in database, but none match company ID $activeBusiness or sync criteria. Please check sync status.";
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 5),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Select Product"),
          content: Container(
            width: double.maxFinite,
            height: 400,
            child: ListView.builder(
              itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];
                return ListTile(
                  title: Text(product.name ?? 'Unnamed Product'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (product.default_code != null)
                        Text('Code: ${product.default_code}'),
                      Text('Price: \$${product.list_price?.toStringAsFixed(2) ?? '0.00'}'),
                    ],
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    _addProductToInvoice(product);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("Cancel"),
            ),
          ],
        );
      },
    );
  }

  // Add selected product to invoice
  void _addProductToInvoice(ProductProductTableData product) {
    // Create a new invoice line with the selected product
    final newLine = AccountMoveLineTableData(
      id: 0,
      move_id: widget.invoice.id,
      product_id: product.id,
      name: product.name ?? '',
      quantity: 1,
      price_unit: product.list_price ?? 0,
      price_subtotal: product.list_price ?? 0,
      price_total: product.list_price ?? 0,
      is_synced: false,
      is_confirmed: true,
      is_deleted: false,
      version: 1,
    );

    // Add the new line to our local list
    _invoiceLines.add(newLine);

    // Add new focus node and layer link
    _focusNodes.add(FocusNode());
    _layerLinks.add(LayerLink());

    // Update the UI
    setState(() {});
  }
}
