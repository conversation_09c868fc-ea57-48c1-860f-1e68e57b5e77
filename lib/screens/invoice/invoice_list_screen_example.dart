// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/screens/invoice/components/invoice_list_container_improved.dart';

// class InvoiceListScreenExample extends ConsumerWidget {
//   const InvoiceListScreenExample({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return DefaultTabController(
//       length: 4,
//       child: Scaffold(
//         appBar: AppBar(
//           title: const Text('Invoices'),
//           bottom: const TabBar(
//             tabs: [
//               Tab(text: 'Invoices'),
//               Tab(text: 'Bills'),
//               Tab(text: 'Credits'),
//               Tab(text: 'Debits'),
//             ],
//           ),
//           actions: [
//             IconButton(
//               icon: const Icon(Icons.add),
//               onPressed: () {
//                 // Navigate to create invoice screen
//                 Navigator.pushNamed(context, '/invoice/create');
//               },
//             ),
//           ],
//         ),
//         body: const TabBarView(
//           children: [
//             // Each tab shows a different type of invoice
//             InvoiceListContainer('invoice'),
//             InvoiceListContainer('bill'),
//             InvoiceListContainer('credit'),
//             InvoiceListContainer('debit'),
//           ],
//         ),
//       ),
//     );
//   }
// }
