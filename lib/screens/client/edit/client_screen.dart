
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:invoicer/core/db/drift/database.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import '../../../core/utils/UserPreference.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/input_widget.dart';
import '../../../core/utils/responsive.dart';
import '../../profile/edit/profile_home_screen.dart';
import '../clients_home_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';


class ClientScreen extends ConsumerStatefulWidget {
  ClientScreen({required this.title, required this.code, this.clientId, this.ref});
  final String title;
  final String code;
  int? clientId;
  WidgetRef? ref;

  @override
  _ClientScreenState createState() => _ClientScreenState(clientId);
}

// class ClientScreen extends StatefulWidget {
class _ClientScreenState extends ConsumerState<ClientScreen> with SingleTickerProviderStateMixin {
  _ClientScreenState(int? this.clientId);
  int? clientId;





  final _formKey = GlobalKey<FormState>();

  bool isChecked = false;


  // List persons = [];
  // List original = [];



  TextEditingController txtQuery = new TextEditingController();





  // late int crossAxisCount;
  // late double childAspectRatio;
  // late List<Memo> memosSet = [];

  ResPartnerTableData client = ResPartnerTableData(
    id:0,
    name: '', // Empty string is valid for name
    is_synced: false,
    active: true,
    version: 1,
    is_confirmed: true,
    is_deleted: false
  );

   ResCompanyTableData? selectedCompany;
  double balance = 0;

  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();
  TextEditingController con4 = TextEditingController();
  TextEditingController con5 = TextEditingController();
  TextEditingController con6 = TextEditingController();
  List<ResCompanyTableData> companies = [];

  Future<void> _initclient() async {
     var prefs = await SharedPreferences.getInstance();
    var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

    // Get all companies
    companies = await widget.ref!.read(resCompanyRepositoryProvider).getAll();

    if(clientId != null) {
      // Get client by ID
      final fetchedClient = await widget.ref!.read(resPartnerRepositoryProvider).getById(clientId!);
      if (fetchedClient != null) {
        client = fetchedClient;

        // If client has a company, get it
        if (client.company_id != null) {
          selectedCompany = await widget.ref!.read(resCompanyRepositoryProvider).getById(client.company_id!);
        }

        // Set text controllers with client data
        con1.text = client.name??""; // name is non-nullable in the model
        con2.text = client.email ?? "";
        con3.text = client.street ?? "";
        con4.text = client.city ?? "";
        con5.text = "";
        con6.text = client.phone ?? "";
      }
    } else {
      // For new clients, get the active business
      if (activeBusiness != null) {
        // Get the company for the active business
        final company = await widget.ref!.read(resCompanyRepositoryProvider).getById(activeBusiness);
        if (company != null) {
          // Set the selected company
          selectedCompany = company;

          // Update client with company ID
          client = client.copyWith(company_id: Value(company.id));
        }
      }
    }

    setState(() {});
  }


  @override
  void initState() {
    if(widget.ref==null){
      widget.ref = ref;
    }


    _initclient();

    super.initState();

  }


  @override
  Widget build(BuildContext context) {
    // print(client?.toJson().toString());

    if(widget.ref==null){
      widget.ref = ref;
    };

    return Column(
      children: [


        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(
              child:
              Row(
                  children:[
                    // Text( "Active Business:            ", style: TextStyle(fontWeight: FontWeight.bold ),
                    // ),
                    // Container(
                    //   margin: EdgeInsets.only(left: 0),
                    //   padding: EdgeInsets.symmetric(
                    //     horizontal: defaultPadding/100,
                    //     vertical: defaultPadding / 100,
                    //   ),
                    //   decoration: BoxDecoration(
                    //     // color:  Theme.of(context).colorScheme.surface,
                    //     borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                    //     border: Border.all(color: Theme.of(context).colorScheme.outline),
                    //   ),
                    //   child: TextButton(
                    //     child: Text(selectedCompany?.name ?? "Create Business" ),
                    //     onPressed: (){
                    //       if(selectedCompany?.name==null)SchedulerBinding.instance
                    //           .addPostFrameCallback((_) {
                    //         Navigator.pushReplacement(
                    //           context,
                    //           MaterialPageRoute(builder: (context) => ProfileHome(title: 'New Invoice', code: 'invoice',)),
                    //         );
                    //       });
                    //     },
                    //
                    //   ),
                    //
                    // ),
                  ]
              ),

            ),
          ],
        ),
        // Text("Business: "+ (client.business?.name??"Create a business profile first."), style: TextStyle(fontSize: 20,  ) ),
        // if(client.business==null)ElevatedButton.icon(
        //   style: TextButton.styleFrom(
        //     backgroundColor: defaultColor,
        //     padding: EdgeInsets.symmetric(
        //       horizontal: defaultPadding * 1.5,
        //       vertical:
        //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
        //     ),
        //   ),
        //   onPressed: () {
        //     setState(() {
        //
        //     });
        //
        //     Navigator.pop(context);
        //
        //     SchedulerBinding.instance!
        //         .addPostFrameCallback((_) {
        //       Navigator.pushReplacement(
        //         context,
        //         MaterialPageRoute(builder: (context) => ProfileHome(title: 'New Invoice', code: 'invoice',)),
        //       );
        //     });
        //
        //
        //
        //
        //   },
        //   icon: Icon(Icons.add),
        //   label: Text(
        //     "Add Business Profile",
        //   ),
        // ),
        SizedBox(height: 5,),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Expanded(
            //   child:
            //   Row(
            //       children:[
            //         Text( "Balance:     ", style: TextStyle(fontWeight: FontWeight.bold ),
            //         ),
            //         Container(
            //           margin: EdgeInsets.only(left: 0),
            //           padding: EdgeInsets.symmetric(
            //             horizontal: defaultPadding/100,
            //             vertical: defaultPadding / 100,
            //           ),
            //           decoration: BoxDecoration(
            //             color:  Theme.of(context).colorScheme.surface,
            //             borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
            //             border: Border.all(color: Theme.of(context).colorScheme.outline)
            //           ),
            //           child: TextButton(
            //             // child: Text("\$ "+balance.toString() ),
            //             child: Text("No network" ),
            //             onPressed: () {
            //             },
            //             // Delete
            //           ),
            //
            //         ),
            //       ]
            //   ),
            //
            // ),
          ],
        ),
        SizedBox(height: defaultPadding),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height - 0.0,
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [

                          SizedBox(height: 16.0),

                          Responsive.isMobile(context)
                              ? SizedBox( height: 480,child: Column(children: [
                            Expanded(
                              child: Column(children: [

                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child:
                                      Padding(
                                        padding: EdgeInsets.only(left: 5, right:5),
                                        child: InputWidget(
                                          topLabel: "Client Name",
                                          keyboardType: TextInputType.text,
                                          kController: con1,
                                          onSaved: (String? value) {
                                          },
                                          onChanged: (String? value) {
                                            // Update client with new name
                                            client = client.copyWith(name: Value(value ?? ''));
                                          },
                                          validator: (value) {
                                            if (value == null || value.isEmpty) {
                                              return 'Please enter business name.';
                                            }
                                            return null;
                                          },
                                          // kInitialValue: client.name ?? "",


                                          // prefixIcon: FlutterIcons.chevron_left_fea,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 3),
                                  ],),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child:
                                      Padding(
                                        padding: EdgeInsets.only(left: 5, right:5),
                                        child: InputWidget(
                                          topLabel: "Email",
                                          kController: con2,
                                          keyboardType: TextInputType.text,
                                          onSaved: (String? value) {
                                          },
                                          onChanged: (String? value) {
                                            // Update client with new email
                                            client = client.copyWith(email: Value(value));
                                          },
                                          // kInitialValue: client!.email ,


                                          // prefixIcon: FlutterIcons.chevron_left_fea,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 3),
                                  ],),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child:
                                      Padding(
                                        padding: EdgeInsets.only(left: 5, right:5),
                                        child: InputWidget(
                                          topLabel: "Address",
                                          kController: con3,
                                          keyboardType: TextInputType.text,
                                          onSaved: (String? value) {
                                          },
                                          onChanged: (String? value) {
                                            // Update client with new street
                                            client = client.copyWith(street: Value(value));
                                          },
                                          // kInitialValue: client!.street ,


                                          // prefixIcon: FlutterIcons.chevron_left_fea,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 3),
                                  ],),
                              ],),

                            ),
                            Expanded(
                              child: Column(children: [

                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child:
                                      Padding(
                                        padding: EdgeInsets.only(left: 5, right:5),
                                        child: InputWidget(
                                          topLabel: "City",
                                          kController: con4,
                                          keyboardType: TextInputType.text,
                                          onSaved: (String? value) {
                                          },
                                          onChanged: (String? value) {
                                            // Update client with new city
                                            client = client.copyWith(city: Value(value));
                                          },
                                          validator: (String? value) {
                                            return (value != null && value.contains('@'))
                                                ? 'Do not use the @ char.'
                                                : null;
                                          },
                                          // kInitialValue: client!.city,


                                          // prefixIcon: FlutterIcons.chevron_left_fea,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 3),
                                  ],),
                                // Row(
                                //   mainAxisAlignment: MainAxisAlignment.end,
                                //   children: [
                                //     Expanded(
                                //       child:
                                //       Padding(
                                //         padding: EdgeInsets.only(left: 5, right:5),
                                //         child: InputWidget(
                                //           topLabel: "Country",
                                //           kController: con5,
                                //           keyboardType: TextInputType.text,
                                //           onSaved: (String? value) {
                                //           },
                                //           onChanged: (String? value) {
                                //             // client.country_id = value;
                                //           },
                                //           validator: (String? value) {
                                //             return (value != null && value.contains('@'))
                                //                 ? 'Do not use the @ char.'
                                //                 : null;
                                //           },
                                //           // kInitialValue: client!.country,
                                //
                                //
                                //           // prefixIcon: FlutterIcons.chevron_left_fea,
                                //         ),
                                //       ),
                                //     ),
                                //     SizedBox(height: 3),
                                //   ],),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child:
                                      Padding(
                                        padding: EdgeInsets.only(left: 5, right:5),
                                        child: InputWidget(
                                          topLabel: "Phone Number",
                                    kController: con6,
                                          keyboardType: TextInputType.text,
                                          onSaved: (String? value) {
                                          },
                                          onChanged: (String? value) {
                                            // Update client with new phone
                                            client = client.copyWith(phone: Value(value));
                                          },
                                          validator: (String? value) {
                                            return (value != null && value.contains('@'))
                                                ? 'Do not use the @ char.'
                                                : null;
                                          },
                                          // kInitialValue: client!.phone ,


                                          // prefixIcon: FlutterIcons.chevron_left_fea,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 3),
                                  ],),
                              ],),

                            ),
                          ],))
                              : Row(
                            children: [
                              Expanded(
                                child: Column(children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Expanded(
                                        child:
                                        Padding(
                                          padding: EdgeInsets.only(left: 5, right:5),
                                          child: InputWidget(
                                            topLabel: "Business Name",
                                            keyboardType: TextInputType.text,
                                            kController: con1,
                                            onSaved: (String? value) {
                                            },
                                            onChanged: (String? value) {
                                              // Update client with new name
                                              client = client.copyWith(name: Value(value ?? ''));
                                            },
                                            validator: (value) {
                                              if (value == null || value.isEmpty) {
                                                return 'Please enter business name.';
                                              }
                                              return null;
                                            },
                                            // kInitialValue: client.name ?? '',


                                            // prefixIcon: FlutterIcons.chevron_left_fea,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 3),
                                    ],),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Expanded(
                                        child:
                                        Padding(
                                          padding: EdgeInsets.only(left: 5, right:5),
                                          child: InputWidget(
                                            topLabel: "Email",
                                            keyboardType: TextInputType.text,
                                            kController: con2,
                                            onSaved: (String? value) {
                                            },
                                            onChanged: (String? value) {
                                              client = client.copyWith(email: Value(value));
                                            },
                                            // kInitialValue: client!.email ,


                                            // prefixIcon: FlutterIcons.chevron_left_fea,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 3),
                                    ],),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Expanded(
                                        child:
                                        Padding(
                                          padding: EdgeInsets.only(left: 5, right:5),
                                          child: InputWidget(
                                            topLabel: "Address",
                                            keyboardType: TextInputType.text,
                                            kController: con3,
                                            onSaved: (String? value) {
                                            },
                                            onChanged: (String? value) {
                                              client = client.copyWith(street: Value(value));
                                            },
                                            // kInitialValue: client!.street ,


                                            // prefixIcon: FlutterIcons.chevron_left_fea,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 3),
                                    ],),
                                ],),

                              ),
                              Expanded(
                                child: Column(children: [

                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Expanded(
                                        child:
                                        Padding(
                                          padding: EdgeInsets.only(left: 5, right:5),
                                          child: InputWidget(
                                            topLabel: "City",
                                            keyboardType: TextInputType.text,
                                            kController: con4,
                                            onSaved: (String? value) {
                                            },
                                            onChanged: (String? value) {
                                              client = client.copyWith(city: Value(value));
                                            },
                                            validator: (String? value) {
                                              return (value != null && value.contains('@'))
                                                  ? 'Do not use the @ char.'
                                                  : null;
                                            },
                                            // kInitialValue: client!.city,


                                            // prefixIcon: FlutterIcons.chevron_left_fea,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 3),
                                    ],),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Expanded(
                                        child:
                                        Padding(
                                          padding: EdgeInsets.only(left: 5, right:5),
                                          child: InputWidget(
                                            topLabel: "Country",
                                            kController: con5,
                                            keyboardType: TextInputType.text,
                                            onSaved: (String? value) {
                                            },
                                            onChanged: (String? value) {
                                              // client.country = value;
                                            },
                                            validator: (String? value) {
                                              return (value != null && value.contains('@'))
                                                  ? 'Do not use the @ char.'
                                                  : null;
                                            },
                                            // kInitialValue: client!.country,


                                            // prefixIcon: FlutterIcons.chevron_left_fea,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 3),
                                    ],),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Expanded(
                                        child:
                                        Padding(
                                          padding: EdgeInsets.only(left: 5, right:5),
                                          child: InputWidget(
                                            topLabel: "Phone Number",
                                            kController: con6,
                                            keyboardType: TextInputType.text,
                                            onSaved: (String? value) {
                                            },
                                            onChanged: (String? value) {
                                              client = client.copyWith(phone: Value(value));
                                            },
                                            validator: (String? value) {
                                              return (value != null && value.contains('@'))
                                                  ? 'Do not use the @ char.'
                                                  : null;
                                            },
                                            // kInitialValue: client!.phone ,


                                            // prefixIcon: FlutterIcons.chevron_left_fea,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 3),
                                    ],),
                                ],),

                              ),
                            ],),


                          SizedBox(height: 25,),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [

                              ElevatedButton.icon(
                                style: TextButton.styleFrom(
                                  backgroundColor: mainColor,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: defaultPadding * 1.5,
                                    vertical:
                                    defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                  ),
                                ),
                                onPressed: () async {
                                  if(selectedCompany == null){
                                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                      content: Text("Add your business details first"),
                                    ));
                                    return;
                                  }

                                  if (_formKey.currentState!.validate()) {
                                    try {
                                      // Create a companion for saving
                                      final companion = ResPartnerTableCompanion(
                                        id: Value(client.id),
                                        name: Value(client.name),
                                        email: Value(client.email),
                                        street: Value(client.street),
                                        city: Value(client.city),
                                        phone: Value(client.phone),
                                        company_id: Value(selectedCompany!.id),
                                        is_synced: Value(client.is_synced),
                                        is_confirmed: Value(client.is_confirmed),
                                        is_deleted: Value(client.is_deleted),
                                        version: Value(client.version),
                                      );

                                      // Save the client using the repository
                                      var savedId = await widget.ref!.read(resPartnerRepositoryProvider).save(companion);

                                      if(widget.code == "quick"){
                                        Navigator.pop(context,true);
                                      } else {
                                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                          content: Text("Client saved successfully"),
                                        ));

                                        Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(builder: (context) => ClientsHomeScreen()),
                                        );
                                      }
                                    } catch(e,st) {
                                      print('$e \n$st');
                                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                        content: Text("An error occurred. Check all fields: ${e.toString()}"),
                                      ));
                                    }




                                    // }catch(e){
                                    //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                    //     content: Text("An error occured. Check all fields"),
                                    //   ));
                                    // };



                                  }

                                },
                                icon: Icon(Icons.save),
                                label: Text(
                                  "Save Client",
                                ),
                              ),
                            ],
                          ),

                          // _listView(persons),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: defaultPadding),
                  if (Responsive.isMobile(context))
                    SizedBox(height: defaultPadding),
                ],
              ),
            ),
            if (!Responsive.isMobile(context))
              SizedBox(width: defaultPadding),
            // On Mobile means if the screen is less than 850 we dont want to show it
            // if (!Responsive.isMobile(context))
            //z Expanded(
            //   flex: 2,
            //   child: UserDetailsWidget(),
            // ),
          ],
        )
      ],
    );


  }

  Widget businessProfile(ResCompanyTableData c) {
    return Container(
      margin: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TextButton(
        child: Text(c.name),
        onPressed: () {
          // Update the client with the selected company
          selectedCompany = c;
          client = client.copyWith(company_id: Value(c.id));
          context.pop();
          setState(() {});
        },
      ),
    );
  }

}




