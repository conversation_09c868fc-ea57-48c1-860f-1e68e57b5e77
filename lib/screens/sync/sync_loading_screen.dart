import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/providers/sync/sync_provider.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/dashboard/home_screen.dart';

class SyncLoadingScreen extends ConsumerStatefulWidget {
  const SyncLoadingScreen({Key? key}) : super(key: key);

  @override
  _SyncLoadingScreenState createState() => _SyncLoadingScreenState();
}

class _SyncLoadingScreenState extends ConsumerState<SyncLoadingScreen>
    with SingleTickerProviderStateMixin {

  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 2),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final syncState = ref.watch(syncNotifierProvider);

    // Listen to sync state changes and navigate to home screen when sync is complete
    ref.listen<SyncState>(syncNotifierProvider, (previous, current) {
      if (current.toString().contains('SyncState.loaded') &&
          current.toString().contains('Sync Complete')) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => HomeScreen(source: "sync_complete")),
        );
      }
    });

    return Scaffold(
      body: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 600,
          ),
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo or branding
              Image.asset(
                "assets/logo/logo_icon.png",
                height: 100,
              ),
              SizedBox(height: 40),

              // Animation
              Container(
                height: 200,
                child: _buildSyncAnimation(),
              ),
              SizedBox(height: 40),

              // Title
              Text(
                "Syncing with Odoo",
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),

              // Status message
              _buildStatusMessage(syncState),
              SizedBox(height: 24),

              // Progress indicator
              LinearProgressIndicator(
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(greenColor),
              ),
              SizedBox(height: 40),

              // Information text
              Text(
                "This may take a few minutes depending on the amount of data.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 8),
              Text(
                "Please don't close the app during synchronization.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSyncAnimation() {
    // Custom animation with rotating sync icon
    return Container(
      width: 200,
      height: 200,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Rotating sync icon using AnimatedBuilder for continuous rotation
          AnimatedBuilder(
            animation: _controller,
            builder: (_, __) {
              return Transform.rotate(
                angle: _controller.value * 2 * 3.14159,
                child: Icon(
                  Icons.sync,
                  size: 80,
                  color: greenColor,
                ),
              );
            },
          ),
          SizedBox(height: 20), 
        ],
      ),
    );
  }

  Widget _buildStatusMessage(SyncState state) {
    String message = "Preparing data...";

    if (state.toString().contains('SyncState.loaded')) {
      // Extract the message from the state
      String stateStr = state.toString();
      if (stateStr.contains('loaded: ')) {
        message = stateStr.split('loaded: ')[1].replaceAll(')', '');
      }
    } else if (state.toString().contains('SyncState.error')) {
      // Extract error message
      String stateStr = state.toString();
      if (stateStr.contains('error: ')) {
        message = "Error: " + stateStr.split('error: ')[1].replaceAll(')', '');
      } else {
        message = "An error occurred during synchronization";
      }
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: state.toString().contains('SyncState.error')
            ? Colors.red[50]
            : Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            state.toString().contains('SyncState.error')
                ? Icons.error_outline
                : Icons.info_outline,
            color: state.toString().contains('SyncState.error')
                ? Colors.red
                : Colors.blue,
          ),
          SizedBox(width: 12),
          Flexible(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: state.toString().contains('SyncState.error')
                    ? Colors.red[800]
                    : Colors.blue[800],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
