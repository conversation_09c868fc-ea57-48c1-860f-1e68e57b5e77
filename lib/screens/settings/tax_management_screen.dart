import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_provider.dart';
import 'package:drift/drift.dart' as drift;

class TaxManagementScreen extends ConsumerStatefulWidget {
  const TaxManagementScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<TaxManagementScreen> createState() => _TaxManagementScreenState();
}

class _TaxManagementScreenState extends ConsumerState<TaxManagementScreen> {
  List<AccountTaxTableData> taxes = [];
  bool isLoading = true;
  String selectedType = 'all'; // 'all', 'sale', 'purchase'

  @override
  void initState() {
    super.initState();
    _loadTaxes();
  }

  Future<void> _loadTaxes() async {
    setState(() => isLoading = true);
    
    try {
      final taxDao = ref.read(databaseProvider).accountTaxDao;
      
      List<AccountTaxTableData> loadedTaxes;
      if (selectedType == 'all') {
        loadedTaxes = await taxDao.getAllActiveTaxes();
      } else {
        loadedTaxes = await taxDao.getTaxesByType(selectedType);
      }
      
      setState(() {
        taxes = loadedTaxes;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading taxes: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tax Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showTaxDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildTaxList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          const Text('Filter: '),
          const SizedBox(width: 8),
          DropdownButton<String>(
            value: selectedType,
            items: const [
              DropdownMenuItem(value: 'all', child: Text('All Taxes')),
              DropdownMenuItem(value: 'sale', child: Text('Sales Taxes')),
              DropdownMenuItem(value: 'purchase', child: Text('Purchase Taxes')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() => selectedType = value);
                _loadTaxes();
              }
            },
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTaxes,
          ),
        ],
      ),
    );
  }

  Widget _buildTaxList() {
    if (taxes.isEmpty) {
      return const Center(
        child: Text('No taxes found'),
      );
    }

    return ListView.builder(
      itemCount: taxes.length,
      itemBuilder: (context, index) {
        final tax = taxes[index];
        return _buildTaxCard(tax);
      },
    );
  }

  Widget _buildTaxCard(AccountTaxTableData tax) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: ListTile(
        title: Text(tax.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_formatTaxAmount(tax)),
            Text('Type: ${tax.type_tax_use?.toUpperCase() ?? 'NONE'}'),
            if (tax.price_include == true) 
              const Text('Price Included', style: TextStyle(color: Colors.blue)),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: tax.active ?? true,
              onChanged: (value) => _toggleTaxActive(tax, value),
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showTaxDialog(tax: tax);
                    break;
                  case 'delete':
                    _deleteTax(tax);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Text('Edit'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Delete'),
                ),
              ],
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  String _formatTaxAmount(AccountTaxTableData tax) {
    switch (tax.amount_type) {
      case 'percent':
        return '${tax.amount?.toStringAsFixed(1) ?? '0.0'}%';
      case 'fixed':
        return '\$${tax.amount?.toStringAsFixed(2) ?? '0.00'}';
      case 'group':
        return 'Group Tax';
      case 'code':
        return 'Python Code';
      default:
        return 'Unknown';
    }
  }

  Future<void> _toggleTaxActive(AccountTaxTableData tax, bool active) async {
    try {
      final taxDao = ref.read(databaseProvider).accountTaxDao;
      await taxDao.updateTax(
        tax.id,
        AccountTaxTableCompanion(active: drift.Value(active)),
      );
      _loadTaxes();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating tax: $e')),
        );
      }
    }
  }

  Future<void> _deleteTax(AccountTaxTableData tax) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tax'),
        content: Text('Are you sure you want to delete "${tax.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final taxDao = ref.read(databaseProvider).accountTaxDao;
        await taxDao.deleteTax(tax.id);
        _loadTaxes();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Tax deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting tax: $e')),
          );
        }
      }
    }
  }

  void _showTaxDialog({AccountTaxTableData? tax}) {
    showDialog(
      context: context,
      builder: (context) => TaxEditDialog(
        tax: tax,
        onSaved: () {
          Navigator.of(context).pop();
          _loadTaxes();
        },
      ),
    );
  }
}

class TaxEditDialog extends ConsumerStatefulWidget {
  final AccountTaxTableData? tax;
  final VoidCallback onSaved;

  const TaxEditDialog({
    Key? key,
    this.tax,
    required this.onSaved,
  }) : super(key: key);

  @override
  ConsumerState<TaxEditDialog> createState() => _TaxEditDialogState();
}

class _TaxEditDialogState extends ConsumerState<TaxEditDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _amountController;
  late String _amountType;
  late String _taxType;
  late bool _priceInclude;
  late bool _active;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.tax?.name ?? '');
    _amountController = TextEditingController(
      text: widget.tax?.amount?.toString() ?? '0.0',
    );
    _amountType = widget.tax?.amount_type ?? 'percent';
    _taxType = widget.tax?.type_tax_use ?? 'sale';
    _priceInclude = widget.tax?.price_include ?? false;
    _active = widget.tax?.active ?? true;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.tax == null ? 'Create Tax' : 'Edit Tax'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Tax Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a tax name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _amountType,
                decoration: const InputDecoration(labelText: 'Amount Type'),
                items: const [
                  DropdownMenuItem(value: 'percent', child: Text('Percentage')),
                  DropdownMenuItem(value: 'fixed', child: Text('Fixed Amount')),
                  DropdownMenuItem(value: 'group', child: Text('Group of Taxes')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _amountType = value);
                  }
                },
              ),
              const SizedBox(height: 16),
              if (_amountType != 'group')
                TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(
                    labelText: _amountType == 'percent' ? 'Percentage' : 'Amount',
                    suffixText: _amountType == 'percent' ? '%' : '\$',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an amount';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _taxType,
                decoration: const InputDecoration(labelText: 'Tax Type'),
                items: const [
                  DropdownMenuItem(value: 'sale', child: Text('Sales')),
                  DropdownMenuItem(value: 'purchase', child: Text('Purchase')),
                  DropdownMenuItem(value: 'none', child: Text('None')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _taxType = value);
                  }
                },
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Price Include'),
                subtitle: const Text('Tax is included in the price'),
                value: _priceInclude,
                onChanged: (value) => setState(() => _priceInclude = value),
              ),
              SwitchListTile(
                title: const Text('Active'),
                value: _active,
                onChanged: (value) => setState(() => _active = value),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveTax,
          child: const Text('Save'),
        ),
      ],
    );
  }

  Future<void> _saveTax() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final taxDao = ref.read(databaseProvider).accountTaxDao;
      final amount = double.tryParse(_amountController.text) ?? 0.0;

      final companion = AccountTaxTableCompanion(
        id: widget.tax != null ? drift.Value(widget.tax!.id) : const drift.Value.absent(),
        name: drift.Value(_nameController.text),
        amount_type: drift.Value(_amountType),
        amount: drift.Value(amount),
        type_tax_use: drift.Value(_taxType),
        price_include: drift.Value(_priceInclude),
        active: drift.Value(_active),
      );

      if (widget.tax == null) {
        await taxDao.createTax(companion);
      } else {
        await taxDao.updateTax(widget.tax!.id, companion);
      }

      widget.onSaved();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving tax: $e')),
        );
      }
    }
  }
}
