import 'package:flutter_charts/flutter_charts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/providers/product/paginated_product_product_table_data.dart';
import 'package:invoicer/core/providers/product/products_request.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/core/utils/colorful_tag.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/exceptions/custom_exception.dart';
import '../../../core/db/drift/database.dart';
import '../../../core/providers/product/product_provider.dart';
import '../../../core/utils/responsive.dart';
import '../../dashboard/components/error_page.dart';
import '../../dashboard/components/pagination_widget.dart';
import '../view/product_view_home_screen.dart';

class ProductsList extends ConsumerStatefulWidget {
  const ProductsList({
    Key? key,
  }) : super(key: key);

  @override
  _ProductsListState createState() => _ProductsListState();
}


class _ProductsListState extends ConsumerState<ProductsList> {


  List<ProductProductTableData> products = [];

  String filter = 'ALL';
  String dateSort = 'asc';

  // Initialize request and result objects
  ProductsRequest req = ProductsRequest();
  PaginatedProductProductTableData res = PaginatedProductProductTableData(content: [], totalItems: 0, offset: 0, itemCount: 0, page_number: 0);

  // This method is no longer needed as we're using the productsProvider
  Future<void> _initProducts() async {
    // No need to fetch products manually, the provider handles it
  }

  refreshProviders(request){
    req = request;
    // Refresh the products provider and store the result
    var _ = ref.refresh(productsProvider(req).future);
    setState(() {
      // Update UI
    });
  }

  @override
  void initState() {
    super.initState();
    refreshProviders(req);
    _initProducts();
  }


  @override
  Widget build(BuildContext context) {
    final scrollController = ScrollController();
    return DefaultTabController(
        length: 2,
        child: Container(
          // height: 700,
          constraints: BoxConstraints(minHeight: 200, maxHeight : MediaQuery.of(context).size.height - 200),
          child: TabBarView(

              children: [
                Column(
                  children: [
                    Container(
                      padding: EdgeInsets.all(defaultPadding),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: const BorderRadius.all(Radius.circular(10)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,

                            children: [
                              Text(
                                "Product List",
                                style: Theme.of(context).textTheme.titleMedium,
                              ),

                            ],
                          ),
                          Scrollbar(
                              controller: scrollController,
                              thumbVisibility: true, //always show scrollbar
                              thickness: 10, //width of scrollbar
                              radius: Radius.circular(20), //corner radius of scrollbar
                              scrollbarOrientation: ScrollbarOrientation.bottom, //which side to show scrollbar
                              child: Padding(
                                padding: EdgeInsets.only(bottom: 20 ),
                                child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    controller: scrollController,
                                    child: ConstrainedBox(
                                      constraints: new BoxConstraints(
                                        minWidth: Responsive.isDesktop(context)?((MediaQuery.of(context).size.width/6)*5)-50:(MediaQuery.of(context).size.width-60),
                                      ),
                                      child: ref.watch(productsProvider(req)).when(
                                        data: (data){
                                          res = data;
                                          products = data.content;
                                          return new DecoratedBox(
                                            decoration: new BoxDecoration(),
                                            child: DataTable(
                                              horizontalMargin: 0,
                                              columnSpacing: defaultPadding,
                                              columns: [
                                                DataColumn(
                                                  label: Text("Id"),
                                                ),DataColumn(
                                                  label: Text("Name"),
                                                ),

                                                DataColumn(
                                                  label: Text("Price"),
                                                ),
                                                DataColumn(
                                                  label: Text("Actions"),
                                                ),
                                              ],
                                              rows: List.generate(
                                                products.length,
                                                    (index) => recentUserDataRow(products[index]),
                                              ),
                                            ),
                                          );
                                        },
                                        loading: () =>
                                            Padding(
                                              padding: EdgeInsets.all(10),
                                              child: Center(child: CircularProgressIndicator()),
                                            ),
                                        error: (e, st) => ErrorPage(
                                          error: e is CustomException ? e.message : e.toString(),
                                          onTryAgain: () => setState,
                                        ),
                                      ),
                                    )
                                ),
                              )
                          ),
                          PaginationWidget(res: res, req:req, getItems: refreshProviders),
                        ],
                      ),
                    ),
                    // Text("Swipe right >"),
                    Expanded(child: SizedBox(),)
                  ],
                ),

                ref.watch(getProductDashProvider(0)).when(
                  data: (data){
                    return chartToRun(data);
                  },
                  loading: () =>
                      Padding(
                        padding: EdgeInsets.all(10),
                        child: Center(child: CircularProgressIndicator()),
                      ),
                  error: (e, st) => ErrorPage(
                    error: e is CustomException ? e.message : e.toString(),
                    onTryAgain: () => null,
                  ),
                )



              ]
          ),
        )
    );
  }


  DataRow recentUserDataRow(ProductProductTableData userInfo) {
    deleteDialog(){
      return showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
                title: Center(
                  child: Text("Confirm Deletion"),
                ),
                content: Container(
                  color:  Theme.of(context).colorScheme.surface,
                  height: 70,
                  child: Column(
                    children: [
                      Text(
                          "Are you sure want to delete invoice: ${userInfo.id}'?"),
                      SizedBox(
                        height: 16,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                              icon: Icon(
                                Icons.close,
                                size: 14,
                              ),
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.grey),
                              onPressed: () {
                                context.pop();
                              },
                              label: Text("Cancel")),
                          SizedBox(
                            width: 20,
                          ),
                          ElevatedButton.icon(
                              icon: Icon(
                                Icons.delete,
                                size: 14,
                              ),
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red),
                              onPressed: () async {
                                try{
                                  // Use the productProductRepositoryProvider to delete the product
                                  await ref.read(productProductRepositoryProvider).delete(userInfo.id);
                                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                    content: Text("Product deleted successfully"),
                                  ));
                                }catch(e,st) {
                                  print('$e \n$st');
                                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                    content: Text("An error occurred while deleting"),
                                  ));
                                }
                                context.pop();
                                // Refresh the products list
                                var _ = ref.refresh(productsProvider(ProductsRequest()));
                                // Navigator.push(
                                //   context,
                                //   MaterialPageRoute(builder: (context) => ProductsHomeScreen()),
                                // );
                              },
                              label: Text("Delete"))
                        ],
                      )
                    ],
                  ),
                ));
          });
    }
    return DataRow(
      cells: [
        DataCell(Text(userInfo.id.toString())),
        DataCell(Container(
            padding: EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: getRoleColor(userInfo.name.toString()).withAlpha(51), // 0.2 * 255 = 51
              // border: Border.all(color: Colors.lightBlueAccent),
              borderRadius: BorderRadius.all(Radius.circular(5.0) //
              ),
            ),
            child: Text(userInfo.name != null ? userInfo.name??"" : ""))),
        // DataCell(Text(userInfo.sku?.toString()??"")),
        DataCell(Text(userInfo.list_price?.toString() ?? "0")),
        DataCell(Row(
          children: [
            Responsive.isDesktop(context) ? ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: mainColor,
              ),
              icon: Icon(
                Icons.edit,
                size: 14,
              ),
              onPressed: () {
                Navigator.of(context).push(new MaterialPageRoute<Null>(
                    builder: (BuildContext context) {
                      // return new ProductHome(title: "Edit Product: ${userInfo.id}", code: "edit", invoiceId: userInfo.id );
                      return new ProductViewHome(title: "Edit Product: ${userInfo.id}", code: "edit", invoiceId: userInfo.id );
                    },
                    fullscreenDialog: true));
              },
              // Edit
              label: Text("Edit"),
            ) :

            GestureDetector(
              onTap:(){
                Navigator.of(context).push(new MaterialPageRoute<Null>(
                    builder: (BuildContext context) {
                      // return new ProductHome(title: "Edit Product: ${userInfo.id}", code: "edit", invoiceId: userInfo.id );
                      return new ProductViewHome(title: "Edit Product: ${userInfo.id}", code: "edit", invoiceId: userInfo.id );
                    },
                    fullscreenDialog: true));
              },
              child:Icon(Icons.edit, color:mainColor),
            ),
            SizedBox(
              width: 6,
            ),
            SizedBox(
              width: 6,
            ),
            Responsive.isDesktop(context)
                ? ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.withAlpha(128), // 0.5 * 255 = 128
              ),
              icon: Icon(Icons.delete),
              onPressed: () {
                deleteDialog();
              },
              // Delete
              label: Text("Delete"),
            )
                : GestureDetector(
                onTap: (){
                  deleteDialog();
                } ,
                child: Icon( Icons.delete, color: Colors.red.withAlpha(128),) // 0.5 * 255 = 128
            ),
          ],
        ),),
      ],
    );
  }

}

Widget chartToRun(List<double> data) {
  LabelLayoutStrategy? xContainerLabelLayoutStrategy;
  ChartData chartData;
  ChartOptions chartOptions = const ChartOptions();
  // chartOptions = const ChartOptions.noLabels();
  // Example shows an explicit use of the DefaultIterativeLabelLayoutStrategy.
  // The xContainerLabelLayoutStrategy, if set to null or not set at all,
  //   defaults to DefaultIterativeLabelLayoutStrategy
  // Clients can also create their own LayoutStrategy.
  xContainerLabelLayoutStrategy = DefaultIterativeLabelLayoutStrategy(
    options: chartOptions,
  );
  chartData = ChartData(
    dataRows: [data],
    xUserLabels: const ['Maize', 'Pork', 'Trotters', 'Head', 'Meat', 'Book'],
    dataRowsLegends:  [
      'Sales: Jan 01 - Apr 24 ',
    ],
    chartOptions: chartOptions,
  );
  // chartData.dataRowsDefaultColors(); // if not set, called in constructor
  var verticalBarChartContainer = VerticalBarChartTopContainer(
    chartData: chartData,
    xContainerLabelLayoutStrategy: xContainerLabelLayoutStrategy,
  );

  var verticalBarChart = VerticalBarChart(
    painter: VerticalBarChartPainter(
      verticalBarChartContainer: verticalBarChartContainer,
    ),
  );
  return verticalBarChart;
}


