import 'package:invoicer/core/constants/color_constants.dart';

import 'package:invoicer/core/utils/responsive.dart';
 import 'package:flutter/material.dart';

import '../../category/categorys_home_screen.dart';
import '../edit/product_home_screen.dart';



class ProductsHeader extends StatelessWidget {
  const ProductsHeader({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text( "Products", style: TextStyle(fontSize: 20,fontWeight: FontWeight.bold),),
        SizedBox(height: 10,),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ElevatedButton.icon(
              style: TextButton.styleFrom(
                backgroundColor: mainColor,
                padding: EdgeInsets.symmetric(
                  horizontal: defaultPadding * 1.5,
                  vertical:
                  defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                ),
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => CategorysHomeScreen()),
                );


              },
              icon: Icon(Icons.edit),
              label: Text(
                "Categories",
              ),
            ),

            SizedBox(width: 10,),
            // Product creation disabled - products should only come from Odoo sync
            // ElevatedButton.icon(
            //   style: TextButton.styleFrom(
            //     backgroundColor: mainColor,
            //     padding: EdgeInsets.symmetric(
            //       horizontal: defaultPadding ,
            //       vertical:
            //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
            //     ),
            //   ),
            //   onPressed: () {
            //     Navigator.push(
            //       context,
            //       MaterialPageRoute(builder: (context) => ProductHome(title: 'New Product', code: 'invoice',)),
            //     );
            //   },
            //   icon: Icon(Icons.add),
            //   label: Text(
            //     "Product",
            //   ),
            // ),
            ElevatedButton.icon(
              style: TextButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: EdgeInsets.symmetric(
                  horizontal: defaultPadding ,
                  vertical:
                  defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                ),
              ),
              onPressed: () {
                // TODO: Implement sync products from Odoo functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text("Products are synced from Odoo. Use the sync feature to get latest products."),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
              icon: Icon(Icons.sync),
              label: Text(
                "Sync Products",
              ),
            ),
          ],
        ),
        SizedBox(height: defaultPadding),

        // Responsive(
        //   mobile: InformationCard(
        //     crossAxisCount: _size.width < 650 ? 2 : 4,
        //     childAspectRatio: _size.width < 650 ? 1.2 : 1,
        //   ),
        //   tablet: InformationCard(),
        //   desktop: InformationCard(
        //     childAspectRatio: _size.width < 1400 ? 1.2 : 1.4,
        //   ),
        // ),
      ],
    );
  }
}
