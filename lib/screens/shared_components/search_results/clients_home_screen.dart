import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:flutter/material.dart';
 
import '../../dashboard/components/side_menu.dart';
import 'search_results_screen.dart';

class SearchResultsHomeScreen extends StatelessWidget {
  final List<ResPartnerTableData> clients;

  const SearchResultsHomeScreen({Key? key, required this.clients}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //key: context.read<MenuController>().scaffold<PERSON>ey,
      drawer: SideMenu(),
      body: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // We want this side menu only for large screen
            if (Responsive.isDesktop(context))
              Expanded(
                // default flex = 1
                // and it takes 1/6 part of the screen
                child: SideMenu(),
              ),
            Expanded(
              // It takes 5/6 part of the screen
              flex: 5,
              child: SearchResultsScreen(clients: clients),
            ),
          ],
        ),
      ),
    );
  }
}
