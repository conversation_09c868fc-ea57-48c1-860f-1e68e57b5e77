import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/network/odoo_client.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_odoo_client.dart';

void main() {
  group('OdooClient Tests', () {
    late MockOdooClient mockClient;
    
    setUp(() async {
      await TestHelpers.setUp();
      await TestHelpers.setUpOdooCredentials();
      mockClient = MockOdooClient();
    });
    
    tearDown(() async {
      await TestHelpers.tearDown();
      mockClient.clearMockData();
    });
    
    group('Authentication Tests', () {
      test('should have valid credentials configured', () async {
        final prefs = await SharedPreferences.getInstance();
        
        expect(prefs.getString('odooUrl'), isNotNull);
        expect(prefs.getString('database'), isNotNull);
        expect(prefs.getString('username'), isNotNull);
        expect(prefs.getString('password'), isNotNull);
        expect(prefs.getString('userId'), isNotNull);
        
        print('Credentials configured successfully');
      });
      
      test('should handle missing credentials gracefully', () async {
        // Clear credentials
        final prefs = await SharedPreferences.getInstance();
        await prefs.clear();
        
        // Attempt to make a call without credentials
        expect(
          () async => await OdooClient.instance.executeKw(
            model: 'res.company',
            method: 'search_read',
            args: [[], ['id', 'name']],
          ),
          throwsA(isA<Exception>()),
        );
      });
    });
    
    group('CRUD Operations Tests', () {
      setUp(() {
        // Set up mock data for testing
        mockClient.setMockData('res.company', [
          {
            'id': 1,
            'name': 'Test Company',
            'email': '<EMAIL>',
            'phone': '+1234567890',
          }
        ]);
      });
      
      test('should create records successfully', () async {
        final newCompanyData = {
          'name': 'New Test Company',
          'email': '<EMAIL>',
          'phone': '+0987654321',
        };
        
        final newId = await mockClient.create('res.company', newCompanyData);
        
        expect(newId, isA<int>());
        expect(newId, greaterThan(0));
        
        // Verify the record was created
        final createdRecords = await mockClient.read('res.company', [newId], ['id', 'name', 'email']);
        expect(createdRecords, hasLength(1));
        expect(createdRecords[0]['name'], equals('New Test Company'));
        expect(createdRecords[0]['email'], equals('<EMAIL>'));
      });
      
      test('should read records successfully', () async {
        final records = await mockClient.read('res.company', [1], ['id', 'name', 'email']);
        
        expect(records, hasLength(1));
        expect(records[0]['id'], equals(1));
        expect(records[0]['name'], equals('Test Company'));
        expect(records[0]['email'], equals('<EMAIL>'));
      });
      
      test('should update records successfully', () async {
        final updateData = {
          'name': 'Updated Test Company',
          'email': '<EMAIL>',
        };
        
        final success = await mockClient.write('res.company', [1], updateData);
        expect(success, isTrue);
        
        // Verify the record was updated
        final updatedRecords = await mockClient.read('res.company', [1], ['id', 'name', 'email']);
        expect(updatedRecords[0]['name'], equals('Updated Test Company'));
        expect(updatedRecords[0]['email'], equals('<EMAIL>'));
      });
      
      test('should search records successfully', () async {
        final ids = await mockClient.search('res.company', []);
        
        expect(ids, isA<List<int>>());
        expect(ids, contains(1));
      });
      
      test('should search and read records successfully', () async {
        final records = await mockClient.searchRead(
          'res.company',
          [],
          ['id', 'name', 'email'],
        );
        
        expect(records, hasLength(1));
        expect(records[0]['id'], equals(1));
        expect(records[0]['name'], equals('Test Company'));
      });
    });
    
    group('Error Handling Tests', () {
      test('should handle network errors gracefully', () async {
        // Test with invalid model name
        expect(
          () async => await mockClient.executeKw(
            model: 'invalid.model',
            method: 'search_read',
            args: [[], ['id']],
          ),
          throwsA(isA<Exception>()),
        );
      });
      
      test('should handle invalid method calls', () async {
        expect(
          () async => await mockClient.executeKw(
            model: 'res.company',
            method: 'invalid_method',
            args: [],
          ),
          throwsA(isA<Exception>()),
        );
      });
      
      test('should handle malformed data gracefully', () async {
        // Test create with missing required fields
        expect(
          () async => await mockClient.create('res.company', {}),
          returnsNormally, // Mock should handle this gracefully
        );
      });
    });
    
    group('Real Odoo Server Tests', () {
      test('should connect to real Odoo server and authenticate', () async {
        try {
          // Test basic connection with real credentials
          final result = await OdooClient.instance.executeKw(
            model: 'res.company',
            method: 'search_read',
            args: [[], ['id', 'name']],
            kwargs: {'limit': 1},
          );
          
          expect(result, isA<List>());
          if (result.isNotEmpty) {
            expect(result[0], containsKey('id'));
            expect(result[0], containsKey('name'));
          }
          
          print('Successfully connected to real Odoo server');
          print('Found ${result.length} companies');
        } catch (e) {
          print('Warning: Could not connect to real Odoo server: $e');
          // Don't fail the test if server is unavailable
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
      
      test('should handle real server errors gracefully', () async {
        try {
          // Test with invalid model to trigger server error
          await OdooClient.instance.executeKw(
            model: 'invalid.model.name',
            method: 'search_read',
            args: [[], ['id']],
          );
          
          fail('Expected an exception for invalid model');
        } catch (e) {
          expect(e, isA<Exception>());
          print('Correctly handled server error: $e');
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
    });
    
    group('Data Validation Tests', () {
      test('should validate required fields for companies', () async {
        mockClient.setMockData('res.company', []);
        
        // Test creating company with minimal required data
        final companyData = {
          'name': 'Validation Test Company',
        };
        
        final newId = await mockClient.create('res.company', companyData);
        expect(newId, isA<int>());
        
        // Verify the company was created
        final records = await mockClient.read('res.company', [newId], ['id', 'name']);
        expect(records[0]['name'], equals('Validation Test Company'));
      });
      
      test('should handle special characters in data', () async {
        mockClient.setMockData('res.company', []);
        
        final companyData = {
          'name': 'Test Company with Special Chars: àáâãäåæçèéêë',
          'email': '<EMAIL>',
        };
        
        final newId = await mockClient.create('res.company', companyData);
        expect(newId, isA<int>());
        
        // Verify special characters are preserved
        final records = await mockClient.read('res.company', [newId], ['name', 'email']);
        expect(records[0]['name'], contains('àáâãäåæçèéêë'));
        expect(records[0]['email'], equals('<EMAIL>'));
      });
    });
  });
}
