import 'package:flutter_test/flutter_test.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Basic Sync Tests', () {
    setUp(() async {
      await TestHelpers.setUp();
    });
    
    tearDown(() async {
      await TestHelpers.tearDown();
    });
    
    test('should initialize test environment correctly', () async {
      expect(TestHelpers.testDatabase, isNotNull);
      print('✓ Test database initialized successfully');
    });
    
    test('should set up Odoo credentials', () async {
      await TestHelpers.setUpOdooCredentials();
      print('✓ Odoo credentials configured successfully');
    });
    
    test('should create test company data', () async {
      final company = await TestHelpers.createTestCompany(
        name: 'Basic Test Company',
        universalId: 123,
        isSynced: true,
      );
      
      expect(company.id, greaterThan(0));
      expect(company.name, equals('Basic Test Company'));
      expect(company.universal_id, equals(123));
      expect(company.is_synced, isTrue);
      
      print('✓ Test company created: ${company.name} (ID: ${company.id})');
    });
    
    test('should create test partner data', () async {
      final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
      final partner = await TestHelpers.createTestPartner(
        name: 'Basic Test Partner',
        email: '<EMAIL>',
        companyId: company.id,
        universalId: 456,
        isSynced: true,
      );
      
      expect(partner.id, greaterThan(0));
      expect(partner.name, equals('Basic Test Partner'));
      expect(partner.email, equals('<EMAIL>'));
      expect(partner.company_id, equals(company.id));
      expect(partner.universal_id, equals(456));
      expect(partner.is_synced, isTrue);
      
      print('✓ Test partner created: ${partner.name} (ID: ${partner.id})');
    });
    
    test('should create test product data', () async {
      final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
      final category = await TestHelpers.createTestCategory(universalId: 2, isSynced: true);
      final product = await TestHelpers.createTestProduct(
        name: 'Basic Test Product',
        price: 99.99,
        companyId: company.id,
        categoryId: category.id,
        universalId: 789,
        isSynced: true,
      );
      
      expect(product.id, greaterThan(0));
      expect(product.name, equals('Basic Test Product'));
      expect(product.list_price, equals(99.99));
      expect(product.company_id, equals(company.id));
      expect(product.categ_id, equals(category.id));
      expect(product.universal_id, equals(789));
      expect(product.is_synced, isTrue);
      
      print('✓ Test product created: ${product.name} (ID: ${product.id})');
    });
    
    test('should create test currency data', () async {
      final currency = await TestHelpers.createTestCurrency(
        name: 'USD',
        symbol: '\$',
        rate: 1.0,
        universalId: 101,
        isSynced: true,
      );
      
      expect(currency.id, greaterThan(0));
      expect(currency.name, equals('USD'));
      expect(currency.symbol, equals('\$'));
      expect(currency.rate, equals(1.0));
      expect(currency.universal_id, equals(101));
      expect(currency.is_synced, isTrue);
      
      print('✓ Test currency created: ${currency.name} (ID: ${currency.id})');
    });
    
    test('should create test category data', () async {
      final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
      final category = await TestHelpers.createTestCategory(
        name: 'Basic Test Category',
        companyId: company.id,
        universalId: 202,
        isSynced: true,
      );
      
      expect(category.id, greaterThan(0));
      expect(category.name, equals('Basic Test Category'));
      expect(category.company_id, equals(company.id));
      expect(category.universal_id, equals(202));
      expect(category.is_synced, isTrue);
      
      print('✓ Test category created: ${category.name} (ID: ${category.id})');
    });
    
    test('should clear test data successfully', () async {
      // Create some test data
      await TestHelpers.createTestCompany();
      await TestHelpers.createTestCurrency();
      
      // Verify data exists
      final companiesBefore = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
      final currenciesBefore = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
      expect(companiesBefore, isNotEmpty);
      expect(currenciesBefore, isNotEmpty);
      
      // Clear data
      await TestHelpers.clearTestData();
      
      // Verify data is cleared
      final companiesAfter = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
      final currenciesAfter = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
      expect(companiesAfter, isEmpty);
      expect(currenciesAfter, isEmpty);
      
      print('✓ Test data cleared successfully');
    });
    
    test('should verify sync status correctly', () async {
      final company = await TestHelpers.createTestCompany(
        universalId: 123,
        isSynced: true,
      );
      
      TestHelpers.verifySyncStatus(company, true, 123);
      print('✓ Sync status verification works correctly');
    });
    
    test('should handle async operations', () async {
      await TestHelpers.waitForAsync(const Duration(milliseconds: 50));
      print('✓ Async operations handled correctly');
    });
  });
}
