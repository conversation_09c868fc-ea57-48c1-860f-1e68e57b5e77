import 'package:flutter_test/flutter_test.dart';
import 'package:invoicer/core/network/odoo_client.dart';
import '../helpers/test_helpers.dart';

/// Simple test to verify Odoo connection and authentication
void main() {
  group('Odoo Connection Test', () {
    setUp(() async {
      await TestHelpers.setUp();
      await TestHelpers.setUpOdooCredentials();
    });
    
    tearDown(() async {
      await TestHelpers.tearDown();
    });

    test('should authenticate with Odoo server', () async {
      print('\n🔗 Testing Odoo authentication...');
      
      final odooClient = OdooClient.instance;
      
      try {
        // Test basic authentication by trying to get server version
        final result = await odooClient.executeKw(
          model: 'res.users',
          method: 'search_read',
          args: [
            [['id', '=', 2]], // Search for user ID 2
            ['name', 'login']
          ],
        );
        
        print('✅ Authentication successful');
        print('User data: $result');
        
        expect(result, isNotNull);
        expect(result, isA<List>());
        
      } catch (e) {
        print('❌ Authentication failed: $e');
        rethrow;
      }
    });

    test('should test simple currency fetch', () async {
      print('\n💱 Testing simple currency fetch...');
      
      final odooClient = OdooClient.instance;
      
      try {
        // Test fetching currencies with minimal fields
        final result = await odooClient.executeKw(
          model: 'res.currency',
          method: 'search_read',
          args: [
            [], // No domain filter
            ['name', 'symbol'] // Only basic fields
          ],
        );
        
        print('✅ Currency fetch successful');
        print('Found ${result.length} currencies');
        if (result.isNotEmpty) {
          print('First currency: ${result.first}');
        }
        
        expect(result, isNotNull);
        expect(result, isA<List>());
        
      } catch (e) {
        print('❌ Currency fetch failed: $e');
        rethrow;
      }
    });
  });
}
