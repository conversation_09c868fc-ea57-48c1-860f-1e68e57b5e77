import 'package:flutter_test/flutter_test.dart';
import 'package:invoicer/core/providers/sync/sync_state_notifier.dart';
import 'package:invoicer/core/providers/sync/data/sync_repository.dart';
import 'package:invoicer/core/network/odoo_client.dart';
import 'package:invoicer/core/types/syncresult.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart' hide isNotNull;
import 'package:invoicer/core/db/drift/database.dart';
import '../helpers/test_helpers.dart';

/// Comprehensive test for all sync functionalities as requested
void main() {
  group('Comprehensive Sync Tests', () {
    late SyncRepository syncRepository;
    late ProviderContainer container;
    late SyncNotifier syncNotifier;
    
    setUp(() async {
      await TestHelpers.setUp();
      await TestHelpers.setUpOdooCredentials();
      await TestHelpers.clearTestData();
      
      syncRepository = SyncRepository();
      container = ProviderContainer();
      syncNotifier = SyncNotifier(syncRepository: syncRepository);
    });
    
    tearDown(() async {
      await TestHelpers.tearDown();
      container.dispose();
    });

    group('1. Invoices and Clients Bidirectional Sync', () {
      test('should sync invoices and clients FROM Odoo to local DB', () async {
        print('\n🔄 Testing sync FROM Odoo to local DB...');
        
        try {
          // Perform sync from Odoo
          final success = await syncNotifier.getSync(true);
          expect(success, isTrue, reason: 'Sync from Odoo should succeed');
          
          // Verify invoices were synced
          final invoices = await TestHelpers.testDatabase.accountMoveDao.getAllInvoices();
          final clients = await TestHelpers.testDatabase.resPartnerDao.getAllPartners();
          
          print('✓ Synced ${invoices.length} invoices from Odoo');
          print('✓ Synced ${clients.length} clients from Odoo');
          
          // Verify sync status
          final syncedInvoices = invoices.where((i) => i.is_synced == true).length;
          final syncedClients = clients.where((c) => c.is_synced == true).length;
          
          print('✓ ${syncedInvoices} invoices marked as synced');
          print('✓ ${syncedClients} clients marked as synced');
          
          expect(syncedInvoices, greaterThanOrEqualTo(0));
          expect(syncedClients, greaterThanOrEqualTo(0));
          
        } catch (e) {
          fail('Failed to sync invoices and clients from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 5)));

      test('should sync invoices and clients TO Odoo (in draft state)', () async {
        print('\n🔄 Testing sync TO Odoo...');
        
        try {
          // First sync from Odoo to get some base data
          await syncNotifier.getSync(true);
          
          // Create test client locally
          final testClient = await TestHelpers.createTestPartner(
            name: 'Test Client ${DateTime.now().millisecondsSinceEpoch}',
            email: '<EMAIL>',
            isSynced: false,
          );
          
          print('✓ Created test client: ${testClient.name}');
          
          // Create test invoice locally
          final testInvoice = await _createTestInvoice(testClient.id);
          print('✓ Created test invoice for client');
          
          // Sync to Odoo (using syncAll which does both directions)
          final success = await syncNotifier.syncAll(false);
          expect(success, isTrue, reason: 'Sync to Odoo should succeed');
          
          // Verify client was synced
          final updatedClient = await TestHelpers.testDatabase.resPartnerDao.getPartnerById(testClient.id);
          expect(updatedClient?.is_synced, isTrue, reason: 'Client should be marked as synced');
          expect(updatedClient?.universal_id, isNotNull, reason: 'Client should have universal_id from Odoo');
          
          // Verify invoice was synced
          final updatedInvoice = await TestHelpers.testDatabase.accountMoveDao.getInvoiceById(testInvoice.id);
          expect(updatedInvoice?.is_synced, isTrue, reason: 'Invoice should be marked as synced');
          expect(updatedInvoice?.universal_id, isNotNull, reason: 'Invoice should have universal_id from Odoo');
          
          print('✓ Client synced with universal_id: ${updatedClient?.universal_id}');
          print('✓ Invoice synced with universal_id: ${updatedInvoice?.universal_id}');
          
          // Verify invoice is in draft state in Odoo
          await _verifyInvoiceStateInOdoo(updatedInvoice!.universal_id!, 'draft');
          
        } catch (e) {
          fail('Failed to sync invoices and clients to Odoo: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 5)));
    });

    group('2. Businesses, Products, and Currencies Sync', () {
      test('should sync businesses, products, and currencies FROM Odoo to local DB', () async {
        print('\n🔄 Testing sync of businesses, products, and currencies FROM Odoo...');
        
        try {
          // Perform sync from Odoo
          final success = await syncNotifier.getSync(true);
          expect(success, isTrue, reason: 'Sync from Odoo should succeed');
          
          // Verify data was synced
          final businesses = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
          final products = await TestHelpers.testDatabase.productProductDao.getAllProducts();
          final currencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
          
          print('✓ Synced ${businesses.length} businesses from Odoo');
          print('✓ Synced ${products.length} products from Odoo');
          print('✓ Synced ${currencies.length} currencies from Odoo');
          
          // Verify sync status
          final syncedBusinesses = businesses.where((b) => b.is_synced == true).length;
          final syncedProducts = products.where((p) => p.is_synced == true).length;
          final syncedCurrencies = currencies.where((c) => c.is_synced == true).length;
          
          print('✓ ${syncedBusinesses} businesses marked as synced');
          print('✓ ${syncedProducts} products marked as synced');
          print('✓ ${syncedCurrencies} currencies marked as synced');
          
          expect(syncedBusinesses, greaterThan(0));
          expect(syncedProducts, greaterThanOrEqualTo(0));
          expect(syncedCurrencies, greaterThan(0));
          
        } catch (e) {
          fail('Failed to sync businesses, products, and currencies from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 5)));

      test('should sync businesses, products, and currencies TO Odoo', () async {
        print('\n🔄 Testing sync of businesses, products, and currencies TO Odoo...');
        
        try {
          // First sync from Odoo to get some base data
          await syncNotifier.getSync(true);
          
          // Create test business locally
          final testBusiness = await TestHelpers.createTestCompany(
            name: 'Test Business ${DateTime.now().millisecondsSinceEpoch}',
            isSynced: false,
          );
          print('✓ Created test business: ${testBusiness.name}');
          
          // Create test currency locally
          final testCurrency = await TestHelpers.createTestCurrency(
            name: 'TEST${DateTime.now().millisecondsSinceEpoch}',
            symbol: 'T\$',
            rate: 1.5,
            isSynced: false,
          );
          print('✓ Created test currency: ${testCurrency.name}');
          
          // Create test product locally
          final testProduct = await TestHelpers.createTestProduct(
            name: 'Test Product ${DateTime.now().millisecondsSinceEpoch}',
            price: 99.99,
            isSynced: false,
          );
          print('✓ Created test product: ${testProduct.name}');
          
          // Sync to Odoo (using syncAll which does both directions)
          final success = await syncNotifier.syncAll(false);
          expect(success, isTrue, reason: 'Sync to Odoo should succeed');
          
          // Verify business was synced
          final updatedBusiness = await TestHelpers.testDatabase.resCompanyDao.getCompanyById(testBusiness.id);
          expect(updatedBusiness?.is_synced, isTrue, reason: 'Business should be marked as synced');
          expect(updatedBusiness?.universal_id, isNotNull, reason: 'Business should have universal_id from Odoo');
          
          // Verify currency was synced
          final updatedCurrency = await TestHelpers.testDatabase.resCurrencyDao.getCurrencyById(testCurrency.id);
          expect(updatedCurrency?.is_synced, isTrue, reason: 'Currency should be marked as synced');
          expect(updatedCurrency?.universal_id, isNotNull, reason: 'Currency should have universal_id from Odoo');
          
          // Verify product was synced
          final updatedProduct = await TestHelpers.testDatabase.productProductDao.getProductById(testProduct.id);
          expect(updatedProduct?.is_synced, isTrue, reason: 'Product should be marked as synced');
          expect(updatedProduct?.universal_id, isNotNull, reason: 'Product should have universal_id from Odoo');
          
          print('✓ Business synced with universal_id: ${updatedBusiness?.universal_id}');
          print('✓ Currency synced with universal_id: ${updatedCurrency?.universal_id}');
          print('✓ Product synced with universal_id: ${updatedProduct?.universal_id}');
          
        } catch (e) {
          fail('Failed to sync businesses, products, and currencies to Odoo: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 5)));
    });

    group('3. Odoo Server Verification Tests', () {
      test('should verify synced invoices exist in Odoo server', () async {
        print('\n🔍 Verifying synced invoices exist in Odoo server...');
        
        try {
          // First sync from Odoo to get existing invoices
          await syncNotifier.getSync(true);
          
          // Get synced invoices from local DB
          final localInvoices = await TestHelpers.testDatabase.accountMoveDao.getAllInvoices();
          final syncedInvoices = localInvoices.where((i) => i.is_synced == true && i.universal_id != null).toList();
          
          if (syncedInvoices.isNotEmpty) {
            // Verify a few invoices exist in Odoo
            final invoicesToCheck = syncedInvoices.take(3).toList();
            
            for (final invoice in invoicesToCheck) {
              final odooInvoices = await OdooClient.instance.searchRead(
                'account.move',
                [['id', '=', invoice.universal_id]],
                ['id', 'name', 'state', 'partner_id'],
              );
              
              expect(odooInvoices, isNotEmpty, 
                     reason: 'Invoice with universal_id ${invoice.universal_id} should exist in Odoo');
              
              final odooInvoice = odooInvoices.first;
              print('✓ Verified invoice ${invoice.name} exists in Odoo (ID: ${odooInvoice['id']}, State: ${odooInvoice['state']})');
            }
          } else {
            print('ℹ️ No synced invoices found to verify');
          }
          
        } catch (e) {
          fail('Failed to verify invoices in Odoo server: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 3)));
    });

    group('4. Update Sync Tests', () {
      test('should sync updates from Odoo to local DB', () async {
        print('\n🔄 Testing update sync from Odoo...');
        
        try {
          // First sync to get initial data
          await syncNotifier.getSync(true);
          
          // Get initial counts
          final initialBusinesses = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
          final initialProducts = await TestHelpers.testDatabase.productProductDao.getAllProducts();
          final initialCurrencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
          
          print('✓ Initial sync completed');
          print('  - Businesses: ${initialBusinesses.length}');
          print('  - Products: ${initialProducts.length}');
          print('  - Currencies: ${initialCurrencies.length}');
          
          // Wait a moment then sync again to test incremental updates
          await TestHelpers.waitForAsync(const Duration(seconds: 2));
          
          final updateSuccess = await syncNotifier.getSync(false);
          expect(updateSuccess, isTrue, reason: 'Update sync should succeed');
          
          // Get updated counts
          final updatedBusinesses = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
          final updatedProducts = await TestHelpers.testDatabase.productProductDao.getAllProducts();
          final updatedCurrencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
          
          print('✓ Update sync completed');
          print('  - Businesses: ${updatedBusinesses.length}');
          print('  - Products: ${updatedProducts.length}');
          print('  - Currencies: ${updatedCurrencies.length}');
          
          // Should not duplicate records
          expect(updatedBusinesses.length, equals(initialBusinesses.length),
                 reason: 'Update sync should not duplicate businesses');
          expect(updatedProducts.length, equals(initialProducts.length),
                 reason: 'Update sync should not duplicate products');
          expect(updatedCurrencies.length, equals(initialCurrencies.length),
                 reason: 'Update sync should not duplicate currencies');
          
        } catch (e) {
          fail('Failed to test update sync from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 5)));
    });
  });
}

/// Helper function to create a test invoice
Future<dynamic> _createTestInvoice(int partnerId) async {
  final db = TestHelpers.testDatabase;

  // Create invoice companion
  final invoiceCompanion = AccountMoveTableCompanion(
    name: Value('TEST-INV-${DateTime.now().millisecondsSinceEpoch}'),
    partner_id: Value(partnerId),
    move_type: const Value('out_invoice'),
    state: const Value('draft'),
    amount_total: const Value(100.0),
    amount_untaxed: const Value(100.0),
    amount_tax: const Value(0.0),
    is_synced: const Value(false),
    is_confirmed: const Value(true),
    is_deleted: const Value(false),
    version: const Value(1),
  );

  final invoiceId = await db.accountMoveDao.insertOrUpdateInvoice(invoiceCompanion);
  final invoice = await db.accountMoveDao.getInvoiceById(invoiceId);

  if (invoice == null) {
    throw Exception('Failed to create test invoice');
  }

  return invoice;
}

/// Helper function to verify invoice state in Odoo
Future<void> _verifyInvoiceStateInOdoo(int universalId, String expectedState) async {
  try {
    final odooInvoices = await OdooClient.instance.searchRead(
      'account.move',
      [['id', '=', universalId]],
      ['id', 'state'],
    );

    if (odooInvoices.isNotEmpty) {
      final actualState = odooInvoices.first['state'];
      if (actualState == expectedState) {
        print('✓ Invoice in Odoo has correct state: $actualState');
      } else {
        print('⚠️ Invoice state in Odoo: $actualState (expected: $expectedState)');
      }
    } else {
      print('⚠️ Invoice not found in Odoo with ID: $universalId');
    }
  } catch (e) {
    print('⚠️ Could not verify invoice state in Odoo: $e');
  }
}
