import 'package:flutter_test/flutter_test.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/sync/data/sync_repository.dart';
import 'package:invoicer/core/providers/sync/data/syncmaster.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Currency Deactivation Tests', () {
    late SyncRepository syncRepository;
    late SyncMaster syncMaster;

    setUpAll(() async {
      await TestHelpers.initializeTestEnvironment();
      syncRepository = SyncRepository();
      syncMaster = SyncMaster();
    });

    tearDownAll(() async {
      await TestHelpers.cleanupTestEnvironment();
    });

    test('should handle deactivated currencies during sync', () async {
      // First, create some test currencies in the local database
      final testCurrencies = [
        ResCurrencyTableCompanion.insert(
          name: 'USD',
          symbol: '\$',
          universal_id: const Value(1),
          is_synced: const Value(true),
          active: const Value(true),
        ),
        ResCurrencyTableCompanion.insert(
          name: 'EUR',
          symbol: '€',
          universal_id: const Value(2),
          is_synced: const Value(true),
          active: const Value(true),
        ),
        ResCurrencyTableCompanion.insert(
          name: 'ZIG',
          symbol: 'ZIG',
          universal_id: const Value(3),
          is_synced: const Value(true),
          active: const Value(true),
        ),
      ];

      // Save test currencies to database
      for (final currency in testCurrencies) {
        await TestHelpers.testDatabase.resCurrencyDao.insertOrUpdateCurrency(currency);
      }

      // Verify all currencies are initially active
      final initialCurrencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
      expect(initialCurrencies.length, equals(3));
      expect(initialCurrencies.where((c) => c.is_deleted).length, equals(0));

      // Simulate Odoo response with ZIG currency deactivated
      final mockOdooCurrencies = [
        {
          'id': 1,
          'name': 'USD',
          'symbol': '\$',
          'active': true,
          'rate': 1.0,
        },
        {
          'id': 2,
          'name': 'EUR',
          'symbol': '€',
          'active': true,
          'rate': 0.85,
        },
        {
          'id': 3,
          'name': 'ZIG',
          'symbol': 'ZIG',
          'active': false, // This currency is deactivated in Odoo
          'rate': 0.001,
        },
      ];

      // Test the deactivation handler directly
      await syncMaster.handleDeactivatedCurrencies(mockOdooCurrencies);

      // Verify that ZIG currency is marked as deleted
      final updatedCurrencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
      final zigCurrency = updatedCurrencies.firstWhere((c) => c.name == 'ZIG');
      
      expect(zigCurrency.is_deleted, isTrue, reason: 'ZIG currency should be marked as deleted');
      expect(zigCurrency.active, isFalse, reason: 'ZIG currency should be marked as inactive');

      // Verify that other currencies remain active
      final usdCurrency = updatedCurrencies.firstWhere((c) => c.name == 'USD');
      final eurCurrency = updatedCurrencies.firstWhere((c) => c.name == 'EUR');
      
      expect(usdCurrency.is_deleted, isFalse, reason: 'USD currency should remain active');
      expect(eurCurrency.is_deleted, isFalse, reason: 'EUR currency should remain active');

      print('✓ Currency deactivation test passed');
    });

    test('should filter out deleted currencies from getAllCurrencies', () async {
      // Get currencies using the updated getAllCurrencies method
      final activeCurrencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
      
      // Should only return non-deleted currencies (USD and EUR)
      expect(activeCurrencies.length, equals(2));
      expect(activeCurrencies.any((c) => c.name == 'ZIG'), isFalse, 
             reason: 'ZIG currency should not be returned as it is deleted');
      expect(activeCurrencies.any((c) => c.name == 'USD'), isTrue,
             reason: 'USD currency should be returned');
      expect(activeCurrencies.any((c) => c.name == 'EUR'), isTrue,
             reason: 'EUR currency should be returned');

      print('✓ Currency filtering test passed');
    });

    test('should return only active currencies from getActiveCurrencies', () async {
      // Get only active currencies
      final activeCurrencies = await TestHelpers.testDatabase.resCurrencyDao.getActiveCurrencies();
      
      // Should only return currencies that are both not deleted and active
      expect(activeCurrencies.length, equals(2));
      expect(activeCurrencies.every((c) => !c.is_deleted && c.active == true), isTrue,
             reason: 'All returned currencies should be active and not deleted');

      print('✓ Active currencies filtering test passed');
    });
  });
}
