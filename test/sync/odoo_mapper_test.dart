import 'package:flutter_test/flutter_test.dart';
import 'package:invoicer/core/utils/odoo_mapper.dart';
import 'package:invoicer/core/models/drift/DriftSyncWrappers.dart';
import 'package:invoicer/core/db/drift/database.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('OdooMapper Tests', () {
    setUp(() async {
      await TestHelpers.setUp();
      await TestHelpers.clearTestData();
    });
    
    tearDown(() async {
      await TestHelpers.tearDown();
    });
    
    group('Model Mapping Tests', () {
      test('should have correct model mappings', () {
        expect(OdooMapper.modelMapping['businesses'], equals('res.company'));
        expect(OdooMapper.modelMapping['clients'], equals('res.partner'));
        expect(OdooMapper.modelMapping['categories'], equals('product.category'));
        expect(OdooMapper.modelMapping['products'], equals('product.product'));
        expect(OdooMapper.modelMapping['invoices'], equals('account.move'));
        expect(OdooMapper.modelMapping['currencies'], equals('res.currency'));
        
        print('All model mappings are correct');
      });
    });
    
    group('To Odoo Model Conversion Tests', () {
      test('should convert company to Odoo format', () async {
        final company = await TestHelpers.createTestCompany(
          name: 'Test Company',
          universalId: 123,
          isSynced: true,
        );
        
        final companySync = ResCompanySync(company);
        final odooData = await OdooMapper.toOdooModel('businesses', companySync);
        
        expect(odooData, isA<Map<String, dynamic>>());
        expect(odooData['name'], equals('Test Company'));
        expect(odooData['id'], equals(123)); // Should use universal_id
        expect(odooData['is_synced'], isTrue);
        
        // Verify app-specific fields are removed
        expect(odooData.containsKey('favicon'), isFalse);
        expect(odooData.containsKey('prefix'), isFalse);
        expect(odooData.containsKey('last_used_id'), isFalse);
        expect(odooData.containsKey('payment_info'), isFalse);
        expect(odooData.containsKey('logo_web'), isFalse);
        expect(odooData.containsKey('currency_symbol'), isFalse);
        expect(odooData.containsKey('quick_edit_mode'), isFalse);
        expect(odooData.containsKey('tax_rate'), isFalse);
        expect(odooData.containsKey('is_order'), isFalse);
        
        print('Company converted to Odoo format successfully');
      });
      
      test('should convert partner to Odoo format', () async {
        final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        final partner = await TestHelpers.createTestPartner(
          name: 'Test Partner',
          email: '<EMAIL>',
          companyId: company.id,
          universalId: 456,
          isSynced: true,
        );
        
        final partnerSync = ResPartnerSync(partner);
        final odooData = await OdooMapper.toOdooModel('clients', partnerSync);
        
        expect(odooData, isA<Map<String, dynamic>>());
        expect(odooData['name'], equals('Test Partner'));
        expect(odooData['email'], equals('<EMAIL>'));
        expect(odooData['id'], equals(456)); // Should use universal_id
        expect(odooData['company_id'], equals(1)); // Should map to Odoo company_id
        
        print('Partner converted to Odoo format successfully');
      });
      
      test('should convert product to Odoo format', () async {
        final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        final category = await TestHelpers.createTestCategory(universalId: 2, isSynced: true);
        final product = await TestHelpers.createTestProduct(
          name: 'Test Product',
          price: 99.99,
          companyId: company.id,
          categoryId: category.id,
          universalId: 789,
          isSynced: true,
        );
        
        final productSync = ProductProductSync(product);
        final odooData = await OdooMapper.toOdooModel('products', productSync);
        
        expect(odooData, isA<Map<String, dynamic>>());
        expect(odooData['name'], equals('Test Product'));
        expect(odooData['list_price'], equals(99.99));
        expect(odooData['id'], equals(789)); // Should use universal_id
        expect(odooData['company_id'], equals(1)); // Should map to Odoo company_id
        
        print('Product converted to Odoo format successfully');
      });
      
      test('should convert currency to Odoo format', () async {
        final currency = await TestHelpers.createTestCurrency(
          name: 'USD',
          symbol: '\$',
          rate: 1.0,
          universalId: 101,
          isSynced: true,
        );
        
        final currencySync = ResCurrencySync(currency);
        final odooData = await OdooMapper.toOdooModel('currencies', currencySync);
        
        expect(odooData, isA<Map<String, dynamic>>());
        expect(odooData['name'], equals('USD'));
        expect(odooData['symbol'], equals('\$'));
        expect(odooData['rate'], equals(1.0));
        expect(odooData['id'], equals(101)); // Should use universal_id
        
        print('Currency converted to Odoo format successfully');
      });
      
      test('should convert category to Odoo format', () async {
        final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        final category = await TestHelpers.createTestCategory(
          name: 'Test Category',
          companyId: company.id,
          universalId: 202,
          isSynced: true,
        );
        
        final categorySync = ProductCategorySync(category);
        final odooData = await OdooMapper.toOdooModel('categories', categorySync);
        
        expect(odooData, isA<Map<String, dynamic>>());
        expect(odooData['name'], equals('Test Category'));
        expect(odooData['id'], equals(202)); // Should use universal_id
        expect(odooData['company_id'], equals(1)); // Should map to Odoo company_id
        
        print('Category converted to Odoo format successfully');
      });
      
      test('should handle ID field conversion for Odoo', () async {
        final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        final partner = await TestHelpers.createTestPartner(
          companyId: company.id,
          universalId: 456,
          isSynced: true,
        );
        
        final partnerSync = ResPartnerSync(partner);
        final odooData = await OdooMapper.toOdooModel('clients', partnerSync);
        
        // Check that company_id is converted to the universal_id format expected by Odoo
        expect(odooData['company_id'], equals(1));
        
        print('ID field conversion handled correctly');
      });
    });
    
    group('From Odoo Model Conversion Tests', () {
      test('should convert Odoo company to app model', () {
        final odooData = {
          'id': 123,
          'name': 'Odoo Test Company',
          'email': '<EMAIL>',
          'phone': '+1234567890',
          'website': 'https://odoo.com',
          'currency_id': [1, 'USD'],
          'country_id': [1, 'United States'],
        };
        
        final appModel = OdooMapper.fromOdooModel('businesses', odooData);
        
        expect(appModel, isA<ResCompanySync>());
        final company = (appModel as ResCompanySync).company;
        expect(company.name, equals('Odoo Test Company'));
        expect(company.email, equals('<EMAIL>'));
        expect(company.phone, equals('+1234567890'));
        expect(company.website, equals('https://odoo.com'));
        expect(company.universal_id, equals(123));
        
        print('Odoo company converted to app model successfully');
      });
      
      test('should convert Odoo partner to app model', () {
        final odooData = {
          'id': 456,
          'name': 'Odoo Test Partner',
          'email': '<EMAIL>',
          'phone': '+0987654321',
          'is_company': false,
          'customer_rank': 1,
          'supplier_rank': 0,
          'company_id': [1, 'Test Company'],
        };
        
        final appModel = OdooMapper.fromOdooModel('clients', odooData);
        
        expect(appModel, isA<ResPartnerSync>());
        final partner = (appModel as ResPartnerSync).partner;
        expect(partner.name, equals('Odoo Test Partner'));
        expect(partner.email, equals('<EMAIL>'));
        expect(partner.phone, equals('+0987654321'));
        expect(partner.universal_id, equals(456));
        expect(partner.customer_rank, equals(1));
        
        print('Odoo partner converted to app model successfully');
      });
      
      test('should convert Odoo product to app model', () {
        final odooData = {
          'id': 789,
          'name': 'Odoo Test Product',
          'list_price': 199.99,
          'standard_price': 150.0,
          'categ_id': [1, 'Test Category'],
          'company_id': [1, 'Test Company'],
          'active': true,
          'sale_ok': true,
          'purchase_ok': true,
        };
        
        final appModel = OdooMapper.fromOdooModel('products', odooData);
        
        expect(appModel, isA<ProductProductSync>());
        final product = (appModel as ProductProductSync).product;
        expect(product.name, equals('Odoo Test Product'));
        expect(product.list_price, equals(199.99));
        expect(product.standard_price, equals(150.0));
        expect(product.universal_id, equals(789));
        expect(product.active, isTrue);
        
        print('Odoo product converted to app model successfully');
      });
      
      test('should convert Odoo currency to app model', () {
        final odooData = {
          'id': 101,
          'name': 'EUR',
          'symbol': '€',
          'rate': 0.85,
          'active': true,
          'position': 'after',
        };
        
        final appModel = OdooMapper.fromOdooModel('currencies', odooData);
        
        expect(appModel, isA<ResCurrencySync>());
        final currency = (appModel as ResCurrencySync).currency;
        expect(currency.name, equals('EUR'));
        expect(currency.symbol, equals('€'));
        expect(currency.rate, equals(0.85));
        expect(currency.universal_id, equals(101));
        expect(currency.active, isTrue);
        
        print('Odoo currency converted to app model successfully');
      });
      
      test('should convert Odoo category to app model', () {
        final odooData = {
          'id': 202,
          'name': 'Odoo Test Category',
          'parent_id': false,
          'company_id': [1, 'Test Company'],
        };
        
        final appModel = OdooMapper.fromOdooModel('categories', odooData);
        
        expect(appModel, isA<ProductCategorySync>());
        final category = (appModel as ProductCategorySync).category;
        expect(category.name, equals('Odoo Test Category'));
        expect(category.universal_id, equals(202));
        
        print('Odoo category converted to app model successfully');
      });
      
      test('should handle Odoo array fields correctly', () {
        final odooData = {
          'id': 123,
          'name': 'Test Company',
          'currency_id': [1, 'USD'],
          'country_id': [233, 'United States'],
          'state_id': [5, 'California'],
        };
        
        final appModel = OdooMapper.fromOdooModel('businesses', odooData);
        
        expect(appModel, isA<ResCompanySync>());
        final company = (appModel as ResCompanySync).company;
        expect(company.currency_id, equals(1)); // Should extract ID from array
        expect(company.country_id, equals(233));
        expect(company.state_id, equals(5));
        
        print('Odoo array fields converted correctly');
      });
      
      test('should handle missing fields gracefully', () {
        final odooData = {
          'id': 123,
          'name': 'Minimal Company',
          // Missing optional fields
        };
        
        final appModel = OdooMapper.fromOdooModel('businesses', odooData);
        
        expect(appModel, isA<ResCompanySync>());
        final company = (appModel as ResCompanySync).company;
        expect(company.name, equals('Minimal Company'));
        expect(company.universal_id, equals(123));
        
        print('Missing fields handled gracefully');
      });
    });
    
    group('Error Handling Tests', () {
      test('should throw exception for unknown entity type in fromOdooModel', () {
        final odooData = {'id': 1, 'name': 'Test'};
        
        expect(
          () => OdooMapper.fromOdooModel('unknown_type', odooData),
          throwsA(isA<Exception>()),
        );
      });
      
      test('should handle malformed Odoo data gracefully', () {
        final malformedData = {
          'id': 'not_a_number', // Invalid ID type
          'name': null, // Null name
        };
        
        expect(
          () => OdooMapper.fromOdooModel('businesses', malformedData),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}
