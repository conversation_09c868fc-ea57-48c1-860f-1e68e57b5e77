import 'package:mockito/mockito.dart';
import 'package:invoicer/core/network/odoo_client.dart';

/// Mock implementation of OdooClient for testing
class MockOdooClient extends Mock implements OdooClient {
  // Mock data storage
  final Map<String, List<Map<String, dynamic>>> _mockData = {};
  final Map<String, int> _nextIds = {};
  
  /// Set up mock data for a specific model
  void setMockData(String model, List<Map<String, dynamic>> data) {
    _mockData[model] = data;
    _nextIds[model] = data.isEmpty ? 1 : data.map((e) => e['id'] as int).reduce((a, b) => a > b ? a : b) + 1;
  }
  
  /// Clear all mock data
  void clearMockData() {
    _mockData.clear();
    _nextIds.clear();
  }
  
  @override
  Future<dynamic> executeKw({
    required String model,
    required String method,
    required List args,
    Map<String, dynamic>? kwargs,
    bool ignoreAuthToken = false,
  }) async {
    switch (method) {
      case 'search_read':
        return _handleSearchRead(model, args, kwargs);
      case 'create':
        return _handleCreate(model, args);
      case 'write':
        return _handleWrite(model, args);
      case 'read':
        return _handleRead(model, args);
      case 'search':
        return _handleSearch(model, args, kwargs);
      default:
        throw Exception('Unsupported method: $method');
    }
  }
  
  @override
  Future<int> create(String model, Map<String, dynamic> values) async {
    final nextId = _nextIds[model] ?? 1;
    _nextIds[model] = nextId + 1;
    
    final newRecord = Map<String, dynamic>.from(values);
    newRecord['id'] = nextId;
    
    _mockData[model] ??= [];
    _mockData[model]!.add(newRecord);
    
    return nextId;
  }
  
  @override
  Future<List<Map<String, dynamic>>> read(
    String model,
    List<int> ids,
    List<String> fields
  ) async {
    final modelData = _mockData[model] ?? [];
    return modelData
        .where((record) => ids.contains(record['id']))
        .map((record) {
          final filteredRecord = <String, dynamic>{};
          for (final field in fields) {
            if (record.containsKey(field)) {
              filteredRecord[field] = record[field];
            }
          }
          return filteredRecord;
        })
        .toList();
  }
  
  @override
  Future<bool> write(String model, List<int> ids, Map<String, dynamic> values) async {
    final modelData = _mockData[model] ?? [];
    for (final record in modelData) {
      if (ids.contains(record['id'])) {
        record.addAll(values);
      }
    }
    return true;
  }
  
  @override
  Future<List<int>> search(
    String model,
    List<dynamic> domain, {
    int? offset,
    int? limit,
    String? order,
  }) async {
    final modelData = _mockData[model] ?? [];
    // Simple implementation - return all IDs for now
    // In a real implementation, you'd parse the domain
    return modelData.map((record) => record['id'] as int).toList();
  }
  
  @override
  Future<List<Map<String, dynamic>>> searchRead(
    String model,
    List<dynamic> domain,
    List<String> fields, {
    int? offset,
    int? limit,
    String? order,
  }) async {
    final modelData = _mockData[model] ?? [];
    // Simple implementation - return all records for now
    // In a real implementation, you'd parse the domain and apply filters
    return modelData.map((record) {
      final filteredRecord = <String, dynamic>{};
      for (final field in fields) {
        if (record.containsKey(field)) {
          filteredRecord[field] = record[field];
        }
      }
      return filteredRecord;
    }).toList();
  }
  
  // Private helper methods
  List<Map<String, dynamic>> _handleSearchRead(String model, List args, Map<String, dynamic>? kwargs) {
    final domain = args.isNotEmpty ? args[0] as List : [];
    final fields = args.length > 1 ? args[1] as List<String> : [];
    
    final modelData = _mockData[model] ?? [];
    return modelData.map((record) {
      if (fields.isEmpty) return record;
      
      final filteredRecord = <String, dynamic>{};
      for (final field in fields) {
        if (record.containsKey(field)) {
          filteredRecord[field] = record[field];
        }
      }
      return filteredRecord;
    }).toList();
  }
  
  int _handleCreate(String model, List args) {
    if (args.isEmpty) throw Exception('No data provided for create');
    
    final values = args[0] as Map<String, dynamic>;
    final nextId = _nextIds[model] ?? 1;
    _nextIds[model] = nextId + 1;
    
    final newRecord = Map<String, dynamic>.from(values);
    newRecord['id'] = nextId;
    
    _mockData[model] ??= [];
    _mockData[model]!.add(newRecord);
    
    return nextId;
  }
  
  bool _handleWrite(String model, List args) {
    if (args.length < 2) throw Exception('Insufficient arguments for write');
    
    final ids = args[0] as List<int>;
    final values = args[1] as Map<String, dynamic>;
    
    final modelData = _mockData[model] ?? [];
    for (final record in modelData) {
      if (ids.contains(record['id'])) {
        record.addAll(values);
      }
    }
    
    return true;
  }
  
  List<Map<String, dynamic>> _handleRead(String model, List args) {
    if (args.isEmpty) throw Exception('No IDs provided for read');
    
    final ids = args[0] as List<int>;
    final fields = args.length > 1 ? args[1] as List<String> : [];
    
    final modelData = _mockData[model] ?? [];
    return modelData
        .where((record) => ids.contains(record['id']))
        .map((record) {
          if (fields.isEmpty) return record;
          
          final filteredRecord = <String, dynamic>{};
          for (final field in fields) {
            if (record.containsKey(field)) {
              filteredRecord[field] = record[field];
            }
          }
          return filteredRecord;
        })
        .toList();
  }
  
  List<int> _handleSearch(String model, List args, Map<String, dynamic>? kwargs) {
    final modelData = _mockData[model] ?? [];
    // Simple implementation - return all IDs for now
    return modelData.map((record) => record['id'] as int).toList();
  }
}

/// Factory for creating mock Odoo data
class MockOdooDataFactory {
  /// Create mock company data
  static List<Map<String, dynamic>> createMockCompanies() {
    return [
      {
        'id': 1,
        'name': 'Test Company 1',
        'email': '<EMAIL>',
        'phone': '+1234567890',
        'website': 'https://test1.com',
        'currency_id': [1, 'USD'],
        'country_id': [1, 'United States'],
      },
      {
        'id': 2,
        'name': 'Test Company 2',
        'email': '<EMAIL>',
        'phone': '+0987654321',
        'website': 'https://test2.com',
        'currency_id': [2, 'EUR'],
        'country_id': [2, 'Germany'],
      },
    ];
  }
  
  /// Create mock partner data
  static List<Map<String, dynamic>> createMockPartners() {
    return [
      {
        'id': 1,
        'name': 'Test Customer 1',
        'email': '<EMAIL>',
        'phone': '+1111111111',
        'is_company': false,
        'customer_rank': 1,
        'supplier_rank': 0,
        'company_id': [1, 'Test Company 1'],
      },
      {
        'id': 2,
        'name': 'Test Supplier 1',
        'email': '<EMAIL>',
        'phone': '+2222222222',
        'is_company': true,
        'customer_rank': 0,
        'supplier_rank': 1,
        'company_id': [1, 'Test Company 1'],
      },
    ];
  }
  
  /// Create mock product data
  static List<Map<String, dynamic>> createMockProducts() {
    return [
      {
        'id': 1,
        'name': 'Test Product 1',
        'list_price': 100.0,
        'standard_price': 80.0,
        'categ_id': [1, 'Test Category'],
        'company_id': [1, 'Test Company 1'],
        'active': true,
        'sale_ok': true,
        'purchase_ok': true,
      },
      {
        'id': 2,
        'name': 'Test Product 2',
        'list_price': 200.0,
        'standard_price': 150.0,
        'categ_id': [1, 'Test Category'],
        'company_id': [1, 'Test Company 1'],
        'active': true,
        'sale_ok': true,
        'purchase_ok': false,
      },
    ];
  }
  
  /// Create mock currency data
  static List<Map<String, dynamic>> createMockCurrencies() {
    return [
      {
        'id': 1,
        'name': 'USD',
        'symbol': '\$',
        'rate': 1.0,
        'active': true,
        'position': 'before',
      },
      {
        'id': 2,
        'name': 'EUR',
        'symbol': '€',
        'rate': 0.85,
        'active': true,
        'position': 'after',
      },
    ];
  }
  
  /// Create mock category data
  static List<Map<String, dynamic>> createMockCategories() {
    return [
      {
        'id': 1,
        'name': 'Test Category',
        'parent_id': false,
        'company_id': [1, 'Test Company 1'],
      },
      {
        'id': 2,
        'name': 'Sub Category',
        'parent_id': [1, 'Test Category'],
        'company_id': [1, 'Test Company 1'],
      },
    ];
  }
}
