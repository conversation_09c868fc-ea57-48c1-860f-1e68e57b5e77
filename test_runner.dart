#!/usr/bin/env dart

/// Test runner script for Odoo sync tests
/// 
/// This script can be run directly with: dart test_runner.dart
/// Or you can run specific test suites:
/// - dart test_runner.dart --smoke (for smoke tests only)
/// - dart test_runner.dart --unit (for unit tests only)
/// - dart test_runner.dart --integration (for integration tests only)

import 'dart:io';
import 'dart:async';

void main(List<String> args) async {
  print('🚀 Odoo Sync Test Runner');
  print('=' * 60);
  
  final testType = args.isNotEmpty ? args[0] : '--all';
  
  switch (testType) {
    case '--smoke':
      await runSmokeTests();
      break;
    case '--unit':
      await runUnitTests();
      break;
    case '--integration':
      await runIntegrationTests();
      break;
    case '--all':
    default:
      await runAllTests();
      break;
  }
}

Future<void> runSmokeTests() async {
  print('🔥 Running Smoke Tests...');
  await runTest('test/sync/sync_test_suite.dart', 'runSmokeTests');
}

Future<void> runUnitTests() async {
  print('🧪 Running Unit Tests...');
  final tests = [
    'test/sync/odoo_client_test.dart',
    'test/sync/odoo_mapper_test.dart',
    'test/sync/sync_repository_test.dart',
  ];
  
  for (final test in tests) {
    await runTest(test);
  }
}

Future<void> runIntegrationTests() async {
  print('🌐 Running Integration Tests...');
  await runTest('test/sync/integration_test.dart');
}

Future<void> runAllTests() async {
  print('🎯 Running All Tests...');
  await runTest('test/sync/sync_test_suite.dart');
}

Future<void> runTest(String testFile, [String? function]) async {
  print('\n📋 Running: $testFile');
  print('-' * 40);
  
  try {
    final result = await Process.run(
      'flutter',
      ['test', testFile],
      workingDirectory: Directory.current.path,
    );
    
    if (result.exitCode == 0) {
      print('✅ PASSED: $testFile');
      if (result.stdout.toString().isNotEmpty) {
        print(result.stdout);
      }
    } else {
      print('❌ FAILED: $testFile');
      print('Exit code: ${result.exitCode}');
      if (result.stderr.toString().isNotEmpty) {
        print('Error: ${result.stderr}');
      }
      if (result.stdout.toString().isNotEmpty) {
        print('Output: ${result.stdout}');
      }
    }
  } catch (e) {
    print('💥 ERROR running $testFile: $e');
    
    // Try alternative approach with dart test
    try {
      print('🔄 Trying with dart test...');
      final result = await Process.run(
        'dart',
        ['test', testFile],
        workingDirectory: Directory.current.path,
      );
      
      if (result.exitCode == 0) {
        print('✅ PASSED: $testFile (with dart test)');
        if (result.stdout.toString().isNotEmpty) {
          print(result.stdout);
        }
      } else {
        print('❌ FAILED: $testFile (with dart test)');
        print('Exit code: ${result.exitCode}');
        if (result.stderr.toString().isNotEmpty) {
          print('Error: ${result.stderr}');
        }
      }
    } catch (e2) {
      print('💥 ERROR with dart test: $e2');
    }
  }
}

/// Manual test execution instructions
void printManualInstructions() {
  print('''
📖 Manual Test Execution Instructions:

If the automated test runner doesn't work, you can run tests manually:

1. Install dependencies:
   flutter pub get

2. Run individual test files:
   flutter test test/sync/odoo_client_test.dart
   flutter test test/sync/odoo_mapper_test.dart
   flutter test test/sync/sync_repository_test.dart
   flutter test test/sync/integration_test.dart

3. Run all sync tests:
   flutter test test/sync/

4. Run specific test groups:
   flutter test test/sync/odoo_client_test.dart --name "Authentication Tests"
   flutter test test/sync/integration_test.dart --name "Real Odoo Server Integration"

5. Run with verbose output:
   flutter test test/sync/ --verbose

6. Test with real Odoo server credentials:
   Make sure the credentials in test_helpers.dart are correct:
   - URL: https://erp.kanjan.co.zw
   - Database: piggypro
   - Username: <EMAIL>
   - Password: Secret1234

⚠️  Note: Integration tests require internet connection and valid Odoo server access.
''');
}
