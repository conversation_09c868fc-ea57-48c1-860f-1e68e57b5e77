name: invoicer
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+4

environment:
  sdk: ">=3.0.0 <=3.9.9"

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_svg: any
  freezed_annotation: ^3.0.0
  provider: ^6.1.4
  relative_scale: ^2.0.0
  logger: ^2.5.0
  csv: any
  file_picker: ^10.1.2
  flutter_riverpod: ^2.4.0
  shared_preferences: ^2.0.6
  fl_chart: ^0.70.2
  syncfusion_flutter_pdf: any
  flutter_icons:
    git:
      url: https://github.com/adarsh-technocrat/flutter-icons.git
      branch: null-safety-migration
  json_annotation: ^4.9.0  # Replace 'any' with '^4.9.0' to match json_serializable
  intl: ^0.17.0
  dio: ^5.1.1
  table_calendar: ^3.0.0
  flutter_screenutil: ^5.0.0+2
  flutter_charts: ^0.5.2
  smooth_page_indicator: ^1.2.1
  colorize_text_avatar: ^1.0.2
  data_table_2: ^2.0.3
  docx_template: ^0.4.0
  # Database
#  sqflite: ^2.2.4+1
  path_provider: ^2.0.12
#  sqflite_common_ffi: ^2.2.1+1

#  drift: ^2.26.0
  sqlite3_flutter_libs: ^0.5.20
  syncfusion_flutter_datepicker: ^21.2.3
  flutter_datetime_picker: ^1.5.1
  image_picker: ^1.1.2
  syncfusion_localizations: any
  desktop_window: ^0.4.0
  url_launcher: ^6.1.11
  permission_handler: ^12.0.0+1
  share_plus: ^10.1.4
  flutter_colorpicker: ^1.0.3
  printing: ^5.14.2
  flutter_image_compress: any
  ndialog: any

  pdf: any
  go_router: ^14.8.1
  charts_flutter: any
  flutter_blue_plus: any
  esc_pos_utils_plus:
    path: ./esc_pos_utils_plus
  open_filex: ^4.7.0
  drift: ^2.26.0
  drift_flutter: ^0.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.15
  freezed: ^3.0.6
  json_serializable: ^6.3.1
  file_selector: ^1.0.3
  flutter_launcher_icons: any
  drift_dev: ^2.26.0
  mockito: ^5.4.4
  http_mock_adapter: ^0.6.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/templates/
    - assets/slides/
    - assets/logo/
    - assets/fonts/Roboto/
    - assets/jsons/
    - assets/animations/
  #    - lib/services/
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/icons/icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/icons/icon.png"

