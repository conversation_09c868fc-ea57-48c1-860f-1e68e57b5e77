# Currency Synchronization Fix

## Problem
When currencies are deactivated in Odoo, they continue to appear in the local database even after synchronization. This happens because the sync process only fetches active currencies from Odoo and doesn't handle cleanup of deactivated currencies.

## Root Cause
1. **Sync only fetches active currencies**: The sync process used `domain = [['active', '=', true]]` to only fetch active currencies from Odoo.
2. **No cleanup mechanism**: There was no logic to handle currencies that were deactivated in Odoo.
3. **Local persistence**: Previously synced currencies remained in the local database indefinitely.

## Solution
The fix implements a comprehensive currency synchronization mechanism:

### 1. Fetch All Currencies During Sync
- **File**: `lib/core/providers/sync/data/syncmaster.dart`
- **Change**: Modified the domain filter for currencies from `[['active', '=', true]]` to `[]` to fetch all currencies (both active and inactive).
- **Line**: 276

### 2. Post-Sync Cleanup Process
- **File**: `lib/core/providers/sync/data/syncmaster.dart`
- **Addition**: Added `handleDeactivatedCurrencies()` method that runs after currency sync.
- **Logic**: 
  - Compares local synced currencies with Odoo currencies
  - Identifies currencies that are inactive in Odoo
  - Marks them as deleted (`is_deleted = true`) and inactive (`active = false`) in local database

### 3. Database Query Filtering
- **File**: `lib/core/db/drift/database.dart`
- **Change**: Updated `getAllCurrencies()` to exclude deleted currencies
- **Addition**: Added `getActiveCurrencies()` method to get only active, non-deleted currencies

### 4. Repository Enhancement
- **File**: `lib/core/repositories/drift/res_currency_repository_drift.dart`
- **Addition**: Added `getActive()` method to expose active currencies filtering

### 5. Provider Updates
- **File**: `lib/core/providers/currency/currency_provider.dart`
- **Addition**: Added `activeCurrenciesProvider` for UI components that need only active currencies

## Implementation Details

### Currency Deactivation Handler
```dart
Future<void> handleDeactivatedCurrencies(List<Map<String, dynamic>> odooCurrencies) async {
  // Get all local synced currencies
  final localCurrencies = await db.resCurrencyDao.getAllCurrencies();
  final syncedLocalCurrencies = localCurrencies.where((c) => c.universal_id != null && c.is_synced).toList();
  
  // Create set of active Odoo currency IDs
  final activeCurrencyIds = odooCurrencies
      .where((currency) => currency['active'] == true)
      .map((currency) => currency['id'] as int)
      .toSet();
  
  // Find currencies to deactivate
  final currenciesToDeactivate = syncedLocalCurrencies.where((localCurrency) {
    return localCurrency.universal_id != null && 
           !activeCurrencyIds.contains(localCurrency.universal_id);
  }).toList();
  
  // Mark as deleted and inactive
  for (final currency in currenciesToDeactivate) {
    final companion = ResCurrencyTableCompanion(
      id: Value(currency.id),
      is_deleted: const Value(true),
      active: const Value(false),
    );
    await db.resCurrencyDao.insertOrUpdateCurrency(companion);
  }
}
```

### Database Filtering
```dart
// Get all currencies (excluding deleted ones)
Future<List<ResCurrencyTableData>> getAllCurrencies() {
  return (select(resCurrencyTable)
    ..where((tbl) => tbl.is_deleted.equals(false)))
    .get();
}

// Get active currencies (excluding deleted and inactive ones)
Future<List<ResCurrencyTableData>> getActiveCurrencies() {
  return (select(resCurrencyTable)
    ..where((tbl) => tbl.is_deleted.equals(false) & tbl.active.equals(true)))
    .get();
}
```

## Testing
A comprehensive test suite has been created in `test/sync/currency_deactivation_test.dart` that:

1. **Tests deactivation logic**: Verifies that currencies deactivated in Odoo are marked as deleted locally
2. **Tests filtering**: Ensures deleted currencies are excluded from `getAllCurrencies()`
3. **Tests active filtering**: Verifies `getActiveCurrencies()` returns only active, non-deleted currencies

### Running Tests
```bash
flutter test test/sync/currency_deactivation_test.dart
```

## Usage

### For UI Components
Use the appropriate provider based on your needs:

```dart
// Get all non-deleted currencies (includes inactive ones)
final currencies = ref.watch(currenciesProvider);

// Get only active currencies (recommended for dropdowns/selectors)
final activeCurrencies = ref.watch(activeCurrenciesProvider);
```

### For Repository Access
```dart
// Get all non-deleted currencies
final allCurrencies = await currencyRepository.getAll();

// Get only active currencies
final activeCurrencies = await currencyRepository.getActive();
```

## Migration Notes
- **Existing data**: Previously deactivated currencies will be cleaned up on the next sync
- **Backward compatibility**: The changes are backward compatible with existing code
- **Performance**: Minimal performance impact as filtering is done at the database level

## Verification Steps
1. Deactivate a currency in Odoo (e.g., set ZIG currency to inactive)
2. Run sync in the app
3. Verify the deactivated currency no longer appears in currency lists
4. Check database to confirm the currency is marked as `is_deleted = true`

This fix ensures that the local currency list stays synchronized with Odoo's active currencies, providing a consistent user experience.
