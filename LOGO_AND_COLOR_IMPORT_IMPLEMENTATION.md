# Logo and Company Color Import from Odoo - Implementation Summary

## Overview
This implementation adds functionality to import company logos and colors from Odoo and apply them to invoice templates in the invoicing app.

## Features Implemented

### 1. Logo Download from Odoo
- **Location**: `lib/core/providers/auth/data/auth_repository.dart` and `lib/core/providers/sync/data/syncmaster.dart`
- **Functionality**: 
  - Downloads company logos from Odoo as base64 encoded data
  - Saves logos locally with filename pattern: `company_{company_id}.png`
  - Updates company records with local logo filename
  - Integrates with existing sync process

### 2. Company Color Synchronization
- **Location**: Already included in sync fields in `lib/core/providers/sync/data/syncmaster.dart`
- **Functionality**:
  - Company colors are already synced from Odoo's `res.company.color` field
  - Colors are stored as integer values in the local database

### 3. Invoice Template Color Integration
- **Modified Templates**:
  - `lib/screens/invoice/print/templates/invoice5.dart`
  - `lib/screens/invoice/print/templates/invoice_report.dart`
  - `lib/screens/invoice/print/templates/orders_report.dart`
- **Functionality**:
  - Retrieves company color from database during invoice generation
  - Converts integer color to RGB components
  - Creates PDF color objects for main, accent, and light accent colors
  - Falls back to default colors if no company color is set

## Technical Implementation Details

### Logo Download Process
1. **Trigger**: Automatically called after sync completion
2. **API Call**: Uses Odoo JSON-RPC to read `logo` field from `res.company`
3. **Storage**: Saves to local download directory with unique filename
4. **Database Update**: Updates company record with local logo path

### Color Application Process
1. **Retrieval**: Gets company color from database during invoice generation
2. **Conversion**: Converts integer color (0xRRGGBB) to RGB components
3. **PDF Colors**: Creates PdfColor objects with 0-1 range values
4. **Accent Colors**: Generates darker and lighter variants automatically

### Code Structure
```
lib/core/providers/
├── auth/data/auth_repository.dart          # Logo download methods
├── sync/data/syncmaster.dart               # Logo download implementation
└── sync/sync_state_notifier.dart           # Sync completion trigger

lib/screens/invoice/print/templates/
├── invoice5.dart                           # Main invoice template with color support
├── invoice_report.dart                     # Report template with color support
└── orders_report.dart                      # Orders template with color support
```

## Key Methods Added

### SyncMaster Class
- `downloadLogos(List<ResCompanyTableData> companies)` - Main logo download method
- `_downloadCompanyLogoFromOdoo(int companyId)` - Downloads logo from Odoo API
- `_saveLogoToLocal(List<int> logoBytes, int companyId)` - Saves logo to local storage
- `_updateCompanyLogo(int companyId, String logoFileName)` - Updates database record

### Invoice Templates
- Company color retrieval and RGB conversion logic
- Automatic accent color generation
- Fallback to default colors when company color is not available

## Usage
1. **Automatic**: Logos are downloaded automatically during sync process
2. **Invoice Generation**: Colors are applied automatically when generating invoices
3. **Fallback**: Default colors are used if company color is not set

## Testing
- Basic test structure created in `test/logo_import_test.dart`
- Tests color conversion logic
- Tests handling of missing company colors

## Benefits
1. **Brand Consistency**: Invoices automatically use company branding
2. **Automatic Sync**: No manual intervention required
3. **Fallback Support**: Graceful handling of missing logos/colors
4. **Performance**: Logos cached locally for fast access

## Future Enhancements
1. Logo compression for storage optimization
2. Support for different logo formats (SVG, etc.)
3. Custom color schemes beyond single company color
4. Logo positioning options in templates
